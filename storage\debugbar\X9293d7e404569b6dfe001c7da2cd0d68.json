{"__meta": {"id": "X9293d7e404569b6dfe001c7da2cd0d68", "datetime": "2025-08-12 16:40:46", "utime": 1754995246.3219, "method": "GET", "uri": "/admin/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754995245.960295, "end": 1754995246.321927, "duration": 0.3616321086883545, "duration_str": "362ms", "measures": [{"label": "Booting", "start": 1754995245.960295, "relative_start": 0, "end": 1754995246.260705, "relative_end": 1754995246.260705, "duration": 0.30041003227233887, "duration_str": "300ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754995246.260718, "relative_start": 0.30042314529418945, "end": 1754995246.32193, "relative_end": 2.86102294921875e-06, "duration": 0.06121182441711426, "duration_str": "61.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23740728, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin", "middleware": "web, admin", "controller": "App\\Http\\Controllers\\Admin\\DashboardController@dashboard", "as": "admin.dashboard", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=33\" onclick=\"\">app/Http/Controllers/Admin/DashboardController.php:33-82</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/", "status_code": "<pre class=sf-dump id=sf-dump-1191180688 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1191180688\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1320749528 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1320749528\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1742885371 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1742885371\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2131551018 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ijd1R0ozN0I2aElWMFJZMXB3UjhwV2c9PSIsInZhbHVlIjoiVU1sV0dBNTBzRUxneFV0VWVuR2FqdzFXZW51NFFDOWFaSUc0WEVsbnZXQXNLcmFKNFgyYlkzYTFvSEpCWFFuOUZ2WktyRzZYZFloTE9LM1kzOUhQSURNWWU5MmlIU3c5YXh5eDZxMVF2cXJhUXN4dHc2Qm15UWdDd09LKytnL1kiLCJtYWMiOiI4MWFhNDRiMTUwMzU3MWI3ZjJhZjkxOGRhZThmMTE5Y2IxNTkwZjJmMTkzMzQ3ZTJiZDJlYzQwNTcwMDVjMTE1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ikc4aTc1VTE1U0d2Qm5XM3YvMHRFemc9PSIsInZhbHVlIjoiZDlMcEp5RFZDUmFVY3JRSldVQ3ZFcjcrb2F6dTRhbU5jdmVrTmVhTHBzMlYvY3VEZ3VramphZGxKVERETGpCaU1UaitHd1RTeVZad3pSYTZaUWdXNWxDYzAvd1JTdjFrVE12OGEvNnFCTUwrNDNWN1JONTM2TEFpdk15aFY4dHMiLCJtYWMiOiI2NjdlMzc1MzdkYjg1ZTg1NDVhNTc5ZWY4MWIwN2JhNjZjZDcxMzA2M2U4ZDJiODgxZTk2OWM4NDUyYzZiZGE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131551018\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-99552866 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xXBna3nsIuOcOZy2mqUA7gFRJDRGHJs4xurDvGRe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99552866\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-89950619 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 10:40:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkY1UWl0WTdXbkY4R05lN0tDLy82OHc9PSIsInZhbHVlIjoiNWZqTzRxUHY3WmN0RzA3NC83dWR1SXkxRzRXanlYQlhwSFpDQmdhZFpPNGx3d0Q4SzVxN0dkZVcvQ0RURkxtMkdUR2VLUlk3SkJjRGVNaWtCVmVBeElUaGFaNzNCbzZvNkNJbGdNSWRFeVNpemNLZkJqMjFmYk9PRDZjMWt0WlMiLCJtYWMiOiI1ZjZlNWJiYjRiMTZhNGQ3OGMyYmFlNGRkNDdhNjAyY2U5YWVmOWFlY2FmM2VmZTI3M2Q2MTc3N2U3ZWQ2NTViIiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:40:46 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjNRSEgwUko5eWdYTThha2JOTjdBelE9PSIsInZhbHVlIjoiK2NVbDBoeW9EOEZJTXpyM0xndlIxY0lwQi9RSDVFMVczb1N4ZzlIb1Z6Wk40KzJPTkx4bUlsZk95N09PRG05VHQ2NzhLNVl1Q2FoWHdjMTREc1psWlQ4YnFEUzJIRmdRSFFlVlVNdVllU05SRHlHNjJPQmhRbWl4QkxTWVIwSWwiLCJtYWMiOiIwMjdkMWRjOTg2Y2MwNzYwMzE1NDZhNjBjYmFiZjBkMzU1YWQ5MzViYTRjMDMwNmI1MTM5MzhiNzQzNjBkODc1IiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:40:46 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkY1UWl0WTdXbkY4R05lN0tDLy82OHc9PSIsInZhbHVlIjoiNWZqTzRxUHY3WmN0RzA3NC83dWR1SXkxRzRXanlYQlhwSFpDQmdhZFpPNGx3d0Q4SzVxN0dkZVcvQ0RURkxtMkdUR2VLUlk3SkJjRGVNaWtCVmVBeElUaGFaNzNCbzZvNkNJbGdNSWRFeVNpemNLZkJqMjFmYk9PRDZjMWt0WlMiLCJtYWMiOiI1ZjZlNWJiYjRiMTZhNGQ3OGMyYmFlNGRkNDdhNjAyY2U5YWVmOWFlY2FmM2VmZTI3M2Q2MTc3N2U3ZWQ2NTViIiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:40:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjNRSEgwUko5eWdYTThha2JOTjdBelE9PSIsInZhbHVlIjoiK2NVbDBoeW9EOEZJTXpyM0xndlIxY0lwQi9RSDVFMVczb1N4ZzlIb1Z6Wk40KzJPTkx4bUlsZk95N09PRG05VHQ2NzhLNVl1Q2FoWHdjMTREc1psWlQ4YnFEUzJIRmdRSFFlVlVNdVllU05SRHlHNjJPQmhRbWl4QkxTWVIwSWwiLCJtYWMiOiIwMjdkMWRjOTg2Y2MwNzYwMzE1NDZhNjBjYmFiZjBkMzU1YWQ5MzViYTRjMDMwNmI1MTM5MzhiNzQzNjBkODc1IiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:40:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89950619\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1356112470 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356112470\", {\"maxDepth\":0})</script>\n"}}