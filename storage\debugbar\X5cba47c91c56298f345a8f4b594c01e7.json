{"__meta": {"id": "X5cba47c91c56298f345a8f4b594c01e7", "datetime": "2025-08-12 16:41:09", "utime": 1754995269.180868, "method": "POST", "uri": "/admin/auth/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.604525, "end": 1754995269.1809, "duration": 0.5763750076293945, "duration_str": "576ms", "measures": [{"label": "Booting", "start": **********.604525, "relative_start": 0, "end": **********.849658, "relative_end": **********.849658, "duration": 0.2451329231262207, "duration_str": "245ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.849669, "relative_start": 0.24514389038085938, "end": 1754995269.180904, "relative_end": 3.814697265625e-06, "duration": 0.3312349319458008, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24841192, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST admin/auth/login", "middleware": "web, guest:user", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@submit", "as": "admin.auth.", "namespace": "App\\Http\\Controllers\\Admin\\Auth", "prefix": "admin/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=62\" onclick=\"\">app/Http/Controllers/Admin/Auth/LoginController.php:62-100</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.04476, "accumulated_duration_str": "44.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where (`key` = 'recaptcha') limit 1", "type": "query", "params": [], "bindings": ["recaptcha"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/Auth/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Admin\\Auth\\LoginController.php", "line": 69}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.898067, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 0, "width_percent": 1.542}, {"sql": "select * from `users` where `phone` = '***********' and `type` = 0 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["***********", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/Auth/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Admin\\Auth\\LoginController.php", "line": 93}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.907207, "duration": 0.04407, "duration_str": "44.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:139", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "wallet_db", "start_percent": 1.542, "width_percent": 98.458}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/auth/code/captcha/1\"\n]", "_flash": "array:2 [\n  \"old\" => array:2 [\n    0 => \"_old_input\"\n    1 => \"errors\"\n  ]\n  \"new\" => []\n]", "default_captcha_code": "tKYh", "_old_input": "[]", "errors": "Illuminate\\Support\\ViewErrorBag {#1729\n  #bags: array:1 [\n    \"default\" => Illuminate\\Support\\MessageBag {#1728\n      #messages: array:1 [\n        0 => array:1 [\n          0 => \"Credentials does not match.\"\n        ]\n      ]\n      #format: \":message\"\n    }\n  ]\n}", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/auth/login", "status_code": "<pre class=sf-dump id=sf-dump-100361006 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-100361006\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1792559347 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1792559347\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-826402327 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"11 characters\">***********</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>default_captcha_value</span>\" => \"<span class=sf-dump-str title=\"4 characters\">tKYh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-826402327\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1842353699 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">113</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Imc3M0VFaURQUmtteFhiYWFVd1pqOXc9PSIsInZhbHVlIjoiNHp1cHRodUxnb0gvZTBMaXpVSDZ3VnF2YkhzMklvSFNZek5CK0FqVGVnQ0ltOUlLd2VSMzFmaUh2TE1keWJLTFdJZTFUQ2JXR2RKUmZDT1owKzUwKzNmSWhVUkU5Uk9IcTBXYWpwVVRpQVFJQUNFbUFsblJ3Q0RqREh3Q0pRLzEiLCJtYWMiOiJmYWZjMmY1MDUxNDQ5OTE5NDU0NDBjNzY1OTlmZTI1ZjMzZWE0OGRkZTBjODE1ZTEwZTk3YWEyZTY1MTkxZmI4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkNVOWhvYm1Qc0ZvaEh1ZzIzTUpJSFE9PSIsInZhbHVlIjoiYldBVFBoRW1YUlJFY2ppQmN0Y2M0VEk4bExHaHpoNllENmU0aW5hVnRQQ21rbFdqbXdNNUgxTnlQRVFrZVdQYW9sY1RVZ3d0V2Z2WlNDZFV2UkdCaUlRS0JsRVRyd2c3STFjZEkvRkdJZVRuVngxTTI0aGN4U0hDQitxeGh1ZU0iLCJtYWMiOiJlMjU5ZTM2MGQzZjIyYzAwNGE0ZGRiN2M4OWQ5YTViYjI1YTZmNjIxNDViZTU2OTU3NDc1MjNjN2E1ZDI3OGZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842353699\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-772264901 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xXBna3nsIuOcOZy2mqUA7gFRJDRGHJs4xurDvGRe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-772264901\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1457944860 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 10:41:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjA2OEFVVnhrY25JblNJT3ZFRXcxb1E9PSIsInZhbHVlIjoiU0Y1Wlh4elFJaEJRRmdyUVRoNCtwZno0WkNnQVFORlFmKzMrVlZ5MzJZenB6dVQ3Q1A5T0xlQVFxTDg5UG1jNGlYa3dqMVRXbWVGMVpUZmRUWVVod3RLWkdUL3cvYWVRZHRxODdDdUl3NmpBNHZVdFEyVC91ZW4wdG40RUtyeDEiLCJtYWMiOiJmZmUzZDI4ZmU5ZGU3OGVmMjQyZTljMWU4NWI3NmMzNzc1NWQ4ODhjM2IxNzcyNmFjZGQyZmFhZDRlYjY3OTU1IiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:41:09 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Imxnck9ncmx4RTJOb1dreUhsYlZwRGc9PSIsInZhbHVlIjoieGVyVU1pYUJZYjhQanJFUlpQSHljbTBjY2ZWRjJHbmE5SXBRcU9KZGlaa0Nxa1dQQUcxQXZTNmxMNG5LakhUZm0zTjVkdkdGVld0UnlDNGd4VFoyNGZEazlYSCtVMnpNS3NPWVJaTHhQTVNWSFU4dEFjNjVjRlZuT3o4NTBBVm8iLCJtYWMiOiI4MzRiM2RkMTg0OTA3YzYyZTYxNjRmOTViMTRlNTlhMjljNDE5MjMxZDE0MDdiZGNjYjY1M2YxYzA3NTI2ZDNlIiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:41:09 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjA2OEFVVnhrY25JblNJT3ZFRXcxb1E9PSIsInZhbHVlIjoiU0Y1Wlh4elFJaEJRRmdyUVRoNCtwZno0WkNnQVFORlFmKzMrVlZ5MzJZenB6dVQ3Q1A5T0xlQVFxTDg5UG1jNGlYa3dqMVRXbWVGMVpUZmRUWVVod3RLWkdUL3cvYWVRZHRxODdDdUl3NmpBNHZVdFEyVC91ZW4wdG40RUtyeDEiLCJtYWMiOiJmZmUzZDI4ZmU5ZGU3OGVmMjQyZTljMWU4NWI3NmMzNzc1NWQ4ODhjM2IxNzcyNmFjZGQyZmFhZDRlYjY3OTU1IiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:41:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Imxnck9ncmx4RTJOb1dreUhsYlZwRGc9PSIsInZhbHVlIjoieGVyVU1pYUJZYjhQanJFUlpQSHljbTBjY2ZWRjJHbmE5SXBRcU9KZGlaa0Nxa1dQQUcxQXZTNmxMNG5LakhUZm0zTjVkdkdGVld0UnlDNGd4VFoyNGZEazlYSCtVMnpNS3NPWVJaTHhQTVNWSFU4dEFjNjVjRlZuT3o4NTBBVm8iLCJtYWMiOiI4MzRiM2RkMTg0OTA3YzYyZTYxNjRmOTViMTRlNTlhMjljNDE5MjMxZDE0MDdiZGNjYjY1M2YxYzA3NTI2ZDNlIiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:41:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457944860\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1141290692 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/admin/auth/code/captcha/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">_old_input</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">errors</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>default_captcha_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">tKYh</span>\"\n  \"<span class=sf-dump-key>_old_input</span>\" => []\n  \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ViewErrorBag</span> {<a class=sf-dump-ref>#1729</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">bags</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\MessageBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>MessageBag</span> {<a class=sf-dump-ref>#1728</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">messages</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">Credentials does not match.</span>\"\n          </samp>]\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"\n      </samp>}\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141290692\", {\"maxDepth\":0})</script>\n"}}