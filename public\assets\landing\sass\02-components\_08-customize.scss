/* ************************
02.8: Customize Bootstrap CSS
********************* */

// .dropdown {
//     &-menu {
//         --bs-dropdown-border-width: 0;
//         --bs-dropdown-bg: var(--bs-white);
//         --bs-dropdown-min-width: 15rem;
//         // --bs-dropdown-font-size: 0.75rem;
//         --bs-dropdown-font-size: inherit;
//         --bs-dropdown-color: var(--title-color);
//         // @extend %box-shadow;
//         box-shadow: var(--box-shadow);
//         text-align: start;
//         li {
//             padding: toRem(10) toRem(20);
//             position: relative;
//             font-weight: var(--meidum);
//             &.menu-item-has-children {
//                 &:after {
//                     position: absolute;
//                     font-family: bootstrap-icons !important;
//                     content: "\F285";
//                     inset-inline-end: toRem(16);
//                     // color: var(--title-color);
//                     font-size: toRem(12);
//                     [dir="rtl"] & {
//                         transform: rotate(180deg);
//                     }
//                 }
//             }
//             &:not(:last-child) {
//                 border-bottom: 1px solid var(--bs-border-color);
//             }
//             ul {
//                 position: absolute;
//                 inset-block-start: 0;
//                 background-color: var(--bs-white);
//                 inset-inline-start: 100%;
//                 list-style: none;
//                 inline-size: toRem(240);
//                 padding-inline-start: 0;
//                 transform: translate3d(30px, 0, 0);
//                 transition: transform 0.3s ease-out;
//                 opacity: 0;
//                 visibility: hidden;
//             }
//             &:hover {
//                 // >a {
//                 //     padding-inline-start: toRem(4);
//                 // }
//                 > ul {
//                     transform: translate3d(0, 0, 0);
//                     opacity: 1;
//                     visibility: visible;
//                 }
//             }
//         }

//         &--static {
//             display: block !important;
//             position: static;
//             @extend %shadow-0;
//             > li {
//                 padding-inline-start: toRem(0);
//                 z-index: 2;
//             }
//         }
//     }
// }

// .nav {
//     &--tabs {
//         button, a {
//             border: 0;
//             background-color: transparent;
//             padding: toRem(0);
//             padding-block-end: toRem(6);
//             font-weight: var(--medium);
//             position: relative;
//             text-transform: capitalize;
//             &::after {
//                 @extend %beforeAfter;
//                 block-size: toRem(2);
//                 background-color: var(--bs-primary);
//                 inset-block-start: auto;
//                 inset-block-end: toRem(0);
//                 transform: scaleX(0);
//                 transform-origin: 0 0;
//                 @extend %trans3;
//             }
//             &.active {
//                 color: var(--bs-primary);
//                 &::after {
//                     transform: scaleX(1);
//                 }
//             }
//         }
//     }
// }

// .compare--table {
//     th, td {
//         min-inline-size: toRem(160);
//     }
//     tbody {
//         tr {
//             border-top-width: 0;
//             &:not(:last-child) {
//                 border-bottom-width: 0;
//             }
//         }
//         td {
//             text-align: center;
//         }
//     }
// }

// /* Swiper */
.swiper {
//     inline-size: 100%;
//     block-size: 100%;
//     .product {
//         inline-size: 100%;
//     }
    &-container {
        overflow: hidden;
    }
    &-pagination {
        &-bullet {
            --swiper-pagination-bullet-width: .5rem;
            --swiper-pagination-bullet-height: .25rem;
            border-radius: 50rem;
            &-active {
                --swiper-pagination-bullet-width: 1rem;
            }
        }
    }

//     &-button-next,
//     &-button-prev {
//         --size: 2.1875rem;
//         inline-size: var(--size);
//         block-size: var(--size);
//         background-color: var(--bs-white);
//         box-shadow: var(--box-shadow);
//         color: var(--title-color);
//         margin-block-start: 0;
//         padding: toRem(5);
//         @extend %rounded;
//         @extend %grid-center;
//         position: absolute;
//         z-index: 2;
//         @extend %trans3;
//         inset-block-start: 50%;
//         &:not(.position-static) {
//             transform: translateY(-50%);
//         }
//         &:after {
//             font-size: calc(var(--size) / 2.5);
//         }
//         &:hover {
//             background-color: var(--bs-primary);
//             color: var(--bs-white);
//         }
//     }
//     &-button-next {
//         inset-inline-end: toRem(0);
//         inset-inline-start: auto;
//     }
//     &-button-prev {
//         inset-inline-start: toRem(0);
//         inset-inline-end: auto;
//     }
}


// // .quickviewSliderThumb {
// //     .swiper-button-next,
// //     .swiper-button-prev {
// //         transform: scale(.3) translateY(-50%);
// //         opacity: 0;
// //     }

// //     &:hover {
// //         .swiper-button-next,
// //         .swiper-button-prev {
// //             transform: scale(1) translateY(-50%);
// //             opacity: 1;
// //         }
// //     }
// // }

// .modal {
//     --bs-modal-bg: var(--bs-white);
// }

// .table {
//     --bs-table-color: var(--title-color);
//     &-light {
//         --bs-table-color: var(--title-color);
//         --bs-table-bg: var(--bs-light);
//     }
//     thead {
//         button {
//             @extend %trans2;
//             &.collapsed {
//                 transform: rotate(180deg) !important;
//             }
//         }
//     }
// }
// .table>:not(caption)>*>* {
//     padding: toRem(14) toRem(14);
// }

// .progress {
//     --bs-progress-height: 0.5rem;
//     --bs-progress-bg: var(--bs-light);
//     --bs-progress-bar-bg: var(--bs-primary);
// }

// .list-unstyled {
//     padding-inline-start: 0 !important;
//     margin-block-end: 0 !important;
// }

// .text-start {
//     text-align: start !important;
// }

// .text-end {
//     text-align: end !important;
// }

// .breadcrumb {
//     --bs-breadcrumb-divider-color: var(--title-color);
//     --bs-breadcrumb-item-active-color: var(--title-color);
//     --bs-breadcrumb-divider: ">";
// }

// .breadcrumb-item.active {
//     font-weight: var(--semi-bold);
// }

// .accordion {
//     --bs-accordion-color: var(--title-color);
//     --bs-accordion-bg: var(--bs-white);
//     --bs-accordion-btn-focus-border-color: #86b7fe;
//     --bs-accordion-active-color: var(--bs-primary);
//     --bs-accordion-active-bg: var(--bs-white);
// }

// /**
//  * EasyZoom core styles
//  */
// .easyzoom {
// 	position: relative;
// 	display: inline-block;
// }

// .easyzoom img {
// 	vertical-align: bottom;
// }

// .easyzoom.is-loading img {
// 	cursor: progress;
// }

// .easyzoom.is-ready img {
// 	cursor: crosshair;
// }

// .easyzoom.is-error  img {
// 	cursor: not-allowed;
// }

// .easyzoom-notice {
// 	position: absolute;
// 	inset-inline-start: 50%;
// 	inset-inline-start: 50%;
// 	z-index: 150;
// 	inline-size: 10em;
// 	margin: -1em 0 0 -5em;
// 	line-height: 2em;
// 	text-align: center;
// 	background-color: var(--bs-white);
// 	box-shadow: var(--box-shadow);
// }

// .easyzoom-flyout {
// 	position:absolute;
// 	z-index: 100;
// 	overflow: hidden;
// 	background-color: var(--bs-white);
//     direction: ltr !important;
//     text-align: start !important;
// }

// .easyzoom--overlay .easyzoom-flyout {
//     inset-block-start: 0;
//     inset-inline-start: 0;
//     inline-size: 100%;
//     block-size: 100%;
// }

// .easyzoom--overlay .easyzoom-flyout img {
//     max-inline-size: unset !important;
// }