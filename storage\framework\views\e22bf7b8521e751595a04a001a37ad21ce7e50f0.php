
<div id="sidebarMain" class="d-none">
    <aside
        class="js-navbar-vertical-aside navbar navbar-vertical-aside navbar-vertical navbar-vertical-fixed navbar-expand-xl navbar-bordered  ">
        <div class="navbar-vertical-container">
            <div class="navbar-brand-wrapper justify-content-between">
                <?php ($restaurantLogo=\App\CentralLogics\helpers::get_business_settings('logo')); ?>
                <a class="navbar-brand" href="<?php echo e(route('admin.dashboard')); ?>" aria-label="Front">
                    <img class="w-100 side-logo"
                         src="<?php echo e(Helpers::onErrorImage($restaurantLogo, asset('storage/app/public/business') . '/' . $restaurantLogo, asset('public/assets/admin/img/1920x400/img2.jpg'), 'business/')); ?>"
                         alt="<?php echo e(translate('Logo')); ?>">
                </a>
                <div class="navbar-nav-wrap-content-left">
                    <button type="button" class="js-navbar-vertical-aside-toggle-invoker close mr-">
                        <i class="tio-first-page navbar-vertical-aside-toggle-short-align"></i>
                        <i class="tio-last-page navbar-vertical-aside-toggle-full-align"></i>
                    </button>
                </div>
            </div>

            <div class="navbar-vertical-content">
                <form class="sidebar--search-form">
                    <div class="search--form-group">
                        <button type="button" class="btn"><i class="tio-search"></i></button>
                        <input type="text" class="form-control form--control" placeholder="Search Menu..." id="search-sidebar-menu">
                    </div>
                </form>

                <ul class="navbar-nav navbar-nav-lg nav-tabs">
                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin')?'active':''); ?>">
                        <a class="js-navbar-vertical-aside-menu-link nav-link"
                           href="<?php echo e(route('admin.dashboard')); ?>" title="<?php echo e(translate('dashboard')); ?>">
                            <i class="tio-home-vs-1-outlined nav-icon"></i>
                            <span class="navbar-vertical-aside-mini-mode-hidden-elements text-truncate">
                                <?php echo e(translate('dashboard')); ?>

                            </span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <small class="nav-subtitle"
                               title="<?php echo e(translate('account')); ?> <?php echo e(translate('section')); ?>"><?php echo e(translate('account')); ?> <?php echo e(translate('management')); ?></small>
                        <small class="tio-more-horizontal nav-subtitle-replacer"></small>
                    </li>
                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/emoney*')?'active':''); ?>">
                        <a class="nav-link " href="<?php echo e(route('admin.emoney.index')); ?>"
                           title="<?php echo e(translate('EMoney')); ?>">
                            <i class="tio-money nav-icon"></i>
                            <span class="text-truncate"><?php echo e(translate('E-Money')); ?></span>
                        </a>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/transfer*')?'active':''); ?>">
                        <a class="nav-link " href="<?php echo e(route('admin.transfer.index')); ?>"
                           title="<?php echo e(translate('transfer')); ?>">
                            <i class="tio-users-switch nav-icon"></i>
                            <span class="text-truncate"><?php echo e(translate('Transfer')); ?></span>
                        </a>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/transaction/index')?'active':''); ?>">
                        <a class="nav-link " href="<?php echo e(route('admin.transaction.index', ['trx_type'=>'all'])); ?>"
                           title="<?php echo e(translate('transaction')); ?>">
                            <i class="tio-money-vs nav-icon"></i>
                            <span class="text-truncate"><?php echo e(translate('Transactions')); ?></span>
                        </a>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/expense/index')?'active':''); ?>">
                        <a class="nav-link " href="<?php echo e(route('admin.expense.index')); ?>"
                           title="<?php echo e(translate('Expense Transactions')); ?>">
                            <i class="tio-receipt-outlined nav-icon"></i>
                            <span class="text-truncate"><?php echo e(translate('Expense Transactions')); ?></span>
                        </a>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/transaction/request-money')?'active':''); ?>">
                        <a class="nav-link " href="<?php echo e(route('admin.transaction.request_money')); ?>"
                           title="<?php echo e(translate('Agent Request Money')); ?>">
                            <i class="tio-pound nav-icon"></i>
                            <span class="text-truncate"><?php echo e(translate('Agent Request Money')); ?></span>
                        </a>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/withdraw/requests')?'active':''); ?>">
                        <a class="nav-link " href="<?php echo e(route('admin.withdraw.requests', ['request_status'=>'all'])); ?>"
                           title="<?php echo e(translate('Agent Request Money')); ?>">
                            <i class="tio-pound-outlined nav-icon"></i>
                            <span class="text-truncate"><?php echo e(translate('Withdraw_Requests')); ?></span>
                        </a>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/withdrawal-methods*')?'active':''); ?>">
                        <a class="js-navbar-vertical-aside-menu-link nav-link"
                           href="<?php echo e(route('admin.withdrawal_methods.add')); ?>">
                            <i class="tio-sim-card nav-icon"></i>
                            <span class="navbar-vertical-aside-mini-mode-hidden-elements text-truncate">
                                <?php echo e(translate('Add Withdrawal Methods')); ?>

                            </span>
                        </a>
                    </li>


                    <li class="nav-item">
                        <small class="nav-subtitle"
                               title="<?php echo e(translate('user')); ?> <?php echo e(translate('section')); ?>"><?php echo e(translate('user')); ?> <?php echo e(translate('management')); ?></small>
                        <small class="tio-more-horizontal nav-subtitle-replacer"></small>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/agent*')?'active':''); ?>">
                        <a class="js-navbar-vertical-aside-menu-link nav-link nav-link-toggle" href="javascript:"
                        >
                            <i class="tio-user-big-outlined nav-icon"></i>
                            <span
                                class="navbar-vertical-aside-mini-mode-hidden-elements text-truncate"><?php echo e(translate('agent')); ?></span>
                        </a>
                        <ul class="js-navbar-vertical-aside-submenu nav nav-sub" style="display: <?php echo e(Request::is('admin/agent*')?'block':'none'); ?>">
                            <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/agent/add')?'active':''); ?>">
                                <a class="nav-link " href="<?php echo e(route('admin.agent.add')); ?>"
                                   title="<?php echo e(translate('add')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('register')); ?></span>
                                </a>
                            </li>
                            <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/agent/list')?'active':''); ?>">
                                <a class="nav-link " href="<?php echo e(route('admin.agent.list')); ?>"
                                   title="<?php echo e(translate('list')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('list')); ?></span>
                                </a>
                            </li>
                            <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/agent/kyc-requests')?'active':''); ?>">
                                <a class="nav-link " href="<?php echo e(route('admin.agent.kyc_requests')); ?>"
                                   title="<?php echo e(translate('Verification Requests')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('Verification Requests')); ?></span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/merchant/add') || Request::is('admin/merchant/list') || Request::is('admin/merchant/view*') || Request::is('admin/merchant/edit*') || Request::is('admin/merchant/transaction*')  ?'active':''); ?>">
                        <a class="js-navbar-vertical-aside-menu-link nav-link nav-link-toggle" href="javascript:"
                        >
                            <i class="tio-user-big nav-icon"></i>
                            <span
                                class="navbar-vertical-aside-mini-mode-hidden-elements text-truncate"><?php echo e(translate('merchant')); ?></span>
                        </a>
                        <ul class="js-navbar-vertical-aside-submenu nav nav-sub" style="display: <?php echo e(Request::is('admin/merchant/add') || Request::is('admin/merchant/list') || Request::is('admin/merchant/view*') || Request::is('admin/merchant/edit*') || Request::is('admin/merchant/transaction*') ?'block':'none'); ?>">
                            <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/merchant/add')?'active':''); ?>">
                                <a class="nav-link " href="<?php echo e(route('admin.merchant.add')); ?>"
                                   title="<?php echo e(translate('add')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('register')); ?></span>
                                </a>
                            </li>
                            <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/merchant/list')?'active':''); ?>">
                                <a class="nav-link " href="<?php echo e(route('admin.merchant.list')); ?>"
                                   title="<?php echo e(translate('list')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('list')); ?></span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/customer*')?'active':''); ?>">
                        <a class="js-navbar-vertical-aside-menu-link nav-link nav-link-toggle" href="javascript:"
                        >
                            <i class="tio-group-senior nav-icon"></i>
                            <span
                                class="navbar-vertical-aside-mini-mode-hidden-elements text-truncate"><?php echo e(translate('customer')); ?></span>
                        </a>
                        <ul class="js-navbar-vertical-aside-submenu nav nav-sub" style="display: <?php echo e(Request::is('admin/customer*')?'block':'none'); ?>">
                            <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/customer/add')?'active':''); ?>">
                                <a class="nav-link " href="<?php echo e(route('admin.customer.add')); ?>"
                                   title="<?php echo e(translate('add')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('register')); ?></span>
                                </a>
                            </li>
                            <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/customer/list')?'active':''); ?>">
                                <a class="nav-link " href="<?php echo e(route('admin.customer.list')); ?>"
                                   title="<?php echo e(translate('list')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('list')); ?></span>
                                </a>
                            </li>
                            <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/customer/kyc-requests')?'active':''); ?>">
                                <a class="nav-link " href="<?php echo e(route('admin.customer.kyc_requests')); ?>"
                                   title="<?php echo e(translate('Verification Requests')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('Verification Requests')); ?></span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item <?php echo e(Request::is('admin/user/log')?'active':''); ?>">
                        <a class="nav-link" href="<?php echo e(route('admin.user.log')); ?>">
                            <span class="tio-user-big nav-icon"></span>
                            <span class="text-truncate"><?php echo e(translate('Users Log')); ?></span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <small class="nav-subtitle"
                               title="<?php echo e(translate('Business')); ?> <?php echo e(translate('section')); ?>"><?php echo e(translate('business')); ?> <?php echo e(translate('management')); ?></small>
                        <small class="tio-more-horizontal nav-subtitle-replacer"></small>
                    </li>

                    <li class="nav-item <?php echo e(Request::is('admin/banner*')?'active':''); ?>">
                        <a class="nav-link "
                           href="<?php echo e(route('admin.banner.index')); ?>">
                            <span class="tio-image nav-icon"></span>
                            <span class="text-truncate"><?php echo e(translate('Banner')); ?></span>
                        </a>
                    </li>

                    <li class="nav-item <?php echo e(Request::is('admin/helpTopic/list')?'active':''); ?>">
                        <a class="nav-link" href="<?php echo e(route('admin.helpTopic.list')); ?>">
                            <span class="tio-bookmark-outlined nav-icon"></span>
                            <span class="text-truncate"><?php echo e(translate('faq')); ?></span>
                        </a>
                    </li>

                    <li class="nav-item <?php echo e(Request::is('admin/purpose*')?'active':''); ?>">
                        <a class="nav-link "
                           href="<?php echo e(route('admin.purpose.index')); ?>">
                            <span class="tio-add-square-outlined nav-icon"></span>
                            <span class="text-truncate"><?php echo e(translate('Purpose')); ?></span>
                        </a>
                    </li>

                    <li class="nav-item <?php echo e(Request::is('admin/linked-website')?'active':''); ?>">
                        <a class="nav-link "
                           href="<?php echo e(route('admin.linked-website')); ?>">
                            <span class="tio-website nav-icon"></span>
                            <span class="text-truncate"><?php echo e(translate('Linked Website')); ?></span>
                        </a>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/notification*')?'active':''); ?>">
                        <a class="js-navbar-vertical-aside-menu-link nav-link"
                           href="<?php echo e(route('admin.notification.add-new')); ?>"
                        >
                            <i class="tio-notifications nav-icon"></i>
                            <span class="navbar-vertical-aside-mini-mode-hidden-elements text-truncate">
                                    <?php echo e(translate('send')); ?> <?php echo e(translate('notification')); ?>

                                </span>
                        </a>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/bonus*')?'active':''); ?>">
                        <a class="nav-link " href="<?php echo e(route('admin.bonus.index')); ?>" title="<?php echo e(translate('Bonus')); ?>">
                            <span class="tio-money nav-icon"></span>
                            <span class="text-truncate"><?php echo e(translate('Add Money Bonus')); ?></span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <small class="nav-subtitle"
                               title="<?php echo e(translate('system')); ?> <?php echo e(translate('section')); ?>"><?php echo e(translate('system')); ?> <?php echo e(translate('management')); ?></small>
                        <small class="tio-more-horizontal nav-subtitle-replacer"></small>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/business-settings/*')?'active':''); ?>">
                        <a class="nav-link " href="<?php echo e(route('admin.business-settings.business-setup')); ?>"
                            title="<?php echo e(translate('business')); ?> <?php echo e(translate('setup')); ?>">
                            <span class="tio-settings nav-icon"></span>
                            <span class="text-truncate"><?php echo e(translate('business setup')); ?></span>
                        </a>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/landing-settings/get-landing-information*')?'active':''); ?>">
                        <a class="nav-link " href="<?php echo e(route('admin.landing-settings.get-landing-information', ['web_page' => 'intro_section'])); ?>"
                            title="<?php echo e(translate('landing')); ?> <?php echo e(translate('setup')); ?>">
                            <span class="tio-settings-vs-outlined nav-icon"></span>
                            <span class="text-truncate"><?php echo e(translate('Landing_Page_Settings')); ?></span>
                        </a>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/business-settings*')?'active':''); ?>">
                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/pages*')?'active':''); ?>">
                        <a class="js-navbar-vertical-aside-menu-link nav-link nav-link-toggle" href="javascript:">
                            <i class="tio-pages-outlined nav-icon"></i>
                            <span class="navbar-vertical-aside-mini-mode-hidden-elements text-truncate"><?php echo e(translate('Pages & Media')); ?></span>
                        </a>
                        <ul class="js-navbar-vertical-aside-submenu nav nav-sub" style="display: <?php echo e(Request::is('admin/pages*')?'block':'none'); ?>">
                            <li class="nav-item <?php echo e(Request::is('admin/pages/terms-and-conditions')?'active':''); ?>">
                                <a class="nav-link "
                                   href="<?php echo e(route('admin.pages.terms-and-conditions')); ?>"
                                >
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('terms & Condition')); ?></span>
                                </a>
                            </li>

                            <li class="nav-item <?php echo e(Request::is('admin/pages/privacy-policy')?'active':''); ?>">
                                <a class="nav-link " href="<?php echo e(route('admin.pages.privacy-policy')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('privacy Policy')); ?></span>
                                </a>
                            </li>

                            <li class="nav-item <?php echo e(Request::is('admin/pages/about-us')?'active':''); ?>">
                                <a class="nav-link "
                                   href="<?php echo e(route('admin.pages.about-us')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('about Us')); ?></span>
                                </a>
                            </li>

                            <li class="nav-item <?php echo e(Request::is('admin/pages/social-media')?'active':''); ?>">
                                <a class="nav-link "
                                   href="<?php echo e(route('admin.pages.social-media.index')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('Social Media Links')); ?></span>
                                </a>
                            </li>

                        </ul>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/merchant-config*')?'active':''); ?>">
                        <a class="js-navbar-vertical-aside-menu-link nav-link nav-link-toggle" href="javascript:"
                        >
                            <i class="tio-settings-outlined nav-icon"></i>
                            <span
                                class="navbar-vertical-aside-mini-mode-hidden-elements text-truncate"><?php echo e(translate('Merchant Config')); ?></span>
                        </a>
                        <ul class="js-navbar-vertical-aside-submenu nav nav-sub" style="display: <?php echo e(Request::is('admin/merchant-config*')?'block':'none'); ?>">
                            <li class="nav-item <?php echo e(Request::is('admin/merchant-config/merchant-payment-otp')?'active':''); ?>">
                                <a class="nav-link "
                                   href="<?php echo e(route('admin.merchant-config.merchant-payment-otp')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('Merchant OTP')); ?></span>
                                </a>
                            </li>
                            <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/merchant-config/settings')?'active':''); ?>">
                                <a class="nav-link " href="<?php echo e(route('admin.merchant-config.settings')); ?>"
                                   title="<?php echo e(translate('settings')); ?>">
                                    <span class="tio-circle nav-indicator-icon"></span>
                                    <span class="text-truncate"><?php echo e(translate('settings')); ?></span>
                                </a>
                            </li>

                        </ul>
                    </li>
                    <li class="nav-item">
                        <small class="nav-subtitle"><?php echo e(translate('Help_&_Support')); ?></small>
                            <small class="tio-more-horizontal nav-subtitle-replacer"></small>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/contact/*') ? 'active' : ''); ?>">
                        <a class="js-navbar-vertical-aside-menu-link nav-link"
                            href="<?php echo e(route('admin.contact.list')); ?>"
                            title="<?php echo e(translate('Contact_messages')); ?>">
                            <i class="tio-messages nav-icon"></i>
                            <span class="navbar-vertical-aside-mini-mode-hidden-elements text-truncate">
                                <?php echo e(translate('Contact_messages')); ?>

                            </span>
                        </a>
                    </li>

                      <li class="nav-item">
                        <small class="nav-subtitle"><?php echo e(translate('system')); ?> <?php echo e(translate('addon')); ?></small>
                        <small class="tio-more-horizontal nav-subtitle-replacer"></small>
                    </li>

                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/addon')?'active':''); ?>">
                    <a class="nav-link " href="<?php echo e(route('admin.addon.index')); ?>" title="<?php echo e(translate('system_Addon')); ?>">
                        <span class="tio-add-circle-outlined nav-icon"></span>
                        <span class="text-truncate"><?php echo e(translate('System Addon')); ?></span>
                    </a>
                </li>

                    <?php if(count(config('addon_admin_routes'))>0): ?>
                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is('admin/payment/configuration/*') || Request::is('admin/sms/configuration/*')?'active':''); ?> mb-5">
                        <a class="js-navbar-vertical-aside-menu-link nav-link nav-link-toggle" href="javascript:" >
                            <i class="tio-puzzle nav-icon"></i>
                            <span  class="navbar-vertical-aside-mini-mode-hidden-elements text-truncate"><?php echo e(translate('Addon Menus')); ?></span>
                        </a>
                        <ul class="js-navbar-vertical-aside-submenu nav nav-sub" style="display: <?php echo e(Request::is('admin/payment/configuration/*') || Request::is('admin/sms/configuration/*')?'block':'none'); ?>">
                            <?php $__currentLoopData = config('addon_admin_routes'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $routes): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php $__currentLoopData = $routes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $route): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="navbar-vertical-aside-has-menu <?php echo e(Request::is($route['path'])  ? 'active' :''); ?>">
                                        <a class="js-navbar-vertical-aside-menu-link nav-link "
                                        href="<?php echo e($route['url']); ?>" title="<?php echo e(translate($route['name'])); ?>">
                                            <span class="tio-circle nav-indicator-icon"></span>
                                            <span class="text-truncate"><?php echo e(translate($route['name'])); ?></span>
                                        </a>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </li>
                <?php endif; ?>

                </ul>
            </div>
        </div>
    </aside>
</div>

<div id="sidebarCompact" class="d-none">

</div>

<?php $__env->startPush('script_2'); ?>
<script>
    $(window).on('load' , function() {
        if($(".navbar-vertical-content li.active").length) {
            $('.navbar-vertical-content').animate({
                scrollTop: $(".navbar-vertical-content li.active").offset().top - 150
            }, 10);
        }
    });

    var $rows = $('.navbar-vertical-content  .navbar-nav > li');
    $('#search-sidebar-menu').keyup(function() {
        var val = $.trim($(this).val()).replace(/ +/g, ' ').toLowerCase();

        $rows.show().filter(function() {
            var text = $(this).text().replace(/\s+/g, ' ').toLowerCase();
            return !~text.indexOf(val);
        }).hide();
    });
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\laragon\www\arefan_wallet_admin\resources\views/layouts/admin/partials/_sidebar.blade.php ENDPATH**/ ?>