@if ($data['intro_section']['status'] == '1')
<div class="banner overflow-hidden" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 text-center text-lg-start">
                <h1 class="banner-title text-white mb-4">{!! change_text_color_or_bg($data['intro_section']['data']['title']) !!}</h1>
                <p class="fs-18 text-white mb-5">{!! $data['intro_section']['data']['description'] !!}</p>
                <div class="d-flex justify-content-center justify-content-lg-start gap-3">
                    <a href="{{$data['intro_section']['data']['download_link']}}" class="btn btn-lg btn-light text-primary fw-bold">{{$data['intro_section']['data']['button_name'] ?? translate('Download App')}}</a>
                    <a href="#feature-section" class="btn btn-lg btn-outline-light fw-bold">{{translate('Learn More')}}</a>
                </div>
            </div>
            <div class="col-lg-6 mt-5 mt-lg-0">
                <div class="banner-mobile-frame text-center">
                    <img width="350" src="{{$imageSource['intro_middle_image']}}" class="img-fluid" alt="{{translate('app screenshot')}}">
                </div>
            </div>
        </div>

        <div class="row mt-5 pt-5">
            <div class="col-12">
                <div class="d-flex flex-wrap justify-content-around align-items-center gap-4 text-white">
                    <div class="text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <img width="40" src="{{asset('storage/app/public/landing-page/intro-section/'.$data['intro_section']['rating_and_user_data']['review_user_icon'])}}" class="rounded-circle me-2" alt="{{translate('reviewer')}}">
                            <span class="fw-bold">{{$data['intro_section']['rating_and_user_data']['reviewer_name']}}</span>
                        </div>
                        <div class="star-rating text-warning">
                            @php $rating = $data['intro_section']['rating_and_user_data']['rating']; @endphp
                            @for ($i = 1; $i <= 5; $i++)
                                <i class="bi {{ $i <= $rating ? 'bi-star-fill' : ($i - 0.5 <= $rating ? 'bi-star-half' : 'bi-star') }}"></i>
                            @endfor
                        </div>
                    </div>

                    <div class="text-center">
                        <h3 class="text-white mb-1">{{format_number($data['intro_section']['rating_and_user_data']['total_user_count'])}}+</h3>
                        <h6 class="fs-14 text-white op-8">{{$data['intro_section']['rating_and_user_data']['total_user_content']}}</h6>
                    </div>

                    <div class="d-flex justify-content-center">
                        <img width="48" src="{{$imageSource['user_image_one']}}" class="rounded-circle border border-2 border-light" alt="{{translate('user')}}">
                        <img width="48" src="{{$imageSource['user_image_two']}}" class="rounded-circle border border-2 border-light ms-n3" alt="{{translate('user')}}">
                        <img width="48" src="{{$imageSource['user_image_three']}}" class="rounded-circle border border-2 border-light ms-n3" alt="{{translate('user')}}">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

