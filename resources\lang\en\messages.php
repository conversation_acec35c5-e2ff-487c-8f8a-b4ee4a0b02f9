<?php return array (
  'software_version' => 'Software version',
  'signin' => 'Signin',
  'admin_or_employee_signin' => 'Admin or employee signin',
  'want' => 'Want',
  'to' => 'To',
  'login' => 'Login',
  'your' => 'Your',
  'vendors' => 'Vendors',
  'vendor' => 'Vendor',
  'email' => 'Email',
  'password' => 'Password',
  'remember' => 'Remember',
  'me' => 'Me',
  'sign_in' => 'Sign in',
  'remember me' => 'Remember me',
  'your phone' => 'Your phone',
  'settings' => 'Settings',
  'sign_out' => 'Sign out',
  'dashboard' => 'Dashboard',
  'restaurant' => 'Restaurant',
  'section' => 'Section',
  'management' => 'Management',
  'business' => 'Business',
  'setup' => 'Setup',
  'profile' => 'Profile',
  'card_holder_name' => 'Card holder name',
  'card_holder_email' => 'Card holder email',
  'card_number' => 'Card number',
  'card_expire_month' => 'Card expire month',
  'card_expire_year' => 'Card expire year',
  'security_code' => 'Security code',
  'dues' => 'Dues',
  'document_type' => 'Document type',
  'document_number' => 'Document number',
  'issuing_bank' => 'Issuing bank',
  'amount_to_be_paid' => 'Amount to be paid',
  'country_permission_denied_or_misconfiguration' => 'Country permission denied or misconfiguration',
  'sms' => 'Sms',
  'module' => 'Module',
  'payment' => 'Payment',
  'methods' => 'Methods',
  'gateway' => 'Gateway',
  'twilio_sms' => 'Twilio sms',
  'active' => 'Active',
  'inactive' => 'Inactive',
  'sid' => 'Sid',
  'messaging_service_sid' => 'Messaging service sid',
  'token' => 'Token',
  'from' => 'From',
  'otp_template' => 'Otp template',
  'save' => 'Save',
  'nexmo_sms' => 'Nexmo sms',
  'api_key' => 'Api key',
  'api_secret' => 'Api secret',
  '2factor_sms' => '2factor sms',
  'msg91_sms' => 'Msg91 sms',
  'template_id' => 'Template id',
  'authkey' => 'Authkey',
  'method' => 'Method',
  'configure' => 'Configure',
  'sslcommerz' => 'Sslcommerz',
  'store' => 'Store',
  'id' => 'Id',
  'razorpay' => 'Razorpay',
  'razorkey' => 'Razorkey',
  'razorsecret' => 'Razorsecret',
  'paypal' => 'Paypal',
  'client' => 'Client',
  'secret' => 'Secret',
  'stripe' => 'Stripe',
  'published' => 'Published',
  'key' => 'Key',
  'api' => 'Api',
  'paystack' => 'Paystack',
  'senang' => 'Senang',
  'pay' => 'Pay',
  'wallet' => 'Wallet',
  'point' => 'Point',
  'bkash' => 'Bkash',
  'username' => 'Username',
  'paymob' => 'Paymob',
  'iframe_id' => 'Iframe id',
  'integration_id' => 'Integration id',
  'hmac' => 'Hmac',
  'flutterwave' => 'Flutterwave',
  'public_key' => 'Public key',
  'secret_key' => 'Secret key',
  'hash' => 'Hash',
  'mercadopago' => 'Mercadopago',
  'access_token' => 'Access token',
  'merchant' => 'Merchant',
  'publicKey' => 'PublicKey',
  'secretKey' => 'SecretKey',
  'paymentUrl' => 'PaymentUrl',
  'merchantEmail' => 'MerchantEmail',
  'Business' => 'Business',
  'maintenance_mode' => 'Maintenance mode',
  'name' => 'Name',
  'currency' => 'Currency',
  'currency_symbol_positon' => 'Currency symbol positon',
  'country' => 'Country',
  'time' => 'Time',
  'zone' => 'Zone',
  'scheduled' => 'Scheduled',
  'orders' => 'Orders',
  'customer_varification_toggle' => 'Customer varification toggle',
  'on' => 'On',
  'off' => 'Off',
  'order_confirmation_model' => 'Order confirmation model',
  'order_confirmation_model_hint' => 'Order confirmation model hint',
  'deliveryman' => 'Deliveryman',
  'admin' => 'Admin',
  'order' => 'Order',
  'notification' => 'Notification',
  'customer' => 'Customer',
  'verification' => 'Verification',
  'delivery' => 'Delivery',
  'order_varification_toggle' => 'Order varification toggle',
  'default_admin_commission' => 'Default admin commission',
  'free_delivery_over' => 'Free delivery over',
  'minimum_shipping_charge' => 'Minimum shipping charge',
  'per_km_shipping_charge' => 'Per km shipping charge',
  'dm_maximum_order' => 'Dm maximum order',
  'dm_maximum_order_hint' => 'Dm maximum order hint',
  'phone' => 'Phone',
  'footer' => 'Footer',
  'text' => 'Text',
  'logo' => 'Logo',
  'ratio' => 'Ratio',
  'choose' => 'Choose',
  'file' => 'File',
  'address' => 'Address',
  'latitude' => 'Latitude',
  'click_on_the_map_select_your_defaul_location' => 'Click on the map select your defaul location',
  'longitude' => 'Longitude',
  'successfully_updated_to_changes_restart_user_app' => 'Successfully updated to changes restart user app',
  'successfully_updated' => 'Successfully updated',
  'to_changes_restart_user_app' => 'To changes restart user app',
  'successfully_updated_to_changes_restart_app' => 'Successfully updated to changes restart app',
  'successfully_updated_to_changes_restart_the_app' => 'Successfully updated to changes restart the app',
  'push' => 'Push',
  'firebase' => 'Firebase',
  'server' => 'Server',
  'submit' => 'Submit',
  'messages' => 'Messages',
  'message' => 'Message',
  'settings_updated' => 'Settings updated',
  'welcome' => 'Welcome',
  'message_updated' => 'Message updated',
  'terms_and_condition' => 'Terms and condition',
  'privacy_policy' => 'Privacy policy',
  'about_us' => 'About us',
  'About us updated!' => 'About us updated!',
  'Privacy policy updated!' => 'Privacy policy updated!',
  'Terms and Conditions updated!' => 'Terms and Conditions updated!',
  'Linked Website' => 'Linked Website',
  'add' => 'Add',
  'new' => 'New',
  'branch' => 'Branch',
  'coverage' => 'Coverage',
  'km' => 'Km',
  '#' => '#',
  'action' => 'Action',
  'Add New Website' => 'Add New Website',
  'add new website' => 'Add new website',
  'link' => 'Link',
  'example' => 'Example',
  'www.example.com' => 'Www.example.com',
  '""_www.example.com' => 'www.example.com',
  'url' => 'Url',
  'Linked Website Table' => 'Linked Website Table',
  'image' => 'Image',
  'URL' => 'URL',
  'Status' => 'Status',
  'edit' => 'Edit',
  'Added Successfully!' => 'Added Successfully!',
  'delete' => 'Delete',
  'Updated Successfully!' => 'Updated Successfully!',
  'Website removed!' => 'Website removed!',
  'send' => 'Send',
  'title' => 'Title',
  'description' => 'Description',
  'status' => 'Status',
  'disabled' => 'Disabled',
  'update' => 'Update',
  'No Image' => 'No Image',
  'list' => 'List',
  'customers' => 'Customers',
  'export' => 'Export',
  'options' => 'Options',
  'copy' => 'Copy',
  'print' => 'Print',
  'download' => 'Download',
  'excel' => 'Excel',
  'csv' => 'Csv',
  'pdf' => 'Pdf',
  'columns' => 'Columns',
  'total' => 'Total',
  'actions' => 'Actions',
  'available' => 'Available',
  'points' => 'Points',
  'view' => 'View',
  'QR' => 'QR',
  'details' => 'Details',
  'joined_at' => 'Joined at',
  'current' => 'Current',
  'contact' => 'Contact',
  'info' => 'Info',
  'addresses' => 'Addresses',
  'Customer Details' => 'Customer Details',
  'wCustomer Details' => 'WCustomer Details',
  'Customer List' => 'Customer List',
  'search' => 'Search',
  'Admin' => 'Admin',
  'Login' => 'Login',
  'Email' => 'Email',
  '<EMAIL>' => '<EMAIL>',
  'Password' => 'Password',
  12345678 => '12345678',
  '+8801*********' => '+8801*********',
  '8+ characters required' => '8+ characters required',
  'Please enter a valid email address' => 'Please enter a valid email address',
  'Please enter a valid email address.' => 'Please enter a valid email address.',
  'Your password is invalid. Please try again.' => 'Your password is invalid. Please try again.',
  'Settings' => 'Settings',
  'Business Setup' => 'Business Setup',
  'New Business' => 'New Business',
  'Select Country' => 'Select Country',
  'Sorry! You can not enable maintainance mode in demo!' => 'Sorry! You can not enable maintainance mode in demo!',
  'Sorry! You can not enable maintenance mode in demo!' => 'Sorry! You can not enable maintenance mode in demo!',
  'Are you sure?' => 'Are you sure ',
  'Be careful before you turn on/off maintenance mode' => 'Be careful before you turn on/off maintenance mode',
  'FCM Settings' => 'FCM Settings',
  'Firebase Push Notification Setup' => 'Firebase Push Notification Setup',
  'ratio 1:1' => 'Ratio 1:1',
  'SMS Module Setup' => 'SMS Module Setup',
  '#OTP# will be replace with otp' => '#OTP# will be replace with otp',
  'NB : #OTP# will be replace with otp' => 'NB : #OTP# will be replace with otp',
  'business-settings.business-setup' => 'Business-settings.business-setup',
  'business-settings' => 'Business-settings',
  'Front Builder' => 'Front Builder',
  'Customize your overview page layout. Choose the one that best fits your needs.' => 'Customize your overview page layout. Choose the one that best fits your needs.',
  'Layout skins' => 'Layout skins',
  'Disabled' => 'Disabled',
  '3 kinds of layout skins to choose from.' => '3 kinds of layout skins to choose from.',
  'Default' => 'Default',
  'Light' => 'Light',
  'Sidebar layout options' => 'Sidebar layout options',
  'Choose between standard navigation sizing, mini or even compact with icons.' => 'Choose between standard navigation sizing  mini or even compact with icons.',
  'Compact' => 'Compact',
  'Mini' => 'Mini',
  'Header layout options' => 'Header layout options',
  'Choose the primary navigation of your header layout.' => 'Choose the primary navigation of your header layout.',
  'Default (Fluid)' => 'Default (Fluid)',
  'Default (Container)' => 'Default (Container)',
  'Double line (Fluid)' => 'Double line (Fluid)',
  'Double line (Container)' => 'Double line (Container)',
  'Reset' => 'Reset',
  'Preview' => 'Preview',
  'Do you want to logout?' => 'Do you want to logout ',
  'EX of SMS provider\'s template : your OTP is XXXX here, please check.' => 'EX of SMS provider s template : your OTP is XXXX here  please check.',
  'NB : XXXX will be replace with otp' => 'NB : XXXX will be replace with otp',
  'NB : Keep an OTP variable in your SMS providers OTP Template.' => 'NB : Keep an OTP variable in your SMS providers OTP Template.',
  'languages' => 'Languages',
  'Language' => 'Language',
  'Dashboard' => 'Dashboard',
  'language_setting' => 'Language setting',
  'changing_some_settings_will_take_time_to_show_effect_please_clear_session_or_wait_for_60_minutes_else_browse_from_incognito_mode' => 'Changing some settings will take time to show effect please clear session or wait for 60 minutes else browse from incognito mode',
  'language_table' => 'Language table',
  'add_new_language' => 'Add new language',
  'SL#' => 'SL#',
  'Id' => 'Id',
  'Code' => 'Code',
  'default' => 'Default',
  'new_language' => 'New language',
  'language' => 'Language',
  'country_code' => 'Country code',
  'direction' => 'Direction',
  'close' => 'Close',
  'Add' => 'Add',
  'Are you sure to delete this' => 'Are you sure to delete this',
  'You will not be able to revert this' => 'You will not be able to revert this',
  'Yes, delete it' => 'Yes  delete it',
  'Translate' => 'Translate',
  'Delete' => 'Delete',
  'Language Translate' => 'Language Translate',
  'language_content_table' => 'Language content table',
  'back' => 'Back',
  'value' => 'Value',
  'text_updated_successfully' => 'Text updated successfully',
  'Key removed successfully' => 'Key removed successfully',
  'Add New Notification' => 'Add New Notification',
  'New Notification' => 'New Notification',
  'Notification Table' => 'Notification Table',
  'agent section' => 'Agent section',
  'register' => 'Register',
  'first' => 'First',
  'last' => 'Last',
  'all' => 'All',
  'identity' => 'Identity',
  'type' => 'Type',
  'passport' => 'Passport',
  'driving' => 'Driving',
  'license' => 'License',
  'nid' => 'Nid',
  'number' => 'Number',
  'add new agent' => 'Add new agent',
  'Add New Agent' => 'Add New Agent',
  'First Name' => 'First Name',
  'Last Name' => 'Last Name',
  'optional' => 'Optional',
  'Gender' => 'Gender',
  'Select Gender' => 'Select Gender',
  'Male' => 'Male',
  'Female' => 'Female',
  'Other' => 'Other',
  'Occupation' => 'Occupation',
  'Agent Image' => 'Agent Image',
  'Agent Added Successfully!' => 'Agent Added Successfully!',
  'Ex : <EMAIL>' => 'Ex : <EMAIL>',
  'Ex : 017********' => 'Ex : 017********',
  'Ex : Businessman' => 'Ex : Businessman',
  'Ex : password (4digit)' => 'Ex : password (4digit)',
  'Agent' => 'Agent',
  'Agent Table' => 'Agent Table',
  'agent' => 'Agent',
  'Agent List' => 'Agent List',
  'Customer Table' => 'Customer Table',
  'Add New Customer' => 'Add New Customer',
  'Customer Image' => 'Customer Image',
  'Customer Added Successfully!' => 'Customer Added Successfully!',
  'Update Agent' => 'Update Agent',
  'Customer' => 'Customer',
  'transfer' => 'Transfer',
  'E-Money Transfer' => 'E-Money Transfer',
  'Transfer' => 'Transfer Money',
  'New Transfer' => 'New Transfer',
  'Sender' => 'Sender',
  'Select Sender' => 'Select Sender',
  'Receiver' => 'Receiver',
  'Select Receiver' => 'Select Receiver',
  'Amount' => 'Amount',
  'Ex : 9999' => 'Ex : 9999',
  'Receiver Type' => 'Receiver Type',
  'Select Receiver Type' => 'Select Receiver Type',
  'Select Type' => 'Select Type',
  'Choose' => 'Choose',
  'Select Type First' => 'Select Type First',
  'Transfer Table' => 'Transfer Table',
  'Unique ID' => 'Unique ID',
  'note' => 'Note',
  'Transfer Recorded Successfully!' => 'Transfer Recorded Successfully!',
  'Walk In Customer' => 'Walk In Customer',
  'Select_valid_receiver_type_first' => 'Select valid receiver type first',
  'Choose11111111' => 'Choose11111111',
  'EMoney' => 'EMoney',
  'pending' => 'Pending',
  'confirmed' => 'Confirmed',
  'processing' => 'Processing',
  'out_for_delivery' => 'Out for delivery',
  'Total Balance' => 'Total Balance',
  'Used Balance' => 'Used Balance',
  'Unused Balance' => 'Unused Balance',
  'Total Earned' => 'Total Earned',
  'Generate EMoney' => 'Generate EMoney',
  'Generate' => 'Generate',
  'Something went wrong!' => 'Something went wrong!',
  'EMoney generated successfully!' => 'EMoney generated successfully!',
  'cashout_charge_percent' => 'Cashout charge percent',
  'addmoney_charge_percent' => 'Addmoney charge percent',
  'sentmoney_charge_flat' => 'Sentmoney charge flat',
  'cashout_charge' => 'Cashout charge',
  'percent (%)' => 'Percent (%)',
  'in percent (%)' => 'In percent (%)',
  '""_percent (%)' => '   percent (%)',
  'addmoney_charge' => 'Addmoney charge',
  'sentmoney_charge' => 'Sentmoney charge',
  'flat' => 'Flat',
  'add_money_charge' => 'Add money charge',
  'sent_money_charge' => 'Sent money charge',
  'cash_out_charge' => 'Cash out charge',
  'send_money_charge' => 'Send money charge',
  'Transferred Successfully!' => 'Transferred Successfully!',
  'No#' => 'No#',
  'Receiver QR' => 'Receiver QR',
  'amount' => 'Amount',
  'Payment_view' => 'Payment view',
  'Payment' => 'Payment',
  'About us' => 'About us',
  'Purpose' => 'Purpose',
  'add purpose' => 'Add purpose',
  'any' => 'Any',
  'Title' => 'Title',
  'Color' => 'Color',
  'New Title' => 'New Title',
  'ratio 3:1 ' => 'Ratio 3:1 ',
  'choose file' => 'Choose file',
  'ratio 1:1 ' => 'Ratio 1:1 ',
  'color' => 'Color',
  'Hexa color code' => 'Hexa color code',
  'Successfully Added!' => 'Successfully Added!',
  'Image' => 'Image',
  'choose HEXA' => 'Choose HEXA',
  'choose_in_HEXA' => 'Choose in HEXA',
  'choose_in_HEXA_formate' => 'Choose in HEXA formate',
  'choose_in_HEXA_format' => 'Choose in HEXA format',
  'Action' => 'Action',
  'Edit' => 'Edit',
  'Logo' => 'Logo',
  'Successfully Updated!' => 'Successfully Updated!',
  'Add Title' => 'Add Title',
  'Successfully Deleted!' => 'Successfully Deleted!',
  'Edit Title' => 'Edit Title',
  'paymob_supports_EGP_currency' => 'Paymob supports EGP currency',
  'Payment Setup' => 'Payment Setup',
  'Terms and Conditions' => 'Terms and Conditions',
  'Privacy Policy' => 'Privacy Policy',
  'Forgot Password' => 'Forgot Password',
  'FORGOT PASSWORD' => 'FORGOT PASSWORD',
  'We have sent you this email in response to your request to reset your password. After you reset your password, you will be able to login with your new password.' => 'We have sent you this email in response to your request to reset your password. After you reset your password  you will be able to login with your new password.',
  'To reset your password, please use the token below' => 'To reset your password  please use the token below',
  'We recommend that you keep your password secure and not share it with anyone.If you feel your password has been compromised, you can change it by going to your app, My Account Page and clicking on the "Change Email Address or Password" link.' => 'We recommend that you keep your password secure and not share it with anyone.If you feel your password has been compromised  you can change it by going to your app  My Account Page and clicking on the  Change Email Address or Password  link.',
  'If you need help, or you have any other questions, feel free to email us.' => 'If you need help  or you have any other questions  feel free to email us.',
  'From Customer Service' => 'From Customer Service',
  'Please enter a valid phone number.' => 'Please enter a valid phone number.',
  'Enter Unername' => 'Enter Unername',
  'Enter your phone' => 'Enter your phone',
  'Enter your password' => 'Enter your password',
  'sign in' => 'Sign in',
  'Enter your phone No.' => 'Enter your phone No.',
  'Enter your phone no.' => 'Enter your phone no.',
  'welcome_message' => 'Welcome message',
  'dashboard_order_statistics' => 'Dashboard order statistics',
  'earnings' => 'Earnings',
  'EMoney Statistics' => 'EMoney Statistics',
  'top_agent' => 'Top agent',
  'Credit' => 'Credit',
  'Transaction statistics for business analytics' => 'Transaction statistics for business analytics',
  'Yearly Transaction' => 'Yearly Transaction',
  'transaction' => 'Transaction',
  'Transaction' => 'Transaction',
  'New transaction' => 'New transaction',
  'transaction Table' => 'Transaction Table',
  'Debit' => 'Debit',
  'Type' => 'Type',
  'Balance' => 'Balance',
  'send_money' => 'Send money',
  'received_money' => 'Received money',
  'admin_charge' => 'Admin charge',
  'cash_out' => 'Cash out',
  'cash_in' => 'Cash in',
  'Top Transactions' => 'Top Transactions',
  'Total Business Overview' => 'Total Business Overview',
  'Purpose Table' => 'Purpose Table',
  'ID' => 'ID',
  'Seller_Details' => 'Seller Details',
  'Back_to_seller_list' => 'Back to seller list',
  'Shop' => 'Shop',
  'Order' => 'Order',
  'Product' => 'Product',
  'Setting' => 'Setting',
  'Review' => 'Review',
  'seller_wallet' => 'Seller wallet',
  'Seller' => 'Seller',
  'Account' => 'Account',
  'Back_to_customer_list' => 'Back to customer list',
  'customer_Details' => 'Customer Details',
  'Personal Info' => 'Personal Info',
  'Phone' => 'Phone',
  'balance' => 'Balance',
  'customer wallet' => 'Customer wallet',
  'cash in transaction' => 'Cash in transaction',
  'Cash In Transaction' => 'Cash In Transaction',
  'Customer Transaction' => 'Customer Transaction',
  'customer_Transactions' => 'Customer Transactions',
  'Details' => 'Details',
  'Back_to_list' => 'Back to list',
  'Transactions' => 'Transactions',
  'Agent Commission' => 'Agent Commission',
  'two_factor' => 'Two factor',
  'payment settings updated!' => 'Payment settings updated!',
  'Failed!' => 'Failed!',
  'customer_transfer_message' => 'Customer transfer message',
  'Customer Transfer Message' => 'Customer Transfer Message',
  'EMoney Transfer Message' => 'EMoney Transfer Message',
  'Push notification failed for Customer!' => 'Push notification failed for Customer!',
  'User' => 'User',
  'user' => 'User',
  'account' => 'Account',
  'receiver' => 'Receiver',
  'Select' => 'Select',
  'All' => 'All',
  'Customers' => 'Customers',
  'Agents' => 'Agents',
  'Banner' => 'Banner',
  'banner' => 'Banner',
  'Add New Banner' => 'Add New Banner',
  'Banner Table' => 'Banner Table',
  'New Banner Title' => 'New Banner Title',
  'New banner title' => 'New banner title',
  'Update Banner' => 'Update Banner',
  'Top Agent' => 'Top Agent',
  'Top Customer' => 'Top Customer',
  'Top Agents' => 'Top Agents',
  'Top Customers' => 'Top Customers',
  'agents' => 'Agents',
  'faq' => 'Faq',
  'FAQ' => 'FAQ',
  'help_topic' => 'Help topic',
  'Table' => 'Table',
  'SL' => 'SL',
  'Question' => 'Question',
  'Answer' => 'Answer',
  'Ranking' => 'Ranking',
  'Add Help Topic' => 'Add Help Topic',
  'Type Question' => 'Type Question',
  'Type Answer' => 'Type Answer',
  'Active' => 'Active',
  'Close' => 'Close',
  'Save' => 'Save',
  'Edit Modal Help Topic' => 'Edit Modal Help Topic',
  'Are you sure delete this FAQ' => 'Are you sure delete this FAQ',
  'FAQ deleted successfully' => 'FAQ deleted successfully',
  'Refer Commission' => 'Refer Commission',
  'g-recaptcha-response google reCatpcha failed' => 'G-recaptcha-response google reCatpcha failed',
  'reCatpcha' => 'ReCatpcha',
  'Please check the recaptcha' => 'Please check the recaptcha',
  'Enter recatpcha value' => 'Enter recatpcha value',
  'recatpcha' => 'Recatpcha',
  'credentials' => 'Credentials',
  'reCatpcha Setup' => 'ReCatpcha Setup',
  'Site Key' => 'Site Key',
  'Secret Key' => 'Secret Key',
  'reCaptcha' => 'ReCaptcha',
  'Enter recaptcha value' => 'Enter recaptcha value',
  'reCaptcha Setup' => 'ReCaptcha Setup',
  'Two Factor Authentication' => 'Two Factor Authentication',
  'left' => 'Left',
  'right' => 'Right',
  'welcome_to_6cash' => 'Welcome to 6cash',
  'welcome_to_6cash_admin_panel' => 'Welcome to 6cash admin panel',
  'pagination' => 'Pagination',
  'Add Banner' => 'Add Banner',
  'Add Purpose' => 'Add Purpose',
  'transaction_list' => 'Transaction list',
  'transaction List' => 'Transaction List',
  'Amount must be greater than zero!' => 'Amount must be greater than zero!',
  'theme' => 'Theme',
  'Status Updated Successfully!' => 'Status Updated Successfully!',
  'Please fill reCAPTCHA' => 'Please fill reCAPTCHA',
  'Please Fill ReCAPTCHA' => 'Please Fill ReCAPTCHA',
  'Credentials SetUp' => 'Credentials SetUp',
  'reCaptcha credential Set up Instructions' => 'ReCaptcha credential Set up Instructions',
  'Go to the Credentials page' => 'Go to the Credentials page',
  'Click' => 'Click',
  'here' => 'Here',
  'Add a ' => 'Add a ',
  'label' => 'Label',
  '(Ex: Test Label)' => '(Ex: Test Label)',
  'Select reCAPTCHA v2 as ' => 'Select reCAPTCHA v2 as ',
  'reCAPTCHA Type' => 'ReCAPTCHA Type',
  'Sub type: I\'m not a robot Checkbox' => 'Sub type: I m not a robot Checkbox',
  'domain' => 'Domain',
  '(For ex: demo.6amtech.com)' => '(For ex: demo.6amtech.com)',
  'Check in ' => 'Check in ',
  'Accept the reCAPTCHA Terms of Service' => 'Accept the reCAPTCHA Terms of Service',
  'Press' => 'Press',
  'Submit' => 'Submit',
  'Copy' => 'Copy',
  'and' => 'And',
  'paste in the input filed below and' => 'Paste in the input filed below and',
  'cashIn Message' => 'CashIn Message',
  'cash In Message' => 'Cash In Message',
  'Cash In Message' => 'Cash In Message',
  'Cash Out Message' => 'Cash Out Message',
  'Send Money Message' => 'Send Money Message',
  'Request Money Message' => 'Request Money Message',
  'Deny Money Message' => 'Deny Money Message',
  'Approved Money Message' => 'Approved Money Message',
  'Denied Money Message' => 'Denied Money Message',
  'Add Money Message' => 'Add Money Message',
  'Received Money Message' => 'Received Money Message',
  'Transaction ID' => 'Transaction ID',
  'Transfer List' => 'Transfer List',
  'Search' => 'Search',
  '#NO' => '#NO',
  'Transaction Id' => 'Transaction Id',
  'Enter captcha value' => 'Enter captcha value',
  'Captcha Failed' => 'Captcha Failed',
  'welcome_to_the_admin_panel' => 'Welcome to the admin panel',
  'Software Version' => 'Software Version',
  'EX: 100' => 'EX: 100',
  'Too many login attempts. Banned for 1minute.' => 'Too many login attempts. Banned for 1minute.',
  'Error occurred while logging in.' => 'Error occurred while logging in.',
  'add_money' => 'Add money',
  'Request Money' => 'Request Money',
  'Note' => 'Note',
  'Customer unavailable' => 'Customer unavailable',
  'Request Money by Agents' => 'Request Money by Agents',
  'Agent Request Money' => 'Add Money Requests',
  'Requested time' => 'Requested time',
  'Requested Amount' => 'Requested Amount',
  'Pending' => 'Pending',
  'Accept' => 'Accept',
  'Accepted' => 'Accepted',
  'Agent request money' => 'Add Money Requests',
  'Agent Requested Transactions' => 'Agent\'s Requests for add money',
  'Deny' => 'Deny',
  'Successfully changed the status' => 'Successfully changed the status',
  'Status change failed' => 'Status change failed',
  'Denied' => 'Denied',
  'Ex : +88017********' => 'Ex : +88017********',
  'Must use country code' => 'Must use country code',
  '* Must use country code' => '* Must use country code',
  'Choose receiver' => 'Choose receiver',
  'Update receiver' => 'Update receiver',
  'disabl  ed' => 'Disabl  ed',
  'blocked' => 'Blocked',
  'User unavailable' => 'User unavailable',
  'ratio 3:1' => 'Ratio 3:1',
  'Update Customer' => 'Update Customer',
  'Approve' => 'Approve',
  'Approved' => 'Approved',
  'Email unavailable' => 'Email unavailable',
  'Total Earn from Charges' => 'Total Earn from Charges',
  'Ex : PIN (4digit)' => 'Ex : PIN (4digit)',
  'Unused eMoney' => 'Unused eMoney',
  'In Minute' => 'In Minute',
  'Inactive authentication time' => 'Inactive authentication time',
  'Given credentials are not correct. Try again.' => 'Given credentials are not correct. Try again.',
  'eMoney Being Used' => 'EMoney Being Used',
  'Total Generated eMoney' => 'Total Generated eMoney',
  'Notification Settings' => 'Notification Settings',
  'App Settings' => 'App Settings',
  'App settings' => 'App settings',
  'Select for app theme' => 'Select for app theme',
  'Theme 1' => 'Theme 1',
  'Theme 3' => 'Theme 3',
  'Theme 2' => 'Theme 2',
  'Select for user app theme' => 'Select for user app theme',
  'Select for User App Theme' => 'Select for User App Theme',
  'Select Theme for User App' => 'Select Theme for User App',
  'PIN' => 'PIN',
  '$digit PIN' => '$digit PIN',
  '4digit PIN' => '4digit PIN',
  'No image available' => 'No image available',
  'Time' => 'Time',
  'E-Money' => 'E-Money',
  'ReCaptcha Google Credentials Setup' => 'ReCaptcha Google Credentials Setup',
  'Enter captcha' => 'Enter captcha',
  'Enter captcha.' => 'Enter captcha.',
  'This text will be replaced in the future. This text will be replaced in the future. This is dummy text.' => 'This text will be replaced in the future. This text will be replaced in the future. This is dummy text.',
  'Welcome to 6Cash' => 'Welcome to 6Cash',
  '6 cash is a secured and user-friendly digital wallet' => '6 cash is a secured and user-friendly digital wallet',
  'Update Notification' => 'Update Notification',
  'Resend' => 'Resend',
  'edit & resend' => 'Edit & resend',
  '6cash is a secured and user-friendly digital wallet' => '6cash is a secured and user-friendly digital wallet',
  'User will be logged out if no activity happened within this time' => 'User will be logged out if no activity happened within this time',
  'Inactive auth token expire time' => 'Inactive auth token expire time',
  'Customers can use these purposes when they will send money or request money' => 'Customers can use these purposes when they will send money or request money',
  'agent_commission' => 'Agent commission',
  'Successfully deleted' => 'Successfully deleted',
  'Not found' => 'Not found',
  'Total Transaction' => 'Total Transaction',
  'Redirecting_to_the_payment_page' => 'Redirecting to the payment page',
  'SenderQQQQ' => 'SenderQQQQ',
  'Welcome to 6cash' => 'Welcome to 6cash',
  'Welcome to 6cash @' => 'Welcome to 6cash @',
  '6cash @' => '6cash @',
  '6cash @ is a secured and user-friendly digital wallet' => '6cash @ is a secured and user-friendly digital wallet',
  'User Not found' => 'User Not found',
  'amount must be greater than equal to ' => 'Amount must be greater than equal to ',
  'Amount is too low to transfer' => 'Amount is too low to transfer',
  'amount must be less than equal to ' => 'Amount must be less than equal to ',
  'nill' => 'Nill',
  'nil' => 'Nil',
  'The amount must be less than equal to ' => 'The amount must be less than equal to ',
  'The amount is too low to transfer' => 'The amount is too low to transfer',
  'Welcome to 6CashPay' => 'Welcome to 6CashPay',
  '6CashPay is a secured and user-friendly digital wallet' => '6CashPay is a secured and user-friendly digital wallet',
  'The requested amount is too big' => 'The requested amount is too big',
  'KYC Requests' => 'KYC Requests',
  'approve' => 'Approve',
  'deny' => 'Deny',
  'Identification Type' => 'Identification Type',
  'Identification Image' => 'Identification Image',
  'Identification Number' => 'Identification Number',
  'Type unavailable' => 'Type unavailable',
  'Number unavailable' => 'Number unavailable',
  'click for bigger view' => 'Click for bigger view',
  'Successfully updated.' => 'Successfully updated.',
  'KYC requests list' => 'KYC requests list',
  'Agent KYC requests' => 'Agent KYC requests',
  'The amount must be less than or equal to ' => 'The amount must be less than or equal to ',
  'identification_type' => 'Identification type',
  'identification_number' => 'Identification number',
  'Verification Requests' => 'Verification Requests',
  'Add_withdrawal_methods' => 'Add withdrawal methods',
  'sdasdas' => 'Sdasdas',
  'Select Attributes' => 'Select Attributes',
  'required' => 'Required',
  'min' => 'Min',
  'max' => 'Max',
  'minlength' => 'Minlength',
  'maxlength' => 'Maxlength',
  'placeholder' => 'Placeholder',
  'step' => 'Step',
  'Labels' => 'Labels',
  'Attributes_intro' => 'Attributes intro',
  'this is name' => 'This is name',
  'a default value will be given for the user' => 'A default value will be given for the user',
  'hint will be shown for the user' => 'Hint will be shown for the user',
  'user must have to give the info' => 'User must have to give the info',
  'minimum value for the given value' => 'Minimum value for the given value',
  'maximum value for the given value' => 'Maximum value for the given value',
  'minlength length for the given value' => 'Minlength length for the given value',
  'maximum length for the given value' => 'Maximum length for the given value',
  'Attribute for the field' => 'Attribute for the field',
  'Method Name' => 'Method Name',
  'string' => 'String',
  'Required' => 'Required',
  'Required/Optional' => 'Required/Optional',
  'Withdrawal Method add' => 'Withdrawal Method add',
  'Withdrawal Method Add' => 'Withdrawal Method Add',
  'Method Type' => 'Method Type',
  'Is Required' => 'Is Required',
  'Add Method' => 'Add Method',
  'Method Fields' => 'Method Fields',
  'Method Field Name' => 'Method Field Name',
  'Name' => 'Name',
  'Fields Name' => 'Fields Name',
  'reset' => 'Reset',
  'String' => 'String',
  'Number' => 'Number',
  'Input Name' => 'Input Name',
  'Input Type' => 'Input Type',
  'Field Name' => 'Field Name',
  'Field Type' => 'Field Type',
  'Add Fields' => 'Add Fields',
  'Reset All' => 'Reset All',
  'Fields' => 'Fields',
  'successfully removed' => 'Successfully removed',
  'Are you sure' => 'Are you sure',
  'Removed successfully' => 'Removed successfully',
  'Reached maximum' => 'Reached maximum',
  'ok' => 'Ok',
  'Filter by method' => 'Filter by method',
  'Kane Scott' => 'Kane Scott',
  'BKASH' => 'BKASH',
  'Paypal' => 'Paypal',
  'qawrewr' => 'Qawrewr',
  'Withdrawal Method' => 'Withdrawal Method',
  'Withdraw_Requests' => 'Withdraw Requests',
  'Sender_Note' => 'Sender Note',
  'Admin_Note' => 'Admin Note',
  'Payment_Status' => 'Payment Status',
  'User_not_available' => 'User not available',
  'Not_Paid' => 'Not Paid',
  'Is_Paid' => 'Is Paid',
  'Request_Status' => 'Request Status',
  'Actions' => 'Actions',
  'approved' => 'Approved',
  'Paid' => 'Paid',
  'denied' => 'Denied',
  'The request has been successfully updated' => 'The request has been successfully updated',
  'User Log' => 'User Log',
  'User Logs' => 'User Logs',
  'Users Log' => 'Users Log',
  'login_time' => 'Login time',
  'device_model' => 'Device model',
  'os' => 'Os',
  'browser' => 'Browser',
  'mac_address' => 'Mac address',
  'ip_address' => 'Ip address',
  'Logs' => 'Logs',
  'Log' => 'Log',
  'Agent Log' => 'Agent Log',
  'Invalid data' => 'Invalid data',
  'device_id' => 'Device id',
  'BCASH' => 'BCASH',
  'pin' => 'Pin',
  'Withdrawal Method Fields' => 'Withdrawal Method Fields',
  'Date' => 'Date',
  'Test' => 'Test',
  'Card' => 'Card',
  'withdraw' => 'Withdraw',
  'Export' => 'Export',
  'Input Field Placeholder/Hints' => 'Input Field Placeholder/Hints',
  'Input Field Name' => 'Input Field Name',
  'Input Field Type' => 'Input Field Type',
  0 => '0',
  'Placeholder' => 'Placeholder',
  'Tyrone Russell' => 'Tyrone Russell',
  'No_data_available' => 'No data available',
  'Not_available' => 'Not available',
  'The request sender is unavailable' => 'The request sender is unavailable',
  'Sender Type' => 'Sender Type',
  'Verification requests list' => 'Verification requests list',
  'Agent Verification requests' => 'Agent Verification requests',
  'Verification List' => 'Verification List',
  'Agent/Customer will use these methods to withdraw their money directly from admin' => 'Agent/Customer will use these methods to withdraw their money directly from admin',
  'Remove the input field' => 'Remove the input field',
  'verification_request_is_accepted' => 'Verification request is accepted',
  'verification_request_is_denied' => 'Verification request is denied',
  '6Cash is a secured and user-friendly digital wallet' => '6Cash is a secured and user-friendly digital wallet',
  'Add New Merchant' => 'Add New Merchant',
  'Merchant Image' => 'Merchant Image',
  'Ex : 434624829' => 'Ex : 434624829',
  'Store Name' => 'Store Name',
  'Store Domain' => 'Store Domain',
  'Address' => 'Address',
  'BIN' => 'BIN',
  'Online Payment' => 'Online Payment',
  'card_holder\'s_name' => 'Card holder s name',
  'expiry_date' => 'Expiry date',
  'cvv' => 'Cvv',
  'Ex : DH-23434-LS' => 'Ex : DH-23434-LS',
  'Deliveryman Image' => 'Deliveryman Image',
  'Identity Image' => 'Identity Image',
  'Merchant Added Successfully!' => 'Merchant Added Successfully!',
  'merchant Table' => 'Merchant Table',
  'Ex: 534354' => 'Ex: 534354',
  'Update Merchant' => 'Update Merchant',
  'erchant' => 'Erchant',
  'Merchant Added Failed!' => 'Merchant Added Failed!',
  'Merchant Updated Failed!' => 'Merchant Updated Failed!',
  'Merchant Updated Successfully!' => 'Merchant Updated Successfully!',
  'store_name' => 'Store name',
  'store_domain' => 'Store domain',
  'merchant_number' => 'Merchant number',
  'Merchant Verification requests' => 'Merchant Verification requests',
  'merchants' => 'Merchants',
  'Merchant' => 'Merchant',
  'welcome_to_6cash_merchant_panel' => 'Welcome to 6cash merchant panel',
  'shop' => 'Shop',
  'integration' => 'Integration',
  'Integration Settings' => 'Integration Settings',
  'Public Key' => 'Public Key',
  'generate_code' => 'Generate code',
  'coupon code' => 'Coupon code',
  'Generate Public Key' => 'Generate Public Key',
  'Generate Secret Key' => 'Generate Secret Key',
  'generate' => 'Generate',
  'developer' => 'Developer',
  'Public key is required' => 'Public key is required',
  'Secret key is required' => 'Secret key is required',
  'Merchant number is required' => 'Merchant number is required',
  'Amount is required' => 'Amount is required',
  'User phone is required' => 'User phone is required',
  'Callback url is required' => 'Callback url is required',
  'Payment successful' => 'Payment successful',
  'success' => 'Success',
  'Your 6Cash Account Number' => 'Your 6Cash Account Number',
  '+88 017XXXXXXXX' => '+88 017XXXXXXXX',
  'I agree to the' => 'I agree to the',
  'Terms & conditions' => 'Terms & conditions',
  'Proceed' => 'Proceed',
  'Hotline' => 'Hotline',
  847283 => '847283',
  'OTP' => 'OTP',
  'Cancel' => 'Cancel',
  'Enter Verification Code' => 'Enter Verification Code',
  'Resend Code' => 'Resend Code',
  'Enter PIN Number' => 'Enter PIN Number',
  'PIN Number' => 'PIN Number',
  'Confirm' => 'Confirm',
  'Payment Successfully Completed' => 'Payment Successfully Completed',
  'Phone is required' => 'Phone is required',
  'OTP send !' => 'OTP send !',
  'Check terms and condition' => 'Check terms and condition',
  'Pin must be 4 digit' => 'Pin must be 4 digit',
  'You do not have enough balance. Please generate eMoney first.' => 'You do not have enough balance. Please generate eMoney first.',
  'Payment failed !' => 'Payment failed !',
  'Payment successful !' => 'Payment successful !',
  'please enter a valid phone' => 'Please enter a valid phone',
  'OTP is required' => 'OTP is required',
  'OTP must be 4 digit' => 'OTP must be 4 digit',
  'success !' => 'Success !',
  'Pin is required' => 'Pin is required',
  'wrong password !' => 'Wrong password !',
  'wrong pin !' => 'Wrong pin !',
  'No' => 'No',
  'Yes' => 'Yes',
  'Change status to pending ?' => 'Change status to pending  ',
  'Merchant Setup' => 'Merchant Setup',
  'Merchant OTP ' => 'Merchant OTP ',
  'Merchant OTP Verification' => 'Merchant OTP Verification',
  'OTP verify failed !' => 'OTP verify failed !',
  'pin mismatched !' => 'Pin mismatched !',
  'Transaction id is required' => 'Transaction id is required',
  'Payment OTP Verification' => 'Payment OTP Verification',
  'Merchant OTP' => 'Merchant OTP',
  'user not found !' => 'User not found !',
  'Withdraw_request_accepted' => 'Withdraw request accepted',
  'Withdraw' => 'Withdraw',
  'add withdraw request' => 'Add withdraw request',
  'withdraw request list' => 'Withdraw request list',
  'request' => 'Request',
  'Withdraw Request' => 'Withdraw Request',
  'Add Request' => 'Add Request',
  'select' => 'Select',
  'withdraw methods' => 'Withdraw methods',
  'payment Method' => 'Payment Method',
  'Withdraw Request List' => 'Withdraw Request List',
  'sender note' => 'Sender note',
  'Sender Note' => 'Sender Note',
  'Withdraw request send !' => 'Withdraw request send !',
  'Withdraw_request_denied' => 'Withdraw request denied',
  'Available Balance' => 'Available Balance',
  'Current eMoney' => 'Current eMoney',
  'OTP verify success !' => 'OTP verify success !',
  '6 digit PIN' => '6 digit PIN',
  'Merchant List' => 'Merchant List',
  'regenerate' => 'Regenerate',
  'Merchant Number' => 'Merchant Number',
  'Current Ballance' => 'Current Ballance',
  'Total Withdraw' => 'Total Withdraw',
  'You want to regenerate public key and merchant key' => 'You want to regenerate public key and merchant key',
  'Ex : 17********' => 'Ex : 17********',
  'Ex : 171*******' => 'Ex : 171*******',
  'PIN must contain 6 characters' => 'PIN must contain 6 characters',
  'Country code select is required' => 'Country code select is required',
  'Verification Code' => 'Verification Code',
  'select country' => 'Select country',
  'Country code is required' => 'Country code is required',
  'Password must contain 4 characters' => 'Password must contain 4 characters',
  'country code' => 'Country code',
  'Withdraw Table' => 'Withdraw Table',
  '8 digit PIN' => '8 digit PIN',
  'PIN must contain 8 characters' => 'PIN must contain 8 characters',
  'Payment time expired' => 'Payment time expired',
  'Hotline Number' => 'Hotline Number',
  34532525 => '34532525',
  123456789 => '123456789',
  'Terms_and_conditions' => 'Terms and conditions',
  'About_us' => 'About us',
  'Privacy_policy' => 'Privacy policy',
  'Terms and conditions' => 'Terms and conditions',
  'aa' => 'Aa',
  'debit' => 'Debit',
  'credit' => 'Credit',
  'Domain url is required' => 'Domain url is required',
  'Domain is required' => 'Domain is required',
  'This phone number is already taken' => 'This phone number is already taken',
  'Merchant Config' => 'Merchant Config',
  'Merchant Settings' => 'Merchant Settings',
  'Transaction Commission' => 'Transaction Commission',
  '4 digit PIN' => '4 digit PIN',
  'Add Domain' => 'Add Domain',
  'Domain Callback' => 'Domain Callback',
  'Domain' => 'Domain',
  'Callback' => 'Callback',
  'Payment Received' => 'Payment Received',
  'callback' => 'Callback',
  'driving_license' => 'Driving license',
  'store_callback' => 'Store callback',
  'Add Payment Message' => 'Add Payment Message',
  '8 digit Password' => '8 digit Password',
  'This user do not have enough balance. Please generate eMoney first.' => 'This user do not have enough balance. Please generate eMoney first.',
  'Pending Ballance' => 'Pending Ballance',
  '8 digit password' => '8 digit password',
  'fail' => 'Fail',
  'Agent/Customer/Merchant will use these methods to withdraw their money directly from admin' => 'Agent/Customer/Merchant will use these methods to withdraw their money directly from admin',
  'Business Analytics' => 'Business Analytics',
  'E-Money Statistics' => 'E-Money Statistics',
  'Search by Name' => 'Search by Name',
  'Search by ID' => 'Search by ID',
  'Search by Agent' => 'Search by Agent',
  'Update_Agent' => 'Update Agent',
  'Agent Details' => 'Agent Details',
  'Agent Transactions' => 'Agent Transactions',
  'Agent Details Logs' => 'Agent Details Logs',
  'sdf' => 'Sdf',
  'g-recaptcha-response google reCaptcha failed' => 'G-recaptcha-response google reCaptcha failed',
  'Contact' => 'Contact',
  'Contacts' => 'Contacts',
  'banner_image' => 'Banner image',
  'banner Image' => 'Banner Image',
  'Search by Title' => 'Search by Title',
  'Add New Purpose' => 'Add New Purpose',
  'Update Purpose' => 'Update Purpose',
  'Update Website' => 'Update Website',
  'update Notification' => 'Update Notification',
  'Sms Module' => 'Sms Module',
  'Payment Methods' => 'Payment Methods',
  'Recaptcha' => 'Recaptcha',
  'Languages' => 'Languages',
  'Business Settings' => 'Business Settings',
  'Pages' => 'Pages',
  'pages' => 'Pages',
  'system' => 'System',
  'Business Information' => 'Business Information',
  'form' => 'Form',
  'Instructions' => 'Instructions',
  'Resource not found' => 'Resource not found',
  'Request not found' => 'Request not found',
  'expense' => 'Expense',
  'withdraw_charge' => 'Withdraw charge',
  'Charge Setup' => 'Charge Setup',
  'Charge Config' => 'Charge Config',
  'Bonus' => 'Bonus',
  'Add New bonus' => 'Add New bonus',
  'bonus Table' => 'Bonus Table',
  'Add bonus' => 'Add bonus',
  'User Type' => 'User Type',
  'maximum_add_money_amount' => 'Maximum add money amount',
  'maximum_bonus_amount' => 'Maximum bonus amount',
  'limit_per_user' => 'Limit per user',
  'start_date_time' => 'Start date time',
  'end_date_time' => 'End date time',
  'Flat' => 'Flat',
  'Percentage' => 'Percentage',
  'bonus' => 'Bonus',
  'Bonus Type' => 'Bonus Type',
  'maximum Add Money Amount' => 'Maximum Add Money Amount',
  'maximum Bonus Amount' => 'Maximum Bonus Amount',
  'limit Per User' => 'Limit Per User',
  'Each user will only receive the bonus up to the limit' => 'Each user will only receive the bonus up to the limit',
  'End Date' => 'End Date',
  'End Up' => 'End Up',
  'Start Date' => 'Start Date',
  'Bonus added successfully!' => 'Bonus added successfully!',
  'Limit Per User' => 'Limit Per User',
  'Maximum Add Money Amount' => 'Maximum Add Money Amount',
  'Bonus status updated!' => 'Bonus status updated!',
  'Bonus removed!' => 'Bonus removed!',
  'Update bonus' => 'Update bonus',
  'Bonus updated successfully!' => 'Bonus updated successfully!',
  'Maximum Bonus Amount' => 'Maximum Bonus Amount',
  'Minimum Add Money Amount' => 'Minimum Add Money Amount',
  'percentage' => 'Percentage',
  'Total Expense' => 'Total Expense',
  'Add Money Bonus' => 'Add Money Bonus',
  'Last Year' => 'Last Year',
  'This Year' => 'This Year',
  'Last Month' => 'Last Month',
  'This Month' => 'This Month',
  'This Week' => 'This Week',
  'Total Debit' => 'Total Debit',
  'Total Users' => 'Total Users',
  'Total Transactions' => 'Total Transactions',
  'business setup' => 'Business setup',
  'View All' => 'View All',
  'E1dit' => 'E1dit',
  'Master Admin' => 'Master Admin',
  'admin panel' => 'Admin panel',
  'Transaction Charge Configuration' => 'Transaction Charge Configuration',
  'Transaction Charges' => 'Transaction Charges',
  'Agent will get the percentage from cash out charge' => 'Agent will get the percentage from cash out charge',
  'The customer will be charged the percentage from cash out amount.' => 'The customer will be charged the percentage from cash out amount.',
  'The sender will be charged the percentage of the sending money amount' => 'The sender will be charged the percentage of the sending money amount',
  'The sender will be charged the amount while sending money to others' => 'The sender will be charged the amount while sending money to others',
  'Merchant Details' => 'Merchant Details',
  'No data available' => 'No data available',
  'Expense Transactions' => 'Expense Transactions',
  'Email Unavailable' => 'Email Unavailable',
  'driving_licence' => 'Driving licence',
  'My Method' => 'My Method',
  'Filter' => 'Filter',
  'terms and Condition' => 'Terms and Condition',
  'privacy Plicy' => 'Privacy Plicy',
  'terms & Condition' => 'Terms & Condition',
  'privacy Policy' => 'Privacy Policy',
  'about Us' => 'About Us',
  'Agents or customers will be charged the percentage of the withdrawal amount' => 'Agents or customers will be charged the percentage of the withdrawal amount',
  'The amount is too big. Please contact with admin' => 'The amount is too big. Please contact with admin',
  'Add Withdrawal Methods' => 'Add Withdrawal Methods',
  'No Data Available' => 'No Data Available',
  'The customer will be charged the percentage of cash out amount' => 'The customer will be charged the percentage of cash out amount',
  'The agent will get the percentage from cash out charge' => 'The agent will get the percentage from cash out charge',
  'choose File' => 'Choose File',
  'Favicon' => 'Favicon',
  'Mail Cash' => 'Mail Cash',
  'Search by user info' => 'Search by user info',
  'Search by ip_address, device_id, browser, os or device_model' => 'Search by ip address  device id  browser  os or device model',
  'Search by ip, device id, browser, os or device model' => 'Search by ip  device id  browser  os or device model',
  'Search by ip, deviceId, browser, os or device model' => 'Search by ip  deviceId  browser  os or device model',
  'The customer will be charged the amount while sending money to others' => 'The customer will be charged the amount while sending money to others',
  'Customers can use these purposes when they will send money' => 'Customers can use these purposes when they will send money',
  'Merchant Panel' => 'Merchant Panel',
  'Admin Charge' => 'Admin Charge',
  'Expense' => 'Expense',
  'The withdraw request sender will be charged the percentage of the withdrawal amount' => 'The withdraw request sender will be charged the percentage of the withdrawal amount',
  'Receiver is not verified' => 'Receiver is not verified',
  'Receiver must be a user' => 'Receiver must be a user',
  'Request Status' => 'Request Status',
  'Add Money Charge Message' => 'Add Money Charge Message',
  'Add Money Bonus Message' => 'Add Money Bonus Message',
  'Click to refresh' => 'Click to refresh',
  'add_money_bonus' => 'Add money bonus',
  'Bonus will be applied depending on the highest valid "Minimum Add Money amount"' => 'Bonus will be applied depending on the highest valid  Minimum Add Money amount ',
  'Bonus will be applied depending on the highest valid Minimum Add Money amount' => 'Bonus will be applied depending on the highest valid Minimum Add Money amount',
  'Welcome to ' => 'Welcome to ',
  '6cash' => '6cash',
  'Welcome to wer' => 'Welcome to wer',
  'wer is a secured and user-friendly digital wallet' => 'Wer is a secured and user-friendly digital wallet',
  'Welcome to test 6cash' => 'Welcome to test 6cash',
  'test 6cash is a secured and user-friendly digital wallet' => 'Test 6cash is a secured and user-friendly digital wallet',
  'Enter new password' => 'Enter new password',
  'Confirm your new password' => 'Confirm your new password',
  'Login and OTP Setup' => 'Login and OTP Setup',
  'OTP Setup' => 'OTP Setup',
  'maximum_OTP_submit_attempt' => 'Maximum OTP submit attempt',
  'The maximum OTP hit is a measure of how many times a specific one-time password has been generated and used within a time.' => 'The maximum OTP hit is a measure of how many times a specific one-time password has been generated and used within a time.',
  'in_second' => 'In second',
  'otp_resend_time' => 'Otp resend time',
  'If the user fails to get the OTP within a certain time, user can request a resend.' => 'If the user fails to get the OTP within a certain time  user can request a resend.',
  'temporary_block_time' => 'Temporary block time',
  'Temporary OTP block time refers to a security measure implemented by systems to restrict access to OTP service for a specified period of time for wrong OTP submission.' => 'Temporary OTP block time refers to a security measure implemented by systems to restrict access to OTP service for a specified period of time for wrong OTP submission.',
  'maximum_login_attempt' => 'Maximum login attempt',
  'The maximum login hit is a measure of how many times a user can submit password within a time.' => 'The maximum login hit is a measure of how many times a user can submit password within a time.',
  'temporary_login_block_time' => 'Temporary login block time',
  'Temporary login block time refers to a security measure implemented by systems to restrict access for a specified period of time for wrong Password submission.' => 'Temporary login block time refers to a security measure implemented by systems to restrict access for a specified period of time for wrong Password submission.',
  'OTP_resend_time' => 'OTP resend time',
  'Settings updated!' => 'Settings updated!',
  'System Feature' => 'System Feature',
  'Add Money' => 'Add Money',
  'Send Money' => 'Send Money',
  'Cash Out' => 'Cash Out',
  'Send Money Request' => 'Send Money Request',
  'Transaction Limits' => 'Transaction Limits',
  'please_try_again_after_' => 'Please try again after ',
  'seconds' => 'Seconds',
  'Customer Transaction Limits' => 'Customer Transaction Limits',
  'Daily Transaction' => 'Daily Transaction',
  'Monthly Transaction' => 'Monthly Transaction',
  'Add Money Limit' => 'Add Money Limit',
  'Cookie Text' => 'Cookie Text',
  'Ex: 5' => 'Ex: 5',
  'transaction_limit_per_day' => 'Transaction limit per day',
  'Transaction Limit Per Day' => 'Transaction Limit Per Day',
  'Ex: 100' => 'Ex: 100',
  'Ex: 500' => 'Ex: 500',
  'Max Amount per Transaction' => 'Max Amount per Transaction',
  'Total Transaction Amount Per Day' => 'Total Transaction Amount Per Day',
  'Transaction Limit Per Month' => 'Transaction Limit Per Month',
  'Total Transaction_amount_per_month' => 'Total Transaction amount per month',
  'Send Money Limit' => 'Send Money Limit',
  'Ex: 10' => 'Ex: 10',
  'Send Money Request Limit' => 'Send Money Request Limit',
  'Cash Out Limit' => 'Cash Out Limit',
  'Withdraw Request Limit' => 'Withdraw Request Limit',
  'Receiver not found' => 'Receiver not found',
  'maximum amount per transaction exceeded' => 'Maximum amount per transaction exceeded',
  'transaction limit per day exceeded' => 'Transaction limit per day exceeded',
  'PIN is incorrect' => 'PIN is incorrect',
  'total transaction amount per day exceeded' => 'Total transaction amount per day exceeded',
  'transaction limit per month exceeded' => 'Transaction limit per month exceeded',
  'Agent Self Registration' => 'Agent Self Registration',
  'Too_many_attempts. please_try_again_after_' => 'Too many attempts. please try again after ',
  'Transaction limit per day cannot be greater than the transaction limit per month.' => 'Transaction limit per day cannot be greater than the transaction limit per month.',
  'Your account is temporarily blocked. Please_try_again_after_' => 'Your account is temporarily blocked. Please try again after ',
  'Try_again_after' => 'Try again after',
  'Welcome to 9cash' => 'Welcome to 9cash',
  '9cash is a secured and user-friendly digital wallet' => '9cash is a secured and user-friendly digital wallet',
  'Maximum amount per transaction cannot be greater than the total transaction amount per day.' => 'Maximum amount per transaction cannot be greater than the total transaction amount per day.',
  'Too_many_attempts. Please_try_again_after_' => 'Too many attempts. Please try again after ',
  'When this feature is enabled, transaction limits will be applied on a daily and monthly basis.' => 'When this feature is enabled  transaction limits will be applied on a daily and monthly basis.',
  'The maximum number of transactions allowed in a day is set by this setting.' => 'The maximum number of transactions allowed in a day is set by this setting.',
  'The maximum amount of money that can be used in a single transaction is set by this setting.' => 'The maximum amount of money that can be used in a single transaction is set by this setting.',
  'This field refers to the maximum amount of money for transactions in a day.' => 'This field refers to the maximum amount of money for transactions in a day.',
  'The maximum number of transactions allowed in a month is set by this setting.' => 'The maximum number of transactions allowed in a month is set by this setting.',
  'This field refers to the maximum amount of money for transactions in a month.' => 'This field refers to the maximum amount of money for transactions in a month.',
  'When this field is active agent can register themself using the agent app' => 'When this field is active agent can register themself using the agent app',
  'Money Request Limit' => 'Money Request Limit',
  'SMS Module' => 'SMS Module',
  'resend' => 'Resend',
  'update_language' => 'Update language',
  'system_addon' => 'System addon',
  'system_addons' => 'System addons',
  'How_the_Setting_Works' => 'How the Setting Works',
  'To Integrate add-on to your system please follow the instruction below' => 'To Integrate add-on to your system please follow the instruction below',
  'After purchasing Demandium from codecanyon. You will find a file download option.' => 'After purchasing Demandium from codecanyon. You will find a file download option.',
  'Download the file. It will be downloaded as Zip format Filename.Zip' => 'Download the file. It will be downloaded as Zip format Filename.Zip',
  'Extract the file you will get a file name payment.zip.' => 'Extract the file you will get a file name payment.zip.',
  'Upload the file here and your Addon uploading is complete !' => 'Upload the file here and your Addon uploading is complete !',
  'Then active the Addon and setup all the options. you are good to go !' => 'Then active the Addon and setup all the options. you are good to go !',
  'Got_It' => 'Got It',
  'upload_Payment_Module' => 'Upload Payment Module',
  'instructions' => 'Instructions',
  'please_make_sure' => 'Please make sure',
  'your_server_php' => 'Your server php',
  'value_is_grater
                                   _or_equal_to_20MB' => 'Value is grater
                                    or equal to 20MB',
  'current_value_is' => 'Current value is',
  'value_is_grater_or_equal_to_20MB' => 'Value is grater or equal to 20MB',
  'upload' => 'Upload',
  'updated successfully!' => 'Updated successfully!',
  'system_Addon' => 'System Addon',
  'System Addon' => 'System Addon',
  'After purchasing 6cash from codecanyon. You will find a file download option.' => 'After purchasing 6cash from codecanyon. You will find a file download option.',
  'file_upload_successfully!' => 'File upload successfully!',
  'are_you_sure?' => 'Are you sure ',
  'want_to_change_status' => 'Want to change status',
  'no' => 'No',
  'yes' => 'Yes',
  'codecanyon_username' => 'Codecanyon username',
  'purchase_code' => 'Purchase code',
  'activate' => 'Activate',
  'addon_menus' => 'Addon menus',
  'payment_setup' => 'Payment setup',
  'sms_setup' => 'Sms setup',
  'Addon Menus' => 'Addon Menus',
  'are_you_sure_you_want_to_delete_the_payment_module' => 'Are you sure you want to delete the payment module',
  'once_you_delete' => 'Once you delete',
  'you_will_lost_the_this_payment_module' => 'You will lost the this payment module',
  'cancel' => 'Cancel',
  'want_to_active_this_payment_module' => 'Want to active this payment module',
  'file_delete_successfully' => 'File delete successfully',
  'already_installed!' => 'Already installed!',
  'already_installed' => 'Already installed',
  'payment_gateway_configuration' => 'Payment gateway configuration',
  'payment_gateway_title' => 'Payment gateway title',
  'sms_gateways_configuration' => 'Sms gateways configuration',
  'Your_current_payment_settings_are_disabled,_because_you_have_enabled_payment_gateway_addon,_To_visit_your_currently_active_payment_gateway_settings_please_follow_the_link.' => 'Your current payment settings are disabled  because you have enabled payment gateway addon  To visit your currently active payment gateway settings please follow the link.',
  'Live' => 'Live',
  'want_to_inactive_this_Payment & Sms gateways' => 'Want to inactive this Payment & Sms gateways',
  'want_to_active_this_payment_modulePayment & Sms gateways' => 'Want to active this payment modulePayment & Sms gateways',
  'messages.addon_menus' => 'Messages.addon menus',
  'Choose_Logo' => 'Choose Logo',
  'Your current sms settings are disabled, because you have enabled
                            sms gateway addon, To visit your currently active sms gateway settings please follow
                            the link.' => 'Your current sms settings are disabled  because you have enabled
                            sms gateway addon  To visit your currently active sms gateway settings please follow
                            the link.',
  'are_you_sure_you_want_to_delete_the_ Payment & Sms gateways' => 'Are you sure you want to delete the  Payment & Sms gateways',
  'you_will_lost_the_this_ Payment & Sms gateways' => 'You will lost the this  Payment & Sms gateways',
  'want_to_activate_this_ Payment & Sms gateways' => 'Want to activate this  Payment & Sms gateways',
  'want_to_inactive_this_ Payment & Sms gateways' => 'Want to inactive this  Payment & Sms gateways',
  'addon' => 'Addon',
  'want_to_inactive_this_' => 'Want to inactive this ',
  'are_you_sure_you_want_to_delete_the_' => 'Are you sure you want to delete the ',
  'you_will_lost_the_this_' => 'You will lost the this ',
  'want_to_activate_this_' => 'Want to activate this ',
  'Landing_Page-Settings' => 'Landing Page-Settings',
  'Intro Section' => 'Intro Section',
  'Landing Setup' => 'Landing Setup',
  'Intro' => 'Intro',
  'Description' => 'Description',
  'download_link' => 'Download link',
  'Intro Left Image' => 'Intro Left Image',
  'Intro Middle Image' => 'Intro Middle Image',
  'Intro Right Image' => 'Intro Right Image',
  'Left Image' => 'Left Image',
  'Middle Image' => 'Middle Image',
  'Right Image' => 'Right Image',
  'landing' => 'Landing',
  'Landing_Page_Settings' => 'Landing Page Settings',
  'Feature' => 'Feature',
  'header_title' => 'Header title',
  'sub_title' => 'Sub title',
  'Feature Image' => 'Feature Image',
  'want_to_delete_this' => 'Want to delete this',
  'feature' => 'Feature',
  'intro_section' => 'Intro section',
  'screenshots' => 'Screenshots',
  'Screenshot Image' => 'Screenshot Image',
  'Why_Choose_Us' => 'Why Choose Us',
  'why-choose-us' => 'Why-choose-us',
  'Choose Us Image' => 'Choose Us Image',
  'WHy Choose Us Image' => 'WHy Choose Us Image',
  'Icon' => 'Icon',
  'landing page settings' => 'Landing page settings',
  'why choose us' => 'Why choose us',
  'Agent Registration' => 'Agent Registration',
  'agent_registraion_section' => 'Agent registraion section',
  'agent_registration_section' => 'Agent registration section',
  'Banner Image' => 'Banner Image',
  'How It Work' => 'How It Work',
  'how_it_works_section' => 'How it works section',
  'How It Works' => 'How It Works',
  'Download' => 'Download',
  'download_section' => 'Download section',
  'play_store_link' => 'Play store link',
  'app_store_link' => 'App store link',
  'Business Statistics' => 'Business Statistics',
  'business_statistics' => 'Business statistics',
  'download_icon' => 'Download icon',
  'Download Icon' => 'Download Icon',
  'download_count' => 'Download count',
  'download_description' => 'Download description',
  'download_sort_description' => 'Download sort description',
  'Review Icon' => 'Review Icon',
  'Country Icon' => 'Country Icon',
  'review_description' => 'Review description',
  'review_sort_description' => 'Review sort description',
  'country_description' => 'Country description',
  'country_sort_description' => 'Country sort description',
  'review_count' => 'Review count',
  'country_count' => 'Country count',
  'Testimonial' => 'Testimonial',
  'Business Download' => 'Business Download',
  'opinion' => 'Opinion',
  'rating' => 'Rating',
  'User_Type' => 'User Type',
  'testimonial' => 'Testimonial',
  'Reviewer Image' => 'Reviewer Image',
  'user_type' => 'User type',
  '6cash Home' => '6cash Home',
  'Invalid_Page_Name' => 'Invalid Page Name',
  'admin_default_landing_page' => 'Admin default landing page',
  'You_can_turn_off/on_system-provided_landing_page' => 'You can turn off/on system-provided landing page',
  'messages.header_section' => 'Messages.header section',
  'messages.default' => 'Messages.default',
  'header_section' => 'Header section',
  'You_can_turn_off/on_intro_section_of_landing_page' => 'You can turn off/on intro section of landing page',
  'intro_section_landing_page' => 'Intro section landing page',
  'feature_section_landing_page' => 'Feature section landing page',
  'You_can_turn_off/on_feature_section_of_landing_page' => 'You can turn off/on feature section of landing page',
  'screenshot_section_landing_page' => 'Screenshot section landing page',
  'You_can_turn_off/on_screenshot_section_of_landing_page' => 'You can turn off/on screenshot section of landing page',
  'why_choose_us_section_landing_page' => 'Why choose us section landing page',
  'You_can_turn_off/on_why_choose_us_section_of_landing_page' => 'You can turn off/on why choose us section of landing page',
  'agent_registration_section_landing_page' => 'Agent registration section landing page',
  'You_can_turn_off/on_agent_registration_section_of_landing_page' => 'You can turn off/on agent registration section of landing page',
  'how_it_works_section_landing_page' => 'How it works section landing page',
  'You_can_turn_off/on_how_it_works_section_of_landing_page' => 'You can turn off/on how it works section of landing page',
  'download_section_landing_page' => 'Download section landing page',
  'You_can_turn_off/on_download_section_of_landing_page' => 'You can turn off/on download section of landing page',
  'business_statistics_landing_page' => 'Business statistics landing page',
  'You_can_turn_off/on_business_statistics_of_landing_page' => 'You can turn off/on business statistics of landing page',
  'Select Rating' => 'Select Rating',
  '0.5' => '0.5',
  1 => '1',
  '1.5' => '1.5',
  2 => '2',
  '2.5' => '2.5',
  3 => '3',
  '3.5' => '3.5',
  4 => '4',
  '4.5' => '4.5',
  5 => '5',
  'Explore More' => 'Explore More',
  'Active Users' => 'Active Users',
  'user_rating_and_count' => 'User rating and count',
  'Reviewer Icon/Image' => 'Reviewer Icon/Image',
  'Reviewer Icon' => 'Reviewer Icon',
  'reviewer_name' => 'Reviewer name',
  'Image One' => 'Image One',
  'Image Two' => 'Image Two',
  'Image Three' => 'Image Three',
  'total_user_count' => 'Total user count',
  'total_user_content' => 'Total user content',
  'Subscribe Newsletter' => 'Subscribe Newsletter',
  'be a part of our journey to self discovery and love.' => 'Be a part of our journey to self discovery and love.',
  'subscription_successful' => 'Subscription successful',
  'Mail Config' => 'Mail Config',
  'Turn OFF' => 'Turn OFF',
  'Important!' => 'Important!',
  'Warning!' => 'Warning!',
  'Enabling mail configuration services will allow the system to send emails. Please ensure that you have correctly configured the SMTP settings to avoid potential issues with email delivery.' => 'Enabling mail configuration services will allow the system to send emails. Please ensure that you have correctly configured the SMTP settings to avoid potential issues with email delivery.',
  'Disabling mail configuration services will prevent the system from sending emails. Please only turn off this service if you intend to temporarily suspend email sending. Note that this may affect system functionality that relies on email communication.' => 'Disabling mail configuration services will prevent the system from sending emails. Please only turn off this service if you intend to temporarily suspend email sending. Note that this may affect system functionality that relies on email communication.',
  '*By Turning OFF mail configuration, all your mailing services will be off.' => '*By Turning OFF mail configuration  all your mailing services will be off.',
  'Turn ON' => 'Turn ON',
  'Ok' => 'Ok',
  'Send Test Mail' => 'Send Test Mail',
  'configuration_updated_successfully' => 'Configuration updated successfully',
  'messages.mailer_name' => 'Messages.mailer name',
  'messages.Ex:' => 'Messages.Ex:',
  'messages.host' => 'Messages.host',
  'messages.Ex_:_mail.6am.one' => 'Messages.Ex : mail.6am.one',
  'messages.driver' => 'Messages.driver',
  'messages.Ex : smtp' => 'Messages.Ex : smtp',
  'messages.port' => 'Messages.port',
  'messages.Ex : 587' => 'Messages.Ex : 587',
  'messages.username' => 'Messages.username',
  'messages.email_id' => 'Messages.email id',
  'messages.encryption' => 'Messages.encryption',
  'messages.password' => 'Messages.password',
  'messages.Ex : 5+ Characters' => 'Messages.Ex : 5+ Characters',
  'messages.reset' => 'Messages.reset',
  'messages.save' => 'Messages.save',
  'mailer_name' => 'Mailer name',
  'Ex:' => 'Ex:',
  'host' => 'Host',
  'Ex_:_mail.6am.one' => 'Ex : mail.6am.one',
  'driver' => 'Driver',
  'Ex : smtp' => 'Ex : smtp',
  'port' => 'Port',
  'Ex : 587' => 'Ex : 587',
  'email_id' => 'Email id',
  'encryption' => 'Encryption',
  'Ex : 5+ Characters' => 'Ex : 5+ Characters',
  'a_test_mail_will_be_sent_to_your_email' => 'A test mail will be sent to your email',
  'email_configuration_error' => 'Email configuration error',
  'email_configured_perfectly!' => 'Email configured perfectly!',
  'email_status_is_not_active' => 'Email status is not active',
  'invalid_email_address' => 'Invalid email address',
  'mail' => 'Mail',
  'send_mail' => 'Send mail',
  'Congratulations! Your SMTP mail has been setup successfully!' => 'Congratulations! Your SMTP mail has been setup successfully!',
  'Go to test mail to check that its work perfectly or not!' => 'Go to test mail to check that its work perfectly or not!',
  'Email Verification' => 'Email Verification',
  'mail_received_successfully' => 'Mail received successfully',
  'Contact Us' => 'Contact Us',
  'Customer Self Delete' => 'Customer Self Delete',
  'When this field is active customer can delete account' => 'When this field is active customer can delete account',
  'Agent Self Delete' => 'Agent Self Delete',
  'When this field is active agent can delete account' => 'When this field is active agent can delete account',
  'Any question or remarks? Just write us a message! or just reach out to us!' => 'Any question or remarks  Just write us a message! or just reach out to us!',
  'My Email' => 'My Email',
  'Call Me Now' => 'Call Me Now',
  'contact Us' => 'Contact Us',
  'Contact us' => 'Contact us',
  'contact_us' => 'Contact us',
  'Contact us updated!' => 'Contact us updated!',
  'contact_us_section' => 'Contact us section',
  'Subject' => 'Subject',
  'Message' => 'Message',
  'Name is required!' => 'Name is required!',
  'Email is required!' => 'Email is required!',
  'Must be a valid email!' => 'Must be a valid email!',
  'Message is required!' => 'Message is required!',
  'Subject is required!' => 'Subject is required!',
  'Thanks_for_your_enquiry._We_will_get_back_to_you_soon.' => 'Thanks for your enquiry. We will get back to you soon.',
  'About Us' => 'About Us',
  'Terms and Condition' => 'Terms and Condition',
  'ABout Us Image' => 'ABout Us Image',
  'About Us Image' => 'About Us Image',
  'Help_&_Support' => 'Help & Support',
  'Contact_messages' => 'Contact messages',
  'Contact Message' => 'Contact Message',
  'Contact Message Table' => 'Contact Message Table',
  'Not_replied_Yet' => 'Not replied Yet',
  'Contact message delete successfully' => 'Contact message delete successfully',
  'Are you sure delete this message' => 'Are you sure delete this message',
  'Contact Message View' => 'Contact Message View',
  'messages.Contact_Message' => 'Messages.Contact Message',
  'messages.Status' => 'Messages.Status',
  'messages.Name' => 'Messages.Name',
  'messages.Email' => 'Messages.Email',
  'messages.Message' => 'Messages.Message',
  'Sent_a_reply' => 'Sent a reply',
  'Mail_Body' => 'Mail Body',
  'Please_send_a_Feedback' => 'Please send a Feedback',
  'Contact_Message' => 'Contact Message',
  'messages.Reply_form_6Cash' => 'Messages.Reply form 6Cash',
  'messages.Dear' => 'Messages.Dear',
  'messages.Something_went_wrong_please_check_your_mail_config' => 'Messages.Something went wrong please check your mail config',
  'Something_went_wrong_please_check_your_mail_config' => 'Something went wrong please check your mail config',
  'Reply_form_6Cash' => 'Reply form 6Cash',
  'Dear' => 'Dear',
  'All_copy_right_reserved' => 'All copy right reserved',
  'Mail_sent_successfully' => 'Mail sent successfully',
  'Seen' => 'Seen',
  'Reply' => 'Reply',
  'Join Now' => 'Join Now',
  'Home' => 'Home',
  'Terms & Condition' => 'Terms & Condition',
  'Home Page' => 'Home Page',
  'Help & Support' => 'Help & Support',
  'Get It Now' => 'Get It Now',
  'business_short_description' => 'Business short description',
  'Quick Links' => 'Quick Links',
  'Pages & Media' => 'Pages & Media',
  'Social Media Links' => 'Social Media Links',
  'Social Media' => 'Social Media',
  'Select Social Media' => 'Select Social Media',
  'Instagram' => 'Instagram',
  'Facebook' => 'Facebook',
  'Twitter' => 'Twitter',
  'LinkedIn' => 'LinkedIn',
  'Pinterest' => 'Pinterest',
  'social_media_link' => 'Social media link',
  'Make_sure_to_include_\'https://\'_to_ensure_correct_functionality.' => 'Make sure to include  https://  to ensure correct functionality.',
  'Ex :facebook.com/your-page-name' => 'Ex :facebook.com/your-page-name',
  'sl' => 'Sl',
  'is_Enabled!' => 'Is Enabled!',
  'is_Disabled!' => 'Is Disabled!',
  'is_enabled_now_everybody_can_use_or_see_this_Social_Medial' => 'Is enabled now everybody can use or see this Social Medial',
  'is_disabled_now_no_one_can_use_or_see_this_Social_Medial' => 'Is disabled now no one can use or see this Social Medial',
  'Social Name Is Requeired' => 'Social Name Is Requeired',
  'Social Link Is Requeired' => 'Social Link Is Requeired',
  'Social Media Already taken' => 'Social Media Already taken',
  'Social Media inserted Successfully' => 'Social Media inserted Successfully',
  'Social info updated Successfully' => 'Social info updated Successfully',
  'Are you sure delete this social media' => 'Are you sure delete this social media',
  'Social media deleted Successfully' => 'Social media deleted Successfully',
  'facebook' => 'Facebook',
  'messages.is_Disabled!' => 'Messages.is Disabled!',
  'instagram' => 'Instagram',
  'algeria (+213)' => 'Algeria (+213)',
  'OTP_Verification' => 'OTP Verification',
  'resend_code_within' => 'Resend code within',
  'resend_OTP' => 'Resend OTP',
  'verify' => 'Verify',
  'Registration_Successful !' => 'Registration Successful !',
  'Intro_Section' => 'Intro Section',
  'Agent Reg.' => 'Agent Reg.',
  'app_Download' => 'App Download',
  'header_Intro_Section' => 'Header Intro Section',
  'section_Title' => 'Section Title',
  'section_Content' => 'Section Content',
  'Icon / Image ' => 'Icon / Image ',
  '(175 x 385 px)' => '(175 x 385 px)',
  'image_Section' => 'Image Section',
  'Min Size for Better Resolution 300x620 px' => 'Min Size for Better Resolution 300x620 px',
  'Image format : jpg, png, jpeg | Maximum size : 5MB' => 'Image format : jpg  png  jpeg | Maximum size : 5MB',
  '(1:1)' => '(1:1)',
  '1:1' => '1:1',
  'play_Store_Link' => 'Play Store Link',
  'app_Store_Link' => 'App Store Link',
  'Download App' => 'Download App',
  'Total Ratings' => 'Total Ratings',
  'Total Count' => 'Total Count',
  'Ex: Very Good Company' => 'Ex: Very Good Company',
  'Reviewer Name' => 'Reviewer Name',
  'Sub_title' => 'Sub title',
  'button_name' => 'Button name',
  'image Section' => 'Image Section',
  'User 1 Image' => 'User 1 Image',
  'User 2 Image' => 'User 2 Image',
  'User 3 Image' => 'User 3 Image',
  'Landing_Page_Setup' => 'Landing Page Setup',
  '3:1' => '3:1',
  'Icon / Image' => 'Icon / Image',
  'This number is already used in another account' => 'This number is already used in another account',
  'Otp Failed!' => 'Otp Failed!',
  'Successfully store!' => 'Successfully store!',
  'Otp not found!' => 'Otp not found!',
  'Your account has been created. Please Download the agent
                                        app and login to your account and complete the verification process
                                        to enjoy all the features .' => 'Your account has been created. Please Download the agent
                                        app and login to your account and complete the verification process
                                        to enjoy all the features .',
  'An OTP has been sent to' => 'An OTP has been sent to',
  'your phone number' => 'Your phone number',
  'Please enter the OTP in the field below to verify your email' => 'Please enter the OTP in the field below to verify your email',
  'Your account has been created. Please Download the agent
                app and login to your account and complete the verification process
                to enjoy all the features .' => 'Your account has been created. Please Download the agent
                app and login to your account and complete the verification process
                to enjoy all the features .',
  'This phone number is already taken.' => 'This phone number is already taken.',
  'Select social media' => 'Select social media',
  'The first name field is required.' => 'The first name field is required.',
  'The last name field is required.' => 'The last name field is required.',
  'Password must contain 4 characters.' => 'Password must contain 4 characters.',
  'linkedin' => 'Linkedin',
  'pinterest' => 'Pinterest',
  'Not Editable' => 'Not Editable',
  'Background Image' => 'Background Image',
  '5:1' => '5:1',
  'landing_page_logo' => 'Landing page logo',
  'Min Size for Better Resolution 200x434 px' => 'Min Size for Better Resolution 200x434 px',
  '---Select Social Media---' => '---Select Social Media---',
  'twitter' => 'Twitter',
  '1. To include a text  color just use  ## around the text ## you want to use background colour -- **6cash**' => '1. To include a text  color just use  ## around the text ## you want to use background colour -- **6cash**',
  '2. To include a text  background just use  ## around the text ## you want to use background colour -- ##6cash##' => '2. To include a text  background just use  ## around the text ## you want to use background colour -- ##6cash##',
  '3. If you want to break the line just use from where you want to break -- %%6cash%%' => '3. If you want to break the line just use from where you want to break -- %%6cash%%',
  '4. To include a text bold just use from where you want to bold -- @6cash@@' => '4. To include a text bold just use from where you want to bold -- @6cash@@',
  'Secure And Convenient Digital Payments' => 'Secure And Convenient Digital Payments',
  'For Title and Headline' => 'For Title and Headline',
  '1. To include a text  color just use  ** around the text ** you want to use background colour' => '1. To include a text  color just use  ** around the text ** you want to use background colour',
  '2. To include a text  background just use  ## around the text ## you want to use background colour' => '2. To include a text  background just use  ## around the text ## you want to use background colour',
  '3. If you want to break the line just use %%6cash%% from where you want to break' => '3. If you want to break the line just use %%6cash%% from where you want to break',
  '1. To include a text color just use ** around the text ** you want to use background colour' => '1. To include a text color just use ** around the text ** you want to use background colour',
  '2. To include a text background just use ## around the text ## you want to use background colour' => '2. To include a text background just use ## around the text ## you want to use background colour',
  '3. If you want to break the line just use %% from where you want to break' => '3. If you want to break the line just use %% from where you want to break',
  '4. To include a text bold just use @@ around the text @@ you want to use bold' => '4. To include a text bold just use @@ around the text @@ you want to use bold',
  '1. To include a text color just use  ** around the text **  you want to use background colour' => '1. To include a text color just use  ** around the text **  you want to use background colour',
  '2. To include a text background just use  ## around the text ##  you want to use background colour' => '2. To include a text background just use  ## around the text ##  you want to use background colour',
  '3. To include a text bold just use  @@ around the text @@  you want to use bold' => '3. To include a text bold just use  @@ around the text @@  you want to use bold',
  '4. If you want to break the line just use  %%  from where you want to break' => '4. If you want to break the line just use  %%  from where you want to break',
  '1. To include a text color just use  ** around the text **  you want to use colour' => '1. To include a text color just use  ** around the text **  you want to use colour',
  'get the latest' => 'Get the latest',
  'offers delivered to your inbox' => 'Offers delivered to your inbox',
  'Welcome to 6Cash 1' => 'Welcome to 6Cash 1',
  '6Cash 1 is a secured and user-friendly digital wallet' => '6Cash 1 is a secured and user-friendly digital wallet',
  'welcome_to' => 'Welcome to',
  'admin_panel' => 'Admin panel',
  'After purchasing 6cash ' => 'After purchasing 6cash ',
  'from codecanyon. You will find a file download option.' => 'From codecanyon. You will find a file download option.',
  'After purchasing ' => 'After purchasing ',
  'invalid_file!' => 'Invalid file!',
  'Codecanyon' => 'Codecanyon',
  'usename' => 'Usename',
  'Ex:_Riad_Uddin' => 'Ex: Riad Uddin',
  'Purchase' => 'Purchase',
  'Ex: 987652' => 'Ex: 987652',
  'Activate' => 'Activate',
  'MethodName' => 'MethodName',
  'Admin updated successfully!' => 'Admin updated successfully!',
  'recaptcha' => 'Recaptcha',
  'Current Balance' => 'Current Balance',
  'Pending Balance' => 'Pending Balance',
  'Roket' => 'Roket',
  'Bkash' => 'Bkash',
  'account_number' => 'Account number',
  '' => '',
  'gif' => 'Gif',
  'Overall Statistics' => 'Overall Statistics',
  'Today s Statistics' => 'Today s Statistics',
  'This Month s Statistics' => 'This Month s Statistics',
  'Save changes' => 'Save changes',
  'New password' => 'New password',
  'Confirm password' => 'Confirm password',
  'Optional' => 'Optional',
  'Save Change' => 'Save Change',
  'Loading' => 'Loading',
  'Registration' => 'Registration',
  'Opening a new agent account is simple ! Join as agent in' => 'Opening a new agent account is simple ! Join as agent in',
  'loader' => 'Loader',
  'business_setup' => 'Business setup',
  'update_option_is_disable_for_demo' => 'Update option is disable for demo',
  'App theme Updated Successfully' => 'App theme Updated Successfully',
  'favicon' => 'Favicon',
  'notes' => 'Notes',
  'about' => 'About',
  'Notes' => 'Notes',
  'Section View' => 'Section View',
  'media' => 'Media',
  'social' => 'Social',
  'Update' => 'Update',
  'want_to_deletec_this' => 'Want to deletec this',
  '6Cashh is a secured and user-friendly digital wallet' => '6Cashh is a secured and user-friendly digital wallet',
  'earned_money' => 'Earned money',
  'Welcome to 6Cashh' => 'Welcome to 6Cashh',
  'unused_balance' => 'Unused balance',
  'used_balance' => 'Used balance',
  'analytics' => 'Analytics',
  'emoney' => 'Emoney',
  'Amount is required.' => 'Amount is required.',
  'Amount must be a valid number with up to 12 digits before the decimal point and up to 2 digits after the decimal point.' => 'Amount must be a valid number with up to 12 digits before the decimal point and up to 2 digits after the decimal point.',
  'total_transaction' => 'Total transaction',
  'total_user' => 'Total user',
  'total_expense' => 'Total expense',
  'Expense Transaction' => 'Expense Transaction',
  'request_money' => 'Request money',
  'dollar' => 'Dollar',
  'Not available' => 'Not available',
  'generated_money' => 'Generated money',
  'UserEmail' => 'UserEmail',
  'UserPhone' => 'UserPhone',
  'UserName' => 'UserName',
  'withdrawal_method' => 'Withdrawal method',
  'shift' => 'Shift',
  'zip' => 'Zip',
  'addon-setting' => 'Addon-setting',
  'business-setup' => 'Business-setup',
  'updatebonus' => 'Updatebonus',
  'update bonus' => 'Update bonus',
  'not_available' => 'Not available',
  'agent image' => 'Agent image',
  'mail-box' => 'Mail-box',
  'identification_image' => 'Identification image',
  'customer_image' => 'Customer image',
  'Password must contain 8 characters' => 'Password must contain 8 characters',
  'business_analytics' => 'Business analytics',
  'email_address' => 'Email address',
  'phone_number' => 'Phone number',
  '6abCash is a secured and user-friendly digital wallet' => '6abCash is a secured and user-friendly digital wallet',
  'Welcome to 6abCash' => 'Welcome to 6abCash',
  'Offline panyment' => 'Offline panyment',
  'Excel' => 'Excel',
  'merchant_panel' => 'Merchant panel',
  'Amount must be a positive number with up to two decimal places' => 'Amount must be a positive number with up to two decimal places',
  'Customer status updated!' => 'Customer status updated!',
  '6cash Software Update' => '6cash Software Update',
  'Note: Update can take more than 5 minutes. Please do not close the window.' => 'Note: Update can take more than 5 minutes. Please do not close the window.',
  'Codecanyon Username' => 'Codecanyon Username',
  'Purchase Code' => 'Purchase Code',
  'All Rights Reserved' => 'All Rights Reserved',
  'Welcome to ZSS Technologies Limited' => 'Welcome to ZSS Technologies Limited',
  'ZSS Technologies Limited is a secured and user-friendly digital wallet' => 'ZSS Technologies Limited is a secured and user-friendly digital wallet',
  '6Cash Software Installation' => '6Cash Software Installation',
  'Please proceed step by step with proper data according to instructions' => 'Please proceed step by step with proper data according to instructions',
  'Before starting the installation process please collect this
                information. Without this information, you will not be able to complete the installation process' => 'Before starting the installation process please collect this
                information. Without this information  you will not be able to complete the installation process',
  'RequiredDatabase Information' => 'RequiredDatabase Information',
  'Where to get this information' => 'Where to get this information',
  'database' => 'Database',
  'Database Name' => 'Database Name',
  'Database Password' => 'Database Password',
  'Database Username' => 'Database Username',
  'Database Host Name' => 'Database Host Name',
  'Are you ready to start installation process' => 'Are you ready to start installation process',
  'Get Started' => 'Get Started',
  'First Step' => 'First Step',
  'Read Documentation' => 'Read Documentation',
  'Step 1' => 'Step 1',
  'Check & Verify File Permissions' => 'Check & Verify File Permissions',
  'Required Database Information' => 'Required Database Information',
  'PHP Version' => 'PHP Version',
  'Curl Enabled' => 'Curl Enabled',
  '.env File Permission' => '.env File Permission',
  'RouteServiceProvider.php File Permission' => 'RouteServiceProvider.php File Permission',
  'All the permissions are provided successfully' => 'All the permissions are provided successfully',
  'Proceed Next' => 'Proceed Next',
  'Step 2' => 'Step 2',
  'Update Purchase Information' => 'Update Purchase Information',
  'Provide your' => 'Provide your',
  'username of codecanyon' => 'Username of codecanyon',
  'purchase code' => 'Purchase code',
  'Username' => 'Username',
  'Continue' => 'Continue',
  'Enabled' => 'Enabled',
  'curl' => 'Curl',
  'bcmath' => 'Bcmath',
  'ctype' => 'Ctype',
  'json' => 'Json',
  'mbstring' => 'Mbstring',
  'openssl' => 'Openssl',
  'pdo' => 'Pdo',
  'tokenizer' => 'Tokenizer',
  'xml' => 'Xml',
  'fileinfo' => 'Fileinfo',
  'gd' => 'Gd',
  'sodium' => 'Sodium',
  'db_file_write_perm' => 'Db file write perm',
  'routes_file_write_perm' => 'Routes file write perm',
  'Step 3' => 'Step 3',
  'Update Database Information' => 'Update Database Information',
  'Provide your database information' => 'Provide your database information',
  'Database Host' => 'Database Host',
  'Step 4' => 'Step 4',
  'Import Database' => 'Import Database',
  'Your Database has been connected ! Just click on the section to automatically import database' => 'Your Database has been connected ! Just click on the section to automatically import database',
  'Click Here' => 'Click Here',
  'Your database is not clean' => 'Your database is not clean',
  'do you want to clean database then import' => 'Do you want to clean database then import',
  'Force Import Database' => 'Force Import Database',
  'Step 5' => 'Step 5',
  'Admin Account Settings' => 'Admin Account Settings',
  'These information will be used to create' => 'These information will be used to create',
  'admin credential' => 'Admin credential',
  'for your admin panel' => 'For your admin panel',
  'Confirm Password' => 'Confirm Password',
  'Complete Installation' => 'Complete Installation',
  'All Done' => 'All Done',
  'Great Job' => 'Great Job',
  'Your software is ready to run' => 'Your software is ready to run',
  'Configure the following setting to run the system properly' => 'Configure the following setting to run the system properly',
  'Business Setting' => 'Business Setting',
  'Mail Setting' => 'Mail Setting',
  'Payment Method Configuration' => 'Payment Method Configuration',
  'SMS Module Configuration' => 'SMS Module Configuration',
  '3rd Party APIs' => '3rd Party APIs',
  'Landing Page' => 'Landing Page',
  'Admin Panel' => 'Admin Panel',
  'Ex: your message' => 'Ex: your message',
  'service_file_content' => 'Service file content',
  'Important Notice: We’re upgrading our push notification system to a new and improved version as the old one will be phased out by June 2024.
                                        Make sure your app is up-to-date to keep getting all the latest updates seamlessly.
                                        Thanks for staying connected!.' => 'Important Notice: We’re upgrading our push notification system to a new and improved version as the old one will be phased out by June 2024.
                                        Make sure your app is up-to-date to keep getting all the latest updates seamlessly.
                                        Thanks for staying connected!.',
  'Important Notice: We’re upgraded the Firebase push notification system to a new and improved version as the old one will be phased out by June 2024.
                                        Make sure your app is up-to-date to keep getting all the latest updates seamlessly.
                                        Thanks for staying connected!.' => 'Important Notice: We’re upgraded the Firebase push notification system to a new and improved version as the old one will be phased out by June 2024.
                                        Make sure your app is up-to-date to keep getting all the latest updates seamlessly.
                                        Thanks for staying connected!.',
  'Important Notice: We’re upgraded the Firebase push notification system to a new and improved version as the old one will be phased out by June 2024.
                                        Make sure your system is up-to-date to keep getting all the notification seamlessly please do check the Notification settings in the Admin panel.
                                        Thanks for staying connected!.' => 'Important Notice: We’re upgraded the Firebase push notification system to a new and improved version as the old one will be phased out by June 2024.
                                        Make sure your system is up-to-date to keep getting all the notification seamlessly please do check the Notification settings in the Admin panel.
                                        Thanks for staying connected!.',
  'Important Notice: We’ve upgraded the Firebase push notification system to a new and improved version as the old one will be phased out by June 2024.
                                        Make sure your system is up-to-date to keep getting all the notification seamlessly please do check the Notification settings in the Admin panel.
                                        Thanks for staying connected!.' => 'Important Notice: We’ve upgraded the Firebase push notification system to a new and improved version as the old one will be phased out by June 2024.
                                        Make sure your system is up-to-date to keep getting all the notification seamlessly please do check the Notification settings in the Admin panel.
                                        Thanks for staying connected!.',
  'kindly follow the documentation' => 'Kindly follow the documentation',
  'select and copy all the service file content and add here' => 'Select and copy all the service file content and add here',
  'nav_menu' => 'Nav menu',
  'Basic information' => 'Basic information',
  'Full name' => 'Full name',
  'Change your password' => 'Change your password',
  'Save Changes' => 'Save Changes',
  'Want to update the admin information ?' => 'Want to update the admin information  ',
  'Want to update the password ?' => 'Want to update the password  ',
  'Learn More' => 'Learn More',
  'app screenshot' => 'App screenshot',
  'reviewer' => 'Reviewer',
  'Are you sure delete this Bonus' => 'Are you sure delete this Bonus',
  'Bonus deleted successfully' => 'Bonus deleted successfully',
);