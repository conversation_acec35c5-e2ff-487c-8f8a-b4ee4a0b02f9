{"__meta": {"id": "Xeaa6e66f8bc1a16a82d848a1096fff8d", "datetime": "2025-07-07 14:27:32", "utime": **********.406847, "method": "GET", "uri": "/public/assets/installation/assets/img/svg-icons/database-name.svg", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876851.983663, "end": **********.406864, "duration": 0.4232008457183838, "duration_str": "423ms", "measures": [{"label": "Booting", "start": 1751876851.983663, "relative_start": 0, "end": **********.358167, "relative_end": **********.358167, "duration": 0.37450385093688965, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.35818, "relative_start": 0.*****************, "end": **********.406866, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "48.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/img/svg-icons/database-name.svg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/img/svg-icons/database-name.svg", "status_code": "<pre class=sf-dump id=sf-dump-675083405 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-675083405\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-612447913 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-612447913\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-22491142 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-22491142\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-242830509 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImsrOUNkcFQwbzdNaDl1NmxxVGRxb3c9PSIsInZhbHVlIjoibTFJa1BNL1lHSnlsUnRZOXFyM1ZDVWNhdGExNmU1dzNNYm91SjNjd0QrMnVRdXZTU3cvdXVBa1piSWlCOUQ3NkllUGNERll1UUVBdXJQd0J4V254MzFWQytaRVFnOVJpb1hjSDhwYWJVNjA5Wk5SY0tsYTZjTWFLSFQyQU9CTUgiLCJtYWMiOiI0MWE2ZmQwMTIwZDE2ZGQ4ODczNDQ3ZDM1ZmU4OTg3ZGUyZGRiYWU4ZmQyYWVhMWY2OTFlNmRmMWI3YzdlZGQ4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5Pb3dWbVFRMS9MSWJiOWM4eGRRZ1E9PSIsInZhbHVlIjoibW4yMXRxazFXWklqN1AzWFY5ek40QjQxclhtM2dpNEt5M09uMDlxUDRuTFhMNW81bXVFcDNQN1pBRkxNUGtUR0YvWWZLQmxwcnhMS3d3NVJselFpbWlhZU5hU0RqVjBTNEh5NzlIYnpndk0zK3BiendkQjJZd0hlZG9IQnhrRVEiLCJtYWMiOiJkYTkxOTQzOGYxMDYxMmFlZDFlZmY5OWM5Y2M0NWExNTZlOTY5ZTRiN2FlZGQyMzY3MmI4MGQxMWExMTNjNWZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-242830509\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2003262449 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003262449\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2035562201 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Iktkd0J6NWVtdXllWVhuQzNDckZJSVE9PSIsInZhbHVlIjoiTXA3V1RHRThTOUZTTFhiZSt2UE1UY2NCd2JHcHAxQXQ0Y3B5c1FFZGxiczVWRXBtZmI5ZDJ6d3VWd0hKL1ZlSWRlQWh4RkF4S2toSlZWaW5iT3MzVS9CeXpsVnhvM2lkcnFPMTFNODFWSWJmdGNmek50cDg3WU16WHZjLy9OTE8iLCJtYWMiOiI5OWRiMGNkNTJhNzZiNmE1YjA0ZDk1MzNlYWIzNTJiZmY1ODI1ZDRlYzAzNjA0MjEwMzA0YzJjZTEyOTBmMThhIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlZTeGwyMVdvM3F1aTI5ZkwzS1B6Mmc9PSIsInZhbHVlIjoiMVBZd1hYWGV2U0E2RzFvUTF6STdLRWZHWloyRWs2eHRxV0R6c3hKWEhGRE9ZMHZwcnkxbnZKdGFSUk9QN1VVRXhTSW9qL00xakNkTHE4cGpJZ1BFZTdTWVhTL0Q3b0N2QytCdE1LVGQzZERiVHFYcnJSS0tqSE1qM3VqUnkvZlUiLCJtYWMiOiJkMDJmY2FmNGE2Y2FjYzZjZDA2NzE3NWNhMjMwMjU3ZDZkZjkzNzEzMGE1YTkzMzYyOWJkODQ0YWY4MTNhMzcwIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Iktkd0J6NWVtdXllWVhuQzNDckZJSVE9PSIsInZhbHVlIjoiTXA3V1RHRThTOUZTTFhiZSt2UE1UY2NCd2JHcHAxQXQ0Y3B5c1FFZGxiczVWRXBtZmI5ZDJ6d3VWd0hKL1ZlSWRlQWh4RkF4S2toSlZWaW5iT3MzVS9CeXpsVnhvM2lkcnFPMTFNODFWSWJmdGNmek50cDg3WU16WHZjLy9OTE8iLCJtYWMiOiI5OWRiMGNkNTJhNzZiNmE1YjA0ZDk1MzNlYWIzNTJiZmY1ODI1ZDRlYzAzNjA0MjEwMzA0YzJjZTEyOTBmMThhIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlZTeGwyMVdvM3F1aTI5ZkwzS1B6Mmc9PSIsInZhbHVlIjoiMVBZd1hYWGV2U0E2RzFvUTF6STdLRWZHWloyRWs2eHRxV0R6c3hKWEhGRE9ZMHZwcnkxbnZKdGFSUk9QN1VVRXhTSW9qL00xakNkTHE4cGpJZ1BFZTdTWVhTL0Q3b0N2QytCdE1LVGQzZERiVHFYcnJSS0tqSE1qM3VqUnkvZlUiLCJtYWMiOiJkMDJmY2FmNGE2Y2FjYzZjZDA2NzE3NWNhMjMwMjU3ZDZkZjkzNzEzMGE1YTkzMzYyOWJkODQ0YWY4MTNhMzcwIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035562201\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1953706668 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"87 characters\">http://127.0.0.1:8000/public/assets/installation/assets/img/svg-icons/database-name.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953706668\", {\"maxDepth\":0})</script>\n"}}