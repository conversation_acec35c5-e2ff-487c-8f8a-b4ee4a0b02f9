/*
	*******************
	Template name:  6valley Multipurpose Theme
	Version:        1.0
	Author:         6amtech
	Author url:     https://6amtech.com/

	NOTE:
	-----
	Please DO NOT EDIT THIS CSS, you may need to use "custom.css" file for writing your custom css.
	We may release future updates so it will overwrite this file. it's better and safer to use "custom.css".

    ******** CSS INDEX ********
    01. Base
        1.1 Typography
        1.2 Spacing
        1.3 Color
    02. Components
        2.1 Helper
        2.2 Animation
        2.3 Social List
        2.4 Inputs
        2.5 Buttons
        2.6 Widget
        2.7 Pagination
        2.8 Customize
        2.9 Preloader
        2.10 Card
        2.11 Common
        2.12 BackToTop
        2.13 Order Timeline
    03. Layout
        3.1 Header
        3.2 Footer
        3.3 Aside
        3.4 Banner
        3.5 Theme Switcher
        3.6 RTL
        3.7 Filter Aside
        3.8 Login
    04. Theme
        4.1 Dark Theme
    05. Shop
        5.1 Product
        5.2 Profile
        5.3 Cart
        5.4 Chat
    ********************/
:root {
    --bs-primary: #5BE0A0;
    --bs-primary-rgb: 91, 224, 160;
    --bs-secondary: #003E47;
    --bs-secondary-rgb: 0, 62, 71;
    --primary-light: #F6F9FD;
    --title-color: #174061;
    --title-color-rgb: 23, 64, 97;
    --bs-body-color: #174061;
    --secondary-body-color: #D9D9D9;
    --bs-light: #F6F6F6;
    --bs-light-rgb: 246, 246, 246;
    --absolute-dark: #262D34;
    --absolute-dark-rgb: 38, 45, 52;
    --absolute-white: #fff;
    --absolute-white-rgb: 255, 255, 255;
    --bs-body-bg-rgb: 253, 253, 253;
    --bs-body-bg: #FDFDFD;
    --bs-border-color: #E3ECF6;
    --bs-border-rgb: 227, 236, 246;
    --bs-success-rgb: 0, 169, 66;
    --bs-success: #00A942;
    --bs-dark-rgb: var(--title-color-rgb);
    --bs-dark: var(--title-color);
    --footer-bg: #0B3668;
    --feature-bg-color: #F2F4F8;
    --box-shadow: 0 0.125rem .6rem rgba(0, 0, 0, .1);
    --product-shadow: 0 0 .5rem rgba(0, 0, 0, 0.05);
    --bs-body-font-family: 'Rubik', sans-serif;
    --title-font: 'Rubik', sans-serif;
    --bs-body-font-weight: 400;
    --thin: 100;
    --extra-light: 200;
    --light: 300;
    --regular: 400;
    --medium: 500;
    --bold: 700;
    --semi-bold: 600;
    --extra-bold: 800;
    --black-bold: 900;
    --bs-body-font-size: clamp(0.75rem, 0.7rem + 0.25vw, 1rem);
    --small-font-size: clamp(0.625rem, 0.6rem + 0.125vw, 0.75rem);
    --h1_fs: clamp(2.25rem, 1.825rem + 2.125vw, 4.375rem);
    --h2_fs: clamp(1.375rem, 1.275rem + 0.5vw, 1.875rem);
    --h3_fs: clamp(1.125rem, 1.1rem + 0.125vw, 1.25rem);
    --h4_fs: clamp(1rem, 0.975rem + 0.125vw, 1.125rem);
    --h5_fs: clamp(0.875rem, 0.85rem + 0.125vw, 1rem);
    --h6_fs: clamp(0.75rem, 0.725rem + 0.125vw, 0.875rem);
    --bs-body-line-height: 1.6;
    --title-line-height: 1.3;
}

/* ************************
   01.1: Reset
   ********************* */
* {
    outline: none !important;
}

html {
    font-family: sans-serif;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    accent-color: var(--bs-primary);
}

body {
    --bs-body-text-align: start;
    -webkit-font-smoothing: antialiased;
    overflow-x: hidden !important;
}

[tabindex="-1"]:focus {
    outline: 0 !important;
}

label {
    text-transform: capitalize;
    color: var(--title-color);
}

h1, .h1,
h2, .h2,
h3, .h3,
h4, .h4,
h5, .h5,
h6, .h6 {
    margin: 0;
    font-weight: var(--bold);
    line-height: var(--title-line-height);
    font-family: var(--title-font);
    color: var(--title-color);
}

h1, .h1 {
    font-size: var(--h1_fs);
}

h2, .h2 {
    font-size: var(--h2_fs);
}

h3, .h3 {
    font-size: var(--h3_fs);
}

h4, .h4 {
    font-size: var(--h4_fs);
}

h5, .h5 {
    font-size: var(--h5_fs);
}

h6, .h6 {
    font-size: var(--h6_fs);
}

p:last-child {
    margin-bottom: 0rem;
}

p:empty {
    margin-bottom: 0rem;
}

a {
    color: var(--title-color);
    text-decoration: none;
}

a:hover {
    color: var(--bs-primary);
}

button {
    color: var(--title-color);
}

[disabled] {
    pointer-events: none;
    opacity: 0.6;
}

textarea {
    overflow: auto;
    resize: vertical;
}

[type="checkbox"] {
    -webkit-margin-end: 0.3125rem;
    margin-inline-end: 0.3125rem;
}

::-moz-selection {
    text-shadow: none;
    color: var(--absolute-white);
    background-color: var(--bs-primary) !important;
}

::selection {
    text-shadow: none;
    color: var(--absolute-white);
    background-color: var(--bs-primary) !important;
}

::-webkit-input-placeholder {
    color: var(--title-color) !important;
    opacity: 0.4 !important;
}

::-moz-placeholder {
    color: var(--title-color) !important;
    opacity: 0.4 !important;
}

:-ms-input-placeholder {
    color: var(--title-color) !important;
    opacity: 0.4 !important;
}

::-ms-input-placeholder {
    color: var(--title-color) !important;
    opacity: 0.4 !important;
}

::placeholder {
    color: var(--title-color) !important;
    opacity: 0.4 !important;
}

iframe {
    max-width: 100%;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: none !important;
}

img {
    max-inline-size: 100%;
    height: auto;
}

.list-base,
ol {
    -webkit-padding-start: 1.5rem;
    padding-inline-start: 1.5rem;
}

.list-base li:not(:last-child),
ol li:not(:last-child) {
    -webkit-margin-after: 0.5rem;
    margin-block-end: 0.5rem;
}

[data-bs-toggle="tooltip"] {
    cursor: pointer;
}

label {
    -webkit-margin-after: 0.5rem;
    margin-block-end: 0.5rem;
}

/* ************************
   01.2: Padding/Margin
   ********************* */
.pt-100 {
    -webkit-padding-before: 6.25rem;
    padding-block-start: 6.25rem;
}

@media only screen and (max-width: 991px) {
    .pt-100 {
        -webkit-padding-before: 3.75rem;
        padding-block-start: 3.75rem;
    }
}

.pb-100 {
    -webkit-padding-after: 6.25rem;
    padding-block-end: 6.25rem;
}

@media only screen and (max-width: 991px) {
    .pb-100 {
        -webkit-padding-after: 3.75rem;
        padding-block-end: 3.75rem;
    }
}

.mt-n1 {
    -webkit-margin-before: -0.25rem !important;
    margin-block-start: -0.25rem !important;
}

.mb-n1 {
    -webkit-margin-after: -0.25rem !important;
    margin-block-end: -0.25rem !important;
}

.ms-n1 {
    -webkit-margin-start: -0.25rem !important;
    margin-inline-start: -0.25rem !important;
}

.me-n1 {
    -webkit-margin-end: -0.25rem !important;
    margin-inline-end: -0.25rem !important;
}

.mt-n2 {
    -webkit-margin-before: -0.5rem !important;
    margin-block-start: -0.5rem !important;
}

.mb-n2 {
    -webkit-margin-after: -0.5rem !important;
    margin-block-end: -0.5rem !important;
}

.ms-n2 {
    -webkit-margin-start: -0.5rem !important;
    margin-inline-start: -0.5rem !important;
}

.me-n2 {
    -webkit-margin-end: -0.5rem !important;
    margin-inline-end: -0.5rem !important;
}

.mt-n3 {
    -webkit-margin-before: -1rem !important;
    margin-block-start: -1rem !important;
}

.mb-n3 {
    -webkit-margin-after: -1rem !important;
    margin-block-end: -1rem !important;
}

.ms-n3 {
    -webkit-margin-start: -1rem !important;
    margin-inline-start: -1rem !important;
}

.me-n3 {
    -webkit-margin-end: -1rem !important;
    margin-inline-end: -1rem !important;
}

.mt-n4 {
    -webkit-margin-before: -1.5rem !important;
    margin-block-start: -1.5rem !important;
}

.mb-n4 {
    -webkit-margin-after: -1.5rem !important;
    margin-block-end: -1.5rem !important;
}

.ms-n4 {
    -webkit-margin-start: -1.5rem !important;
    margin-inline-start: -1.5rem !important;
}

.me-n4 {
    -webkit-margin-end: -1.5rem !important;
    margin-inline-end: -1.5rem !important;
}

.mt-n5 {
    -webkit-margin-before: -3rem !important;
    margin-block-start: -3rem !important;
}

.mb-n5 {
    -webkit-margin-after: -3rem !important;
    margin-block-end: -3rem !important;
}

.ms-n5 {
    -webkit-margin-start: -3rem !important;
    margin-inline-start: -3rem !important;
}

.me-n5 {
    -webkit-margin-end: -3rem !important;
    margin-inline-end: -3rem !important;
}

/* ************************
   01.3: Color
   ********************* */
.text-color {
    color: var(--bs-body-color) !important;
}

a, .secure-payment-card, .contact-icon, .btn-link, .main-menu .sub-menu, .common-nav li.has-sub-item:after,
.aside-body li.has-sub-item:after {
    -webkit-transition: all 300ms ease-in-out;
    -o-transition: all 300ms ease-in-out;
    transition: all 300ms ease-in-out;
}

.widget ul, .sub-menu, .aside-body .nav li ul {
    -webkit-padding-start: 0 !important;
    padding-inline-start: 0 !important;
    -webkit-margin-after: 0 !important;
    margin-block-end: 0 !important;
    list-style: none;
}

.progress-wrap {
    border-radius: 100% !important;
}

.sub-menu {
    border-radius: 0.25rem !important;
}

.rounded-10 {
    border-radius: 0.625rem !important;
}

.ov-hidden {
    overflow: hidden !important;
}

.ovx-hidden {
    overflow-x: hidden !important;
}

.title-font {
    font-family: var(--title-font) !important;
}

.box-shadow, .sub-menu {
    -webkit-box-shadow: var(--box-shadow) !important;
    box-shadow: var(--box-shadow) !important;
}

.overlay {
    --bg-color: 0, 62, 71;
    --opacity: 0.7;
    position: relative;
    z-index: 1;
}

.overlay:after {
    inline-size: 100%;
    block-size: 100%;
    inset-inline-start: 0;
    inset-block-start: 0;
    position: absolute;
    z-index: -1;
    content: "";
    background-color: rgba(var(--bg-color), var(--opacity));
    border-radius: inherit;
}

.img-fit, .img-fit-contain {
    inline-size: 100%;
    block-size: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    display: block;
}

.btn, .flex-center {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.contact-icon, .grid-center, .progress-wrap {
    display: grid;
    place-items: center;
}

/* ************************
   02.1: Helper Classes
   ********************* */
.section-title {
    -webkit-margin-after: 2.5rem;
    margin-block-end: 2.5rem;
}

@media only screen and (max-width: 575px) {
    .section-title {
        -webkit-margin-after: 1.5rem;
        margin-block-end: 1.5rem;
    }
}

.img-box {
    position: relative;
    z-index: 1;
}

.img-box .bg-circle {
    position: absolute;
    z-index: -1;
    width: 90%;
}

@media only screen and (max-width: 991px) {
    .secure-payment-card {
        background-color: var(--bs-whtie);
        -webkit-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
}

.secure-payment-card:hover, .secure-payment-card.active {
    background-color: var(--bs-whtie);
    -webkit-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.access-arefan-wallet-img {
    -webkit-margin-before: -5rem;
    margin-block-start: -5rem;
    z-index: 1;
    position: relative;
    max-block-size: 300px;
    overflow: hidden;
    object-fit: cover;
}

@media only screen and (max-width: 991px) {
    .access-arefan-wallet-img {
        -webkit-margin-before: -3rem;
        margin-block-start: -3rem;
    }
}

.cta-section {
    padding-block: clamp(3.125rem, 2.125rem + 5vw, 8.125rem);
}

@media only screen and (min-width: 576px) {
    .cta-section {
        background-position: center;
    }
}

.cta-title {
    --responsive-fs: clamp(2rem, 1.525rem + 2.375vw, 4.375rem);
    font-size: var(--responsive-fs);
}

.gentle-wave {
    -webkit-animation: move-forever 10s cubic-bezier(0.55, 0.5, 0.45, 0.5) infinite alternate;
    animation: move-forever 10s cubic-bezier(0.55, 0.5, 0.45, 0.5) infinite alternate;
}

@media only screen and (max-width: 575px) {
    .app-btns img {
        inline-size: 5rem;
    }
}

.testimonial-bottom-svg {
    -webkit-margin-before: -4.5rem;
    margin-block-start: -4.5rem;
    z-index: 0;
}

@media only screen and (max-width: 991px) {
    .testimonial-bottom-svg {
        -webkit-margin-before: -3rem;
        margin-block-start: -3rem;
    }
}

@media only screen and (max-width: 575px) {
    .testimonial-bottom-svg {
        -webkit-margin-before: 0;
        margin-block-start: 0;
    }
}

.screenshot-slider {
    position: relative;
}

.screenshot-slider .swiper-wrapper {
    -webkit-padding-before: 2.1rem;
    padding-block-start: 2.1rem;
}

.screenshot-slider .swiper-slide {
    max-inline-size: 12.5rem;
}

.screenshot-slider .slide-center-frame {
    position: absolute;
    z-index: 2;
    inset-inline-start: 50%;
    inset-block-start: 0;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
}

.screenshot-slider .slide-center-frame img {
    min-inline-size: 250px;
}

.screenshot-slider .swiper-slide {
    -webkit-transition: -webkit-all 300ms ease-in-out;
    transition: -webkit-all 300ms ease-in-out;
    -o-transition: all 300ms ease-in-out;
    transition: all 300ms ease-in-out;
    transition: all 300ms ease-in-out,
    -webkit-all 300ms ease-in-out;
}

.screenshot-slider .swiper-slide {
    -webkit-transform: scale(.9);
    -ms-transform: scale(.9);
    transform: scale(.9);
    opacity: 0.5;
}

.screenshot-slider .swiper-slide-active {
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
    opacity: 1;
}

.page-header {
    -webkit-padding-before: 11.25rem;
    padding-block-start: 11.25rem;
    -webkit-padding-after: 6.25rem;
    padding-block-end: 6.25rem;
}

@media only screen and (max-width: 575px) {
    .page-header {
        -webkit-padding-before: 6.25rem;
        padding-block-start: 6.25rem;
        -webkit-padding-after: 2.5rem;
        padding-block-end: 2.5rem;
    }
}

.contact-icon {
    --size: 5rem;
    inline-size: var(--size);
    block-size: var(--size);
    background-color: rgba(var(--bs-primary-rgb), 0.2);
    color: var(--bs-primary);
}

@media only screen and (max-width: 575px) {
    .contact-icon {
        --size: 4rem;
    }
}

.contact-icon.active, .contact-icon:hover {
    background-color: rgba(var(--bs-primary-rgb), 1);
    color: var(--bs-white);
}

.contact-icon i {
    font-size: calc(var(--size) / 3.5);
}

.page-content h3 {
    -webkit-margin-after: 1rem;
    margin-block-end: 1rem;
}

.page-content p:not(:last-child) {
    -webkit-margin-after: 1.5rem;
    margin-block-end: 1.5rem;
}

.contact-info-box {
    border-radius: 0.625rem 0 0 0.625rem;
}

@media only screen and (max-width: 991px) {
    .contact-info-box {
        border-radius: 0.625rem 0.625rem 0 0;
    }
}

/* Option Select */
/* common List */
/* Product Quantity */
/* Rating Review */
.scroll-slider {
    position: relative;
    overflow: hidden;
    display: block;
    margin: 0;
    padding: 0;
    height: 100vh;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.scroll-wrapper {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
}

.scroll-slide {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    max-width: 100vw;
    overflow: hidden;
}

.scroll-slide:not(:last-child) .row {
    -webkit-padding-end: 10.3125rem;
    padding-inline-end: 10.3125rem;
}

@media only screen and (max-width: 575px) {
    .scroll-slide:not(:last-child) .row {
        padding: 0;
    }
}

/* ************************
   02.2: Animations
   ********************* */
.animated {
    -webkit-animation-duration: 200ms;
    animation-duration: 200ms;
    -webkit-animation-fill-mode: backwards;
    animation-fill-mode: backwards;
}

@-webkit-keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

.fadeIn {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
}

@-webkit-keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -2.5rem, 0);
        transform: translate3d(0, -2.5rem, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
}

@keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -2.5rem, 0);
        transform: translate3d(0, -2.5rem, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
}

.fadeInDown {
    -webkit-animation-name: fadeInDown;
    animation-name: fadeInDown;
}

@-webkit-keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 0.625rem, 0);
        transform: translate3d(0, 0.625rem, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 0.625rem, 0);
        transform: translate3d(0, 0.625rem, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
}

.fadeInUp {
    -webkit-animation-name: fadeInUp;
    animation-name: fadeInUp;
}

@-webkit-keyframes fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translatex(-100px);
        transform: translatex(-100px);
    }
    to {
        opacity: 1;
        -webkit-transform: translatex(0);
        transform: translatex(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translatex(-100px);
        transform: translatex(-100px);
    }
    to {
        opacity: 1;
        -webkit-transform: translatex(0);
        transform: translatex(0);
    }
}

.fadeInLeft {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft;
}

@-webkit-keyframes fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translatex(100px);
        transform: translatex(100px);
    }
    to {
        opacity: 1;
        -webkit-transform: translatex(0);
        transform: translatex(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translatex(100px);
        transform: translatex(100px);
    }
    to {
        opacity: 1;
        -webkit-transform: translatex(0);
        transform: translatex(0);
    }
}

.fadeInRight {
    -webkit-animation-name: fadeInRight;
    animation-name: fadeInRight;
}

@-webkit-keyframes move-forever {
    0% {
        -webkit-transform: translate3d(-90px, 0, 0);
        transform: translate3d(-90px, 0, 0);
    }
    100% {
        -webkit-transform: translate3d(85px, 0, 0);
        transform: translate3d(85px, 0, 0);
    }
}

@keyframes move-forever {
    0% {
        -webkit-transform: translate3d(-90px, 0, 0);
        transform: translate3d(-90px, 0, 0);
    }
    100% {
        -webkit-transform: translate3d(85px, 0, 0);
        transform: translate3d(85px, 0, 0);
    }
}

/* ************************
   03.4: Inputs
   ********************* */
/* form control */
.form-select,
.form-control {
    background-color: var(--bs-white);
    color: var(--title-color);
    block-size: 2.8125rem;
    font-size: 0.875rem;
}

[theme="dark"] .form-select, [theme="dark"]
.form-control {
    border-color: #505050;
}

.form-select::file-selector-button,
.form-control::file-selector-button {
    block-size: 2.6875rem;
}

[theme="dark"] .form-select::file-selector-button, [theme="dark"]
.form-control::file-selector-button {
    background-color: #959595;
}

.form-select:focus, .form-select:active,
.form-control:focus,
.form-control:active {
    border-color: var(--bs-primary);
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: var(--bs-white);
    color: var(--title-color);
}

.form-select--sm,
.form-control--sm {
    block-size: 1.875rem;
    font-size: 0.75rem;
}

[theme="dark"] .form-select {
    background-blend-mode: screen;
}

textarea.form-control {
    block-size: auto;
}

/* Change the white to any color */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 1.875rem var(--bs-white) inset !important;
    -webkit-text-fill-color: var(--title-color) !important;
}

/* ************************
   02.5: Buttons
   ********************* */
.btn {
    -webkit-column-gap: 0.375rem;
    -moz-column-gap: 0.375rem;
    column-gap: 0.375rem;
    font-weight: var(--semi-bold);
    text-transform: capitalize;
    white-space: nowrap;
    --bs-btn-font-size: .875rem;
    --bs-btn-border-radius: .5rem;
    --bs-btn-padding-x: 1.5rem;
    --bs-btn-padding-y: 0.5rem;
}

.btn:hover, .btn:focus {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}

.btn:hover svg path, .btn:focus svg path {
    fill: var(--absolute-white);
}

.btn-primary {
    --bs-btn-bg: var(--bs-primary);
    --bs-btn-border-color: var(--bs-primary);
    --bs-btn-hover-bg: #53f3a6;
    --bs-btn-hover-border-color: #53f3a6;
    --bs-btn-active-bg: #53f3a6;
    --bs-btn-active-border-color: #53f3a6;
    --bs-btn-disabled-bg: var(--bs-primary);
    --bs-btn-disabled-border-color: var(--bs-primary);
}

.btn-warning {
    --bs-btn-color: var(--title-color);
    --bs-btn-bg: #E0EC53;
    --bs-btn-border-color: #E0EC53;
    --bs-btn-hover-bg: #E0EC73;
    --bs-btn-hover-border-color: #E0EC73;
    --bs-btn-focus-shadow-rgb: 217, 164, 6;
    --bs-btn-active-color: var(--title-color);
    --bs-btn-hover-color: var(--title-color);
    --bs-btn-active-bg: #E0EC73;
    --bs-btn-active-border-color: #E0EC73;
}

.btn-secondary {
    --bs-btn-color: var(--bs-white);
    --bs-btn-bg: var(--bs-secondary);
    --bs-btn-border-color: var(--bs-secondary);
    --bs-btn-disabled-bg: #d3d4d5;
    --bs-btn-disabled-border-color: #d3d4d5;
    --bs-btn-hover-bg: #024e59;
    --bs-btn-hover-border-color: #024e59;
    --bs-btn-hover-color: var(--bs-btn-color);
}

[theme="dark"] .btn-secondary {
    --bs-btn-color: #fff;
    --bs-btn-bg: #676f75;
    --bs-btn-border-color: #676f75;
    --bs-btn-disabled-bg: #676f75;
    --bs-btn-disabled-border-color: #676f75;
}

.btn-outline-primary {
    --bs-btn-color: var(--bs-primary);
    --bs-btn-border-color: var(--bs-primary);
    --bs-btn-hover-bg: var(--bs-primary);
    --bs-btn-hover-border-color: var(--bs-primary);
    --bs-btn-active-bg: var(--bs-primary);
    --bs-btn-active-border-color: var(--bs-primary);
    --bs-btn-disabled-color: var(--bs-primary);
    --bs-btn-disabled-border-color: var(--bs-primary);
}

.btn-block {
    inline-size: 100%;
}

.btn-link {
    text-decoration: none;
    font-weight: var(--semi-bold);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 0.375rem;
    border: none !important;
    background-color: transparent;
    padding: 0rem;
}

.btn-link:hover {
    color: var(--bs-primary);
    gap: 0.75rem;
}

[dir=rtl] .btn-link i {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

.btn-reset {
    border: 0;
    background-color: transparent;
    padding: 0;
}

/* ************************
   02.6: Widget
   ********************* */
.widget:not(:last-child) {
    -webkit-margin-after: 2.5rem;
    margin-block-end: 2.5rem;
}

.widget__title {
    -webkit-margin-after: 1.25rem;
    margin-block-end: 1.25rem;
}

.widget__about {
    max-inline-size: 20rem;
}

.widget__socials {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 1rem;
    --size: 2.5rem;
}

.widget__socials a {
    width: var(--size);
    height: var(--size);
    border-radius: var(--size);
    border: 1px solid var(--bs-white);
    padding: 0.5rem;
    display: grid;
    place-items: center;
    line-height: 1;
}

.widget__socials a:hover {
    background-color: var(--bs-white);
    color: var(--bs-primary);
}

.widget__socials a:hover i {
    color: var(--bs-primary);
}

/* ************************
   02.7: Pagination
   ********************* */
/* ************************
02.8: Customize Bootstrap CSS
********************* */
.swiper-container {
    overflow: hidden;
}

.swiper-pagination-bullet {
    --swiper-pagination-bullet-width: .5rem;
    --swiper-pagination-bullet-height: .25rem;
    border-radius: 50rem;
}

.swiper-pagination-bullet-active {
    --swiper-pagination-bullet-width: 1rem;
}

/* ************************
   02.9: Preloader
   ********************* */
.preloader {
    position: fixed;
    inline-size: 100%;
    block-size: 100%;
    background-color: var(--bs-white);
    z-index: 9999999;
    display: grid;
    place-items: center;
}

.spinner-grow {
    background-color: var(--bs-primary);
}

/* ************************
   02.10: Card
   ********************* */
/* ************************
   02.11: Common
   ********************* */
.overlay.backdrop-blur::after {
    -webkit-backdrop-filter: blur(2.5px);
    backdrop-filter: blur(2.5px);
}

.overlay.rounded::after {
    border-radius: 0.25rem;
}

/* Fonts */
.body-font {
    font-family: var(--bs-body-font-family) !important;
}

.list-separator {
    --px: 1.25rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.list-separator > li {
    padding-inline: var(--px);
}

.list-separator > li:last-child {
    -webkit-padding-end: 0rem;
    padding-inline-end: 0rem;
}

.list-separator > li:not(:last-child) {
    position: relative;
}

.list-separator > li:not(:last-child)::after {
    position: absolute;
    inset-block-start: 50%;
    inset-inline-end: 0;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    inline-size: 0.0625rem;
    block-size: 0.75rem;
    background-color: var(--absolute-white);
    content: "";
}

.custom-scrollbar {
    --size: .4rem;
    --h: 60vh;
    max-block-size: var(--h);
    overflow-y: auto;
    -ms-scroll-snap-type: y mandatory;
    scroll-snap-type: y mandatory;
    overscroll-behavior-y: contain;
    scroll-behavior: smooth;
}

.custom-scrollbar::-webkit-scrollbar {
    inline-size: var(--size);
    background-color: var(--bs-light);
    border-radius: var(--size);
    visibility: hidden;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    border-radius: var(--size);
    -webkit-box-shadow: inset 0 0 var(--size) rgba(var(--title-color-rgb), 0.3);
    background-color: var(--bs-light);
    visibility: hidden;
}

.custom-scrollbar:hover::-webkit-scrollbar-thumb, .custom-scrollbar:hover::-webkit-scrollbar {
    visibility: visible;
}

/* Gutter 60 */
.media {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
}

.media-body {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.fs-10 {
    font-size: 0.625rem !important;
}

.fs-12,
.fs-small {
    font-size: var(--small-font-size);
}

.fs-18 {
    --responsive-fs: clamp(0.875rem, 0.5625rem + 1vw, 1.125rem);
    font-size: var(--responsive-fs) !important;
}

.avatar {
    --size: 2.1875rem;
    block-size: var(--size);
    inline-size: var(--size);
    min-inline-size: var(--size);
    display: grid;
    place-items: center;
    background-color: var(--bs-light);
}

.avatar-sm {
    --size: 1.75rem;
}

.avatar-lg {
    --size: 3rem;
}

.avatar-xxl {
    --size: 5rem;
}

.bg-img {
    background-size: cover;
    background-repeat: no-repeat;
}

.img-fit-contain {
    -o-object-fit: contain;
    object-fit: contain;
}

/* Cursor */
[data-bs-toggle="modal"],
.cursor-pointer {
    cursor: pointer;
}

.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* Font Weight */
.fw-medium {
    font-weight: 500 !important;
}

.fw-extra-bold {
    font-weight: var(--extra-bold) !important;
}

.bg-bottom {
    background-position: left bottom;
}

.bg-center {
    background-position: center center;
}

.bg-contain {
    background-size: contain;
}

.dot {
    --size: .5rem;
    --bg-color: var(--bs-primary);
    background-color: var(--bg-color);
    inline-size: var(--size);
    min-inline-size: var(--size);
    block-size: var(--size);
    border-radius: var(--size);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

/* custom Radio */
input[type=checkbox] {
    --size: 1rem;
    -webkit-appearance: none;
    min-inline-size: var(--size);
    inline-size: var(--size);
    block-size: var(--size);
    border-radius: 0.3125rem;
    outline: none;
    border: 0.125rem solid var(--secondary-body-color);
}

[theme="dark"] input[type=checkbox] {
    border-color: var(--bs-border-color);
}

input[type=checkbox]:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    position: relative;
}

input[type=checkbox]:checked::after {
    content: "";
    inline-size: var(--size);
    block-size: var(--size);
    position: absolute;
    inset-inline-start: -0.125rem;
    inset-block-start: -0.125rem;
    background-image: url("data:image/svg+xml;base64,PHN2ZyBoZWlnaHQ9JzMwMHB4JyB3aWR0aD0nMzAwcHgnICBmaWxsPSIjZmZmZmZmIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgdmVyc2lvbj0iMS4xIiB4PSIwcHgiIHk9IjBweCI+PHRpdGxlPmljb25fYnlfUG9zaGx5YWtvdjEwPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmZmZmIj48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgyNi4wMDAwMDAsIDI2LjAwMDAwMCkiPjxwYXRoIGQ9Ik0xNy45OTk5ODc4LDMyLjQgTDEwLjk5OTk4NzgsMjUuNCBDMTAuMjI2Nzg5MSwyNC42MjY4MDE0IDguOTczMTg2NDQsMjQuNjI2ODAxNCA4LjE5OTk4Nzc5LDI1LjQgTDguMTk5OTg3NzksMjUuNCBDNy40MjY3ODkxNCwyNi4xNzMxOTg2IDcuNDI2Nzg5MTQsMjcuNDI2ODAxNCA4LjE5OTk4Nzc5LDI4LjIgTDE2LjU4NTc3NDIsMzYuNTg1Nzg2NCBDMTcuMzY2ODIyOCwzNy4zNjY4MzUgMTguNjMzMTUyOCwzNy4zNjY4MzUgMTkuNDE0MjAxNCwzNi41ODU3ODY0IEw0MC41OTk5ODc4LDE1LjQgQzQxLjM3MzE4NjQsMTQuNjI2ODAxNCA0MS4zNzMxODY0LDEzLjM3MzE5ODYgNDAuNTk5OTg3OCwxMi42IEw0MC41OTk5ODc4LDEyLjYgQzM5LjgyNjc4OTEsMTEuODI2ODAxNCAzOC41NzMxODY0LDExLjgyNjgwMTQgMzcuNzk5OTg3OCwxMi42IEwxNy45OTk5ODc4LDMyLjQgWiI+PC9wYXRoPjwvZz48L2c+PC9nPjwvc3ZnPg==");
    background-size: calc(var(--size) * 1.8);
    background-repeat: no-repeat;
    background-position: center;
}

/* Switcher */
/* Offcanvas Overlay */
.z-n1 {
    z-index: -1;
}

.z-1 {
    z-index: 1;
}

.max-content {
    inline-size: -webkit-max-content !important;
    inline-size: -moz-max-content !important;
    inline-size: max-content !important;
}

.mn-w {
    --w: 15rem;
    min-inline-size: var(--w);
}

.mx-w {
    --w: 15rem;
    max-inline-size: var(--w);
}

.mx-w-480 {
    max-inline-size: 30rem;
}

.custom-height {
    --h: 3rem;
    block-size: var(--h) !important;
}

/* ************************
   02.12: Back to Top
   ********************* */
.blok:nth-of-type(odd) {
    background-color: white;
}

.blok:nth-of-type(even) {
    background-color: black;
}

@-webkit-keyframes border-transform {
    0%, 100% {
        border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
    }
    14% {
        border-radius: 40% 60% 54% 46% / 49% 60% 40% 51%;
    }
    28% {
        border-radius: 54% 46% 38% 62% / 49% 70% 30% 51%;
    }
    42% {
        border-radius: 61% 39% 55% 45% / 61% 38% 62% 39%;
    }
    56% {
        border-radius: 61% 39% 67% 33% / 70% 50% 50% 30%;
    }
    70% {
        border-radius: 50% 50% 34% 66% / 56% 68% 32% 44%;
    }
    84% {
        border-radius: 46% 54% 50% 50% / 35% 61% 39% 65%;
    }
}

/* #Progress
================================================== */
.progress-wrap {
    position: fixed;
    inset-block-end: 10%;
    inset-inline-end: 4%;
    --size: 2.187rem;
    inline-size: var(--size);
    block-size: var(--size);
    cursor: pointer;
    display: block;
    -webkit-box-shadow: inset 0 0 0 2px rgba(var(--title-color-rgb), 0.1);
    box-shadow: inset 0 0 0 2px rgba(var(--title-color-rgb), 0.1);
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    -webkit-transform: translateY(-150px);
    -ms-transform: translateY(-150px);
    transform: translateY(-150px);
    -webkit-transition: all 200ms linear;
    -o-transition: all 200ms linear;
    transition: all 200ms linear;
}

.progress-wrap.active-progress {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
}

.progress-wrap::before {
    position: absolute;
    font-family: bootstrap-icons;
    content: '\F148';
    text-align: center;
    line-height: var(--size);
    font-size: 1rem;
    background: var(--bs-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    inset-inline-start: 0;
    inset-block-start: 0;
    inline-size: var(--size);
    block-size: var(--size);
    cursor: pointer;
    display: block;
    z-index: 2;
    -webkit-transition: all 200ms linear;
    -o-transition: all 200ms linear;
    transition: all 200ms linear;
}

.progress-wrap svg path {
    fill: none;
}

.progress-wrap svg.progress-circle path {
    stroke: rgba(var(--bs-primary-rgb), 1);
    stroke-width: 4;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-transition: all 200ms linear;
    -o-transition: all 200ms linear;
    transition: all 200ms linear;
}

/* ************************
   03.1: Header
   ********************* */
.header .sticky-bg-area {
    padding-block: .5rem;
}

.header.sticky {
    inline-size: 100%;
}

.header.sticky .sticky-bg-area {
    position: relative;
    inset-block-start: .5rem;
    z-index: 1;
}

@media only screen and (max-width: 991px) {
    .header.sticky .sticky-bg-area {
        padding-inline: 1rem;
    }
}

.header.sticky .sticky-bg-area::after {
    border-radius: 10px;
    background: rgba(0, 62, 71, 0.8);
    -webkit-box-shadow: 5px 20px 60px -5px rgba(29, 142, 91, 0.1);
    box-shadow: 5px 20px 60px -5px rgba(29, 142, 91, 0.1);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    inline-size: calc(100% + 2rem);
    block-size: 100%;
    inset-inline-start: -1rem;
    position: relative;
    z-index: -1;
    content: "";
    inset-block-start: 0;
    position: absolute;
}

@media only screen and (max-width: 991px) {
    .header.sticky .sticky-bg-area::after {
        inline-size: 100%;
        inset-inline-start: 0;
    }
}

.main-menu {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    gap: 1rem;
}

.main-menu > li {
    text-transform: capitalize;
}

.main-menu > li a {
    padding: 0.625rem;
    color: rgba(var(--bs-white-rgb), 0.8);
}

.main-menu > li a.active {
    color: rgba(var(--bs-white-rgb), 1);
    font-weight: 700;
}

.main-menu > li a.btn-outline-light.active {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.main-menu a {
    font-size: 0.875rem;
}

.main-menu .has-sub-item {
    position: relative;
    -webkit-padding-end: 0.8125rem;
    padding-inline-end: 0.8125rem;
}

.main-menu .has-sub-item::after {
    font-family: bootstrap-icons !important;
    content: "\F282";
    font-size: 0.625rem;
    position: absolute;
    inset-block-start: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    inset-inline-end: 0.5625rem;
}

.main-menu .has-sub-item .has-sub-item::after {
    inset-inline-end: 1rem;
}

.main-menu .sub-menu {
    --submenu-opacity: 0;
    inset-block-start: calc(100% + .625rem);
    position: absolute;
    text-transform: capitalize;
    opacity: var(--submenu-opacity);
    visibility: hidden;
    -webkit-transform: translateY(0.3rem);
    -ms-transform: translateY(0.3rem);
    transform: translateY(0.3rem);
}

.main-menu .sub-menu .sub-menu {
    inset-block-start: 0rem;
    inset-inline-start: 100%;
}

.main-menu li:hover > .sub-menu {
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
    visibility: visible;
    --submenu-opacity: 1;
}

.sub-menu {
    --bs-dropdown-min-width: 13.75rem;
    min-inline-size: var(--bs-dropdown-min-width);
    font-size: var(--bs-dropdown-font-size);
    color: var(--bs-dropdown-color);
    background-color: var(--bs-dropdown-bg);
    background-clip: padding-box;
    z-index: 9;
}

@media only screen and (max-width: 1199px) {
    .sticky-menu-toggle {
        display: none !important;
    }
}

/* ************************
   03.2: Footer
   ********************* */
.footer-bottom {
    background-color: #005F6C;
    font-size: 0.875rem;
    padding-block: 1rem;
}

.footer-main {
    padding-block: 15rem 5rem;
    position: relative;
    z-index: 1;
}

@media only screen and (max-width: 991px) {
    .footer-main {
        -webkit-padding-before: 18.75rem;
        padding-block-start: 18.75rem;
        -webkit-padding-after: 4rem;
        padding-block-end: 4rem;
    }
}

@media only screen and (max-width: 767px) {
    .footer-main {
        -webkit-padding-before: 20rem;
        padding-block-start: 20rem;
    }
}

.footer-bg {
    position: absolute;
    z-index: -1;
    inset-block-start: 0;
    inset-inline-start: 0;
    inline-size: 100%;
    block-size: 100%;
}

@media only screen and (max-width: 991px) {
    .footer-bg {
        inset-inline-start: 50%;
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%);
        inline-size: 200%;
    }
}

.footer * {
    color: rgba(var(--absolute-white-rgb), 0.9);
}

.footer * a:hover {
    color: rgba(var(--absolute-white-rgb), 1);
}

.newsletter-area {
    position: relative;
    z-index: 2;
}

@media only screen and (max-width: 991px) {
    .newsletter-area {
        -webkit-margin-after: -6rem;
        margin-block-end: -6rem;
    }
}

@media only screen and (max-width: 575px) {
    .newsletter-area {
        -webkit-margin-after: -10rem;
        margin-block-end: -10rem;
    }
}

/* ************************
   03.3: aside
   ********************* */
.aside {
    --aside-width: 17.5rem;
    position: fixed;
    inset-inline-start: 0rem;
    inset-block-start: 0;
    z-index: 1031;
    -webkit-transition: -webkit-transform 200ms ease;
    transition: -webkit-transform 200ms ease;
    -o-transition: transform 200ms ease;
    transition: transform 200ms ease;
    transition: transform 200ms ease, -webkit-transform 200ms ease;
    /* background-color: var(--bs-white); */
    -webkit-box-shadow: var(--box-shadow);
    /* box-shadow: var(--box-shadow); */
    inline-size: var(--aside-width);
    block-size: 100%;
    -webkit-transform: translateX(-100%);
    -ms-transform: translateX(-100%);
    transform: translateX(-100%);

    /* border-radius: 0px 0px 20px 20px; */
    background-color: rgba(0, 62, 71, 0.80);
    backdrop-filter: blur(20px);
}

.aside * {
    color: var(--bs-white);
}

[dir="rtl"] .aside {
    -webkit-transform: translateX(100%);
    -ms-transform: translateX(100%);
    transform: translateX(100%);
}

.aside .aside-overlay {
    position: fixed;
    inline-size: 100vw;
    -webkit-transition: -webkit-transform 200ms ease;
    transition: -webkit-transform 200ms ease;
    -o-transition: transform 200ms ease;
    transition: transform 200ms ease;
    transition: transform 200ms ease, -webkit-transform 200ms ease;
    block-size: 100%;
    z-index: -1;
    background-color: var(--title-color);
    opacity: 0;
    visibility: hidden;
    inset-inline-start: var(--aside-width);
    inset-block-start: 0;
}

.aside.active {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
    box-shadow: 5px 20px 60px 5px rgba(104, 180, 145, 0.24);
}

.aside.active .aside-overlay {
    opacity: .5;
    visibility: visible;
}

.aside-header {
    padding-inline: 1rem;
    -webkit-padding-before: 1rem;
    padding-block-start: 1rem;
}

.aside-body {
    padding: 1rem;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
}

.aside-body .main-nav > li {
    -webkit-margin-after: 0.625rem;
    margin-block-end: 0.625rem;
}

.aside-body .nav {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.aside-body .nav li > a {
    padding: 0.375rem 0.625rem;
    display: block;
    font-weight: 500;
}

.aside-body .nav li.sub-menu-opened:after {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}

.aside-body .nav li.active > a {
    color: var(--bs-primary);
}

.aside-body .nav li.active:not(.has-sub-item) > a {
    color: var(--bs-primary);
}

.aside-body .nav li ul {
    padding-block: 0.25rem;
}

.aside-body .nav ul {
    padding-block: 0.625rem;
}

.aside-body .nav ul li {
    -webkit-padding-start: 1.5rem;
    padding-inline-start: 1.5rem;
}

.aside-body .nav ul.sub_menu {
    display: none;
}

.aside .custom-scrollbar {
    --h: calc(100vh - 7rem);
}

.common-nav li.has-sub-item,
.aside-body li.has-sub-item {
    position: relative;
    z-index: 1;
}

.common-nav li.has-sub-item:after,
.aside-body li.has-sub-item:after {
    font-family: bootstrap-icons !important;
    content: "\F285";
    position: absolute;
    inset-inline-end: 0.625rem;
    inset-block-start: 0.625rem;
    z-index: -1;
    font-size: 0.625rem;
}

[dir=rtl] .common-nav li.has-sub-item:after, [dir=rtl]
.aside-body li.has-sub-item:after {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

.common-nav li.has-sub-item.sub-menu-opened:after,
.aside-body li.has-sub-item.sub-menu-opened:after {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}

.search-bar {
    background-color: var(--bs-light);
    border-radius: 3.125rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    block-size: 2.5rem;
}

.search-bar input {
    block-size: 2.5rem;
    background-color: transparent !important;
    border: 0;
    padding-inline: 1rem;
}

.search-bar button {
    border: 0;
    background-color: transparent;
    -webkit-padding-end: 1rem;
    padding-inline-end: 1rem;
}

.search-bar.style--two {
    border-radius: 0.25rem;
    background-color: var(--bs-white);
}

.search-bar.style--two button {
    -webkit-padding-start: 1rem;
    padding-inline-start: 1rem;
    -webkit-padding-end: 0rem;
    padding-inline-end: 0rem;
}

/* ************************
   03.4: Banner
   ********************* */
.banner {
    -webkit-padding-before: 12rem;
    padding-block-start: 12rem;
}

@media only screen and (max-width: 991px) {
    .banner {
        -webkit-padding-before: 6rem;
        padding-block-start: 6rem;
    }
}

.banner .btn-primary {
    background-color: rgba(var(--absolute-white-rgb), 0.1);
    border-color: transparent;
}

.banner .btn-primary:hover {
    background-color: var(--bs-primary);
}

.banner-title {
    -webkit-animation: fadeInLeft 1s ease-in-out 1s 1 both;
    animation: fadeInLeft 1s ease-in-out 1s 1 both;
}

.banner-content {
    -webkit-animation: fadeInRight 1s ease-in-out 1s 1 both;
    animation: fadeInRight 1s ease-in-out 1s 1 both;
}

.banner-middle-content {
    -webkit-animation: fadeIn 1s ease-in-out 1.3s 1 both;
    animation: fadeIn 1s ease-in-out 1.3s 1 both;
}

.banner-mobile-frame {
    -webkit-margin-before: 3rem;
    margin-block-start: 3rem;
    -webkit-margin-after: -12rem;
    margin-block-end: -12rem;
    -webkit-animation: fadeInUp 1s ease-in-out 1.8s 1 both;
    animation: fadeInUp 1s ease-in-out 1.8s 1 both;
}

@media only screen and (max-width: 767px) {
    .banner-mobile-frame {
        -webkit-margin-before: 5rem;
        margin-block-start: 5rem;
        -webkit-margin-after: -5rem;
        margin-block-end: -5rem;
    }
}

.banner-mobile-frame > div {
    position: relative;
    z-index: 1;
}

.banner-mobile-frame .ss-img {
    position: absolute;
    z-index: -1;
    inset-inline-start: 6%;
    inset-block-start: 2%;
    inline-size: 90%;
}

.banner-mobile-frame .banner-middle-frame {
    -webkit-margin-before: -8rem;
    margin-block-start: -8rem;
}

@media only screen and (max-width: 767px) {
    .banner-mobile-frame .banner-middle-frame {
        -webkit-margin-before: -3rem;
        margin-block-start: -3rem;
    }
}


/* Additional CSS */
.testimonial-card {
    box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .075) !important;
    transition: all 300ms ease-in-out;
}

.testimonial-card:hover {
    box-shadow: 0 .25rem .5rem rgba(0, 0, 0, .075) !important;
}

.testimonial-card .media img {
    transition: all 300ms ease-in-out;
}

.testimonial-card .media img:hover {
    transform: scale(1.05);
}

.autoWheelSwiper .swiper-slide .how-it-works-img {
    animation: fadeInUp 1s ease-in-out 1s 1 both infinite;
}

.autoWheelSwiper .swiper-slide .how-it-works-img,
.autoWheelSwiper .swiper-slide .how-it-works-content {
    opacity: 0;
    /* transition: all 300ms ease-in-out; */
}

.autoWheelSwiper .swiper-slide-active .how-it-works-img,
.autoWheelSwiper .swiper-slide-active .how-it-works-content {
    -webkit-animation: fadeInLeft 1000ms ease-in-out 100ms 1 both;
    animation: fadeInLeft 1000ms ease-in-out 100ms 1 both;
    opacity: 1;
}

.autoWheelSwiper .swiper-slide-active.swiper-slide-item-1 .how-it-works-img,
.autoWheelSwiper .swiper-slide-active.swiper-slide-item-5 .how-it-works-img,
.autoWheelSwiper .swiper-slide-active.swiper-slide-item-1 .how-it-works-content,
.autoWheelSwiper .swiper-slide-active.swiper-slide-item-5 .how-it-works-content {
    -webkit-animation-name: fadeInRight;
    animation-name: fadeInRight;
}

.autoWheelSwiper .swiper-slide-active.swiper-slide-item-3 .how-it-works-img,
.autoWheelSwiper .swiper-slide-active.swiper-slide-item-7 .how-it-works-img,
.autoWheelSwiper .swiper-slide-active.swiper-slide-item-3 .how-it-works-content,
.autoWheelSwiper .swiper-slide-active.swiper-slide-item-7 .how-it-works-content {
    -webkit-animation-name: fadeInUp;
    animation-name: fadeInUp;
}

@media (min-width: 992px) {
    .autoWheelSwiper .swiper-slide-active.swiper-slide-item-3 .how-it-works-img,
    .autoWheelSwiper .swiper-slide-active.swiper-slide-item-7 .how-it-works-img,
    .autoWheelSwiper .swiper-slide-active.swiper-slide-item-3 .how-it-works-content,
    .autoWheelSwiper .swiper-slide-active.swiper-slide-item-7 .how-it-works-content {
        text-align: center;
        background-position: center center;
    }

    .autoWheelSwiper .swiper-slide-active.swiper-slide-item-7 .row,
    .autoWheelSwiper .swiper-slide-active.swiper-slide-item-3 .row {
        flex-direction: column-reverse !important;
    }

}

.how-it-works-img-wrap {
    -webkit-animation: moveUpDown 3s ease 3s infinite both;
    animation: moveUpDown 3s ease 3s infinite both;
}

@keyframes moveUpDown {
    0% {
        transform: translateY(-.5rem);
    }
    50% {
        transform: translateY(0.5rem);
    }
    100% {
        transform: translateY(-.5rem);
    }
}

/* Option Select */
.option-select-btn {
    display: flex;
    gap: 1rem;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.option-select-btn li {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: .25rem;
}

.option-select-btn li > span {
    font-size: 13px;
}

.option-select-btn label {
    margin: 0;
}

.option-select-btn label span {
    --size: 52px;
    color: #BCC7CF;
    border-radius: 5px;
    text-transform: capitalize;
    padding: .625rem;
    cursor: pointer;
    display: grid;
    inline-size: var(--size);
    block-size: var(--size);
    place-items: center;
    background-color: rgba(188, 199, 207, .2);
}

.option-select-btn label input:checked ~ span {
    color: #fff;
    background-color: var(--bs-primary);
}

.__input-grp-select {
    background: transparent;
    border: 1px solid #e5e5e5;
    width: 100%;
    height: 45px;
    border-radius: 5px;
}

.input-group {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-align: stretch;
    align-items: stretch;
    width: 100%;
}

@media (min-width: 576px) {
    .__input-grp .__input-grp-select {
        width: 100px;
        padding-left: 10px;
        padding-right: 10px;
    }
}


.otp-form .otp-field {
    --size: 3.125rem;
    inline-size: var(--size);
    block-size: var(--size);
    border: none;
    background-color: #F9F9FB;
    text-align: center;
    border-radius: 5px;
    font-weight: 700;
}

[theme="dark"] .otp-form .otp-field {
    color: var(--absolute-white, #fff);
}

.otp-form .otp-field.style--two {
    background-color: var(--bs-light);
    border: 0.0625rem solid transparent;
    border-radius: 0.3125rem;
}

@media only screen and (max-width: 479px) {
    .otp-form .otp-field {
        --size: 2.5rem;
    }
}

.otp-form .otp-field:focus {
    border-color: var(--title-color);
}

.fs-13 {
    font-size: 13px !important;
}

.custom-loader {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: radial-gradient(farthest-side, var(--bs-primary) 95%, #0000) 50% 1px/10px 2px no-repeat,
    radial-gradient(farthest-side, #0000 calc(100% - 3px), #E4E4ED 0);
    animation: rotate 2s infinite linear;
}

@keyframes rotate {
    to {
        transform: rotate(1turn)
    }
}

.__sub-title,
.feature__sub-title {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

.feature__content-box {
    min-height: 161px;
}

@media (min-width: 1200px) {
    .featureSwiper .swiper-wrapper {
        justify-content: center;
    }
}

.input-group-merge {
    position: relative;
}

.input-group-merge .custom-select:not(:last-child), .input-group-merge .form-control:not(:last-child) {
    padding-right: 2.625rem;
    border-top-right-radius: 0.3125rem;
    border-bottom-right-radius: 0.3125rem;
}

.input-group-merge .input-group-append {
    right: 0;
}

.input-group-merge .input-group-append, .input-group-merge .input-group-prepend {
    position: absolute;
    top: 13px;
    bottom: 0;
}

.input-group .input-group-append, .input-group .input-group-prepend {
    z-index: 4;
}

.input-group-merge .input-group-append .input-group-text:last-child {
    border-top-right-radius: 0.3125rem;
    border-bottom-right-radius: 0.3125rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-group-merge .input-group-append .input-group-text {
    padding: 0 0.875rem 0 0;
}

.input-group > .input-group-append > .btn, .input-group > .input-group-append > .input-group-text, .input-group > .input-group-prepend:first-child > .btn:not(:first-child), .input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child), .input-group > .input-group-prepend:not(:first-child) > .btn, .input-group > .input-group-prepend:not(:first-child) > .input-group-text {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-group-merge .input-group-text {
    z-index: 4;
    border: none;
}

.input-group-text {
    border-color: #e7eaf3;
}

.input-group-text {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    padding: 0.54688rem 0.875rem;
    margin-bottom: 0;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.6;
    color: #677788;
    text-align: center;
    white-space: nowrap;
    background-color: transparent;
    border: 0.0625rem solid #e7eaf3;
    border-radius: 0.3125rem;
}

.contain-twoByOne {
    aspect-ratio: 2 / 1;
    object-fit: contain;
}

.screenshot-slider img {
    border-radius: 20px;
}

.bg--light {
    background-color: #F8F8F9 !important;
}

.banking-ss-img {
    border-radius: 0px 0px 10px 10px;
    border: 2px solid rgba(0, 0, 0, 0.44);
    border-top: 0;
}

.featureSwiper .swiper-slide:nth-child(even) .banking-ss-img {
    border-radius: 10px 10px 0px 0px;
    border: 2px solid rgba(0, 0, 0, 0.44);
    border-bottom: 0;
}

.testimonial-content:nth-child(even) {
    margin-left: 60px;
}

.testimonial-content:nth-child(odd) {
    margin-right: 60px;
}

.w-22-rem {
    --w: 22rem;
}

.min-h-500 {
    min-height: 500px;
}

.max-w-410 {
    max-width: 410px;
}

#feature-section {
    transform: translateY(-100px);
}

.star-opacity {
    --bs-bg-opacity: 0.3;
}

.contact-opacity {
    --opacity: .8;
}
