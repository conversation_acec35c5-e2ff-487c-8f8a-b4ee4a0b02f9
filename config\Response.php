<?php

if (!defined('DEFAULT_200')) {
    //default responses
    define('DEFAULT_200', [
        'response_code' => 'default_200',
        'message' => 'successfully fetched data'
    ]);

    define('DEFAULT_204', [
        'response_code' => 'default_204',
        'message' => 'data not found'
    ]);

    define('DEFAULT_400', [
        'response_code' => 'default_400',
        'message' => 'invalid or missing data'
    ]);

    define('DEFAULT_401', [
        'response_code' => 'default_401',
        'message' => 'unauthorized'
    ]);

    define('DEFAULT_403', [
        'response_code' => 'default_403',
        'message' => 'access denied'
    ]);
    define('DEFAULT_404', [
        'response_code' => 'default_404',
        'message' => 'no resource found'
    ]);

    define('DEFAULT_DELETE_200', [
        'response_code' => 'default_delete_200',
        'message' => 'successfully deleted data'
    ]);

    define('DEFAULT_FAIL_200', [
        'response_code' => 'default_fail_200',
        'message' => 'something went wrong'
    ]);


    define('DEFAULT_STORE_200', [
        'response_code' => 'default_store_200',
        'message' => 'successfully added'
    ]);

    define('DEFAULT_UPDATE_200', [
        'response_code' => 'default_update_200',
        'message' => 'successfully updated'
    ]);

    define('DEFAULT_STATUS_UPDATE_200', [
        'response_code' => 'default_status_update_200',
        'message' => 'successfully status updated'
    ]);

    define('TOO_MANY_ATTEMPT_403', [
        'response_code' => 'too_many_attempt_403',
        'message' => 'your api hit limit exceeded, try after a minute.'
    ]);


    define('REGISTRATION_200', [
        'response_code' => 'registration_200',
        'message' => 'successfully registered'
    ]);

    //auth module
    define('AUTH_LOGIN_200', [
        'response_code' => 'auth_login_200',
        'message' => 'successfully logged in'
    ]);

    define('AUTH_LOGOUT_200', [
        'response_code' => 'auth_logout_200',
        'message' => 'successfully logged out'
    ]);

    define('AUTH_LOGIN_401', [
        'response_code' => 'auth_login_401',
        'message' => 'Invalid Credentials'
    ]);

    define('AUTH_LOGIN_403', [
        'response_code' => 'auth_login_403',
        'message' => 'wrong credentials'
    ]);

    define('AUTH_BLOCK_LOGIN_403', [
        'response_code' => 'auth_login_403',
        'message' => 'You have been blocked'
    ]);

    define('AUTH_LOGIN_400', [
        'response_code' => 'auth_login_400',
        'message' => 'Invalid data'
    ]);

    define('AUTH_LOGIN_404', [
        'response_code' => 'auth_login_404',
        'message' => 'User not found'
    ]);
}
