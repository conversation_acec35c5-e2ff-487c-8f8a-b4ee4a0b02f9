{"__meta": {"id": "X317190037a98f150a73cbaa59580b902", "datetime": "2025-07-07 14:48:58", "utime": **********.655152, "method": "POST", "uri": "/api/v1/customer/auth/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.125286, "end": **********.655166, "duration": 0.5298798084259033, "duration_str": "530ms", "measures": [{"label": "Booting", "start": **********.125286, "relative_start": 0, "end": **********.396984, "relative_end": **********.396984, "duration": 0.271697998046875, "duration_str": "272ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.397, "relative_start": 0.2717139720916748, "end": **********.655167, "relative_end": 1.1920928955078125e-06, "duration": 0.258167028427124, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 26125560, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/customer/auth/login", "middleware": "api, deviceVerify", "controller": "App\\Http\\Controllers\\Api\\V1\\LoginController@customerLogin", "namespace": "App\\Http\\Controllers\\Api\\V1\\Auth\\Auth", "prefix": "api/v1/customer/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FLoginController.php&line=29\" onclick=\"\">app/Http/Controllers/Api/V1/LoginController.php:29-94</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.021040000000000003, "accumulated_duration_str": "21.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `type` = 2 and `phone` = '+*************' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2", "+*************"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 42}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4434688, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:42", "source": "app/Http/Controllers/Api/V1/LoginController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FLoginController.php&line=42", "ajax": false, "filename": "LoginController.php", "line": "42"}, "connection": "wallet_db", "start_percent": 0, "width_percent": 6.131}, {"sql": "select * from `business_settings` where (`key` = 'temporary_login_block_time') limit 1", "type": "query", "params": [], "bindings": ["temporary_login_block_time"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.448854, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 6.131, "width_percent": 4.42}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 199}, {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 85}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.526083, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "LoginController.php:199", "source": "app/Http/Controllers/Api/V1/LoginController.php:199", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FLoginController.php&line=199", "ajax": false, "filename": "LoginController.php", "line": "199"}, "connection": "wallet_db", "start_percent": 10.551, "width_percent": 0}, {"sql": "update `user_log_histories` set `is_active` = 0, `user_log_histories`.`updated_at` = '2025-07-07 14:48:58' where `user_id` = 2", "type": "query", "params": [], "bindings": ["0", "2025-07-07 14:48:58", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 201}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 85}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.52656, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:201", "source": "app/Http/Controllers/Api/V1/LoginController.php:201", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FLoginController.php&line=201", "ajax": false, "filename": "LoginController.php", "line": "201"}, "connection": "wallet_db", "start_percent": 10.551, "width_percent": 7.272}, {"sql": "insert into `user_log_histories` (`ip_address`, `device_id`, `browser`, `os`, `device_model`, `user_id`, `updated_at`, `created_at`) values ('127.0.0.1', 'test-device-123', 'Mobile App', 'Android', 'Test Device', 2, '2025-07-07 14:48:58', '2025-07-07 14:48:58')", "type": "query", "params": [], "bindings": ["127.0.0.1", "test-device-123", "Mobile App", "Android", "Test Device", "2", "2025-07-07 14:48:58", "2025-07-07 14:48:58"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 203}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 85}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.532827, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:203", "source": "app/Http/Controllers/Api/V1/LoginController.php:203", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FLoginController.php&line=203", "ajax": false, "filename": "LoginController.php", "line": "203"}, "connection": "wallet_db", "start_percent": 17.823, "width_percent": 11.882}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 213}, {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 85}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.546643, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "LoginController.php:213", "source": "app/Http/Controllers/Api/V1/LoginController.php:213", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FLoginController.php&line=213", "ajax": false, "filename": "LoginController.php", "line": "213"}, "connection": "wallet_db", "start_percent": 29.705, "width_percent": 0}, {"sql": "update `users` set `last_active_at` = '2025-07-07 14:48:58', `users`.`updated_at` = '2025-07-07 14:48:58' where `id` = 2", "type": "query", "params": [], "bindings": ["2025-07-07 14:48:58", "2025-07-07 14:48:58", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.547253, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:90", "source": "app/Http/Controllers/Api/V1/LoginController.php:90", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FLoginController.php&line=90", "ajax": false, "filename": "LoginController.php", "line": "90"}, "connection": "wallet_db", "start_percent": 29.705, "width_percent": 8.46}, {"sql": "delete from `oauth_access_tokens` where `oauth_access_tokens`.`user_id` = 2 and `oauth_access_tokens`.`user_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 91}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.557492, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:91", "source": "app/Http/Controllers/Api/V1/LoginController.php:91", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FLoginController.php&line=91", "ajax": false, "filename": "LoginController.php", "line": "91"}, "connection": "wallet_db", "start_percent": 38.165, "width_percent": 16.825}, {"sql": "select exists(select * from `oauth_personal_access_clients`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 121}, {"index": 13, "namespace": null, "name": "vendor/laravel/passport/src/PersonalAccessTokenFactory.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PersonalAccessTokenFactory.php", "line": 74}, {"index": 14, "namespace": null, "name": "vendor/laravel/passport/src/HasApiTokens.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\HasApiTokens.php", "line": 66}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 92}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.582853, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "ClientRepository.php:121", "source": "vendor/laravel/passport/src/ClientRepository.php:121", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClientRepository.php&line=121", "ajax": false, "filename": "ClientRepository.php", "line": "121"}, "connection": "wallet_db", "start_percent": 54.99, "width_percent": 9.078}, {"sql": "select * from `oauth_personal_access_clients` order by `id` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/PersonalAccessTokenFactory.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PersonalAccessTokenFactory.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/HasApiTokens.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\HasApiTokens.php", "line": 66}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 92}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.588807, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ClientRepository.php:125", "source": "vendor/laravel/passport/src/ClientRepository.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClientRepository.php&line=125", "ajax": false, "filename": "ClientRepository.php", "line": "125"}, "connection": "wallet_db", "start_percent": 64.068, "width_percent": 2.376}, {"sql": "select * from `oauth_clients` where `oauth_clients`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/passport/src/PersonalAccessTokenFactory.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PersonalAccessTokenFactory.php", "line": 74}, {"index": 22, "namespace": null, "name": "vendor/laravel/passport/src/HasApiTokens.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\HasApiTokens.php", "line": 66}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 92}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.595285, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ClientRepository.php:125", "source": "vendor/laravel/passport/src/ClientRepository.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClientRepository.php&line=125", "ajax": false, "filename": "ClientRepository.php", "line": "125"}, "connection": "wallet_db", "start_percent": 66.445, "width_percent": 4.183}, {"sql": "select * from `oauth_clients` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Bridge/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Bridge\\ClientRepository.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/league/oauth2-server/src/Grant/AbstractGrant.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\Grant\\AbstractGrant.php", "line": 196}, {"index": 19, "namespace": null, "name": "vendor/laravel/passport/src/Bridge/PersonalAccessGrant.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Bridge\\PersonalAccessGrant.php", "line": 21}], "start": **********.603434, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ClientRepository.php:47", "source": "vendor/laravel/passport/src/ClientRepository.php:47", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClientRepository.php&line=47", "ajax": false, "filename": "ClientRepository.php", "line": "47"}, "connection": "wallet_db", "start_percent": 70.627, "width_percent": 3.089}, {"sql": "select * from `oauth_clients` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Bridge/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Bridge\\ClientRepository.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/league/oauth2-server/src/Grant/AbstractGrant.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\Grant\\AbstractGrant.php", "line": 235}, {"index": 19, "namespace": null, "name": "vendor/league/oauth2-server/src/Grant/AbstractGrant.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\Grant\\AbstractGrant.php", "line": 202}], "start": **********.60849, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "ClientRepository.php:47", "source": "vendor/laravel/passport/src/ClientRepository.php:47", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClientRepository.php&line=47", "ajax": false, "filename": "ClientRepository.php", "line": "47"}, "connection": "wallet_db", "start_percent": 73.717, "width_percent": 5.038}, {"sql": "insert into `oauth_access_tokens` (`id`, `user_id`, `client_id`, `scopes`, `revoked`, `created_at`, `updated_at`, `expires_at`) values ('4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946', 2, '3', '[]', 0, '2025-07-07 14:48:58', '2025-07-07 14:48:58', '2025-07-07 15:28:58')", "type": "query", "params": [], "bindings": ["4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946", "2", "3", "[]", "0", "2025-07-07 14:48:58", "2025-07-07 14:48:58", "2025-07-07 15:28:58"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 17}, {"index": 19, "namespace": null, "name": "vendor/laravel/passport/src/Bridge/AccessTokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Bridge\\AccessTokenRepository.php", "line": 57}, {"index": 20, "namespace": null, "name": "vendor/league/oauth2-server/src/Grant/AbstractGrant.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\Grant\\AbstractGrant.php", "line": 464}, {"index": 21, "namespace": null, "name": "vendor/laravel/passport/src/Bridge/PersonalAccessGrant.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Bridge\\PersonalAccessGrant.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/league/oauth2-server/src/AuthorizationServer.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\AuthorizationServer.php", "line": 201}], "start": **********.615366, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "TokenRepository.php:17", "source": "vendor/laravel/passport/src/TokenRepository.php:17", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FTokenRepository.php&line=17", "ajax": false, "filename": "TokenRepository.php", "line": "17"}, "connection": "wallet_db", "start_percent": 78.755, "width_percent": 10.979}, {"sql": "select * from `oauth_access_tokens` where `id` = '4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946' limit 1", "type": "query", "params": [], "bindings": ["4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/PersonalAccessTokenFactory.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PersonalAccessTokenFactory.php", "line": 131}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/PersonalAccessTokenFactory.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PersonalAccessTokenFactory.php", "line": 77}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/HasApiTokens.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\HasApiTokens.php", "line": 66}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 92}], "start": **********.63437, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "TokenRepository.php:28", "source": "vendor/laravel/passport/src/TokenRepository.php:28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FTokenRepository.php&line=28", "ajax": false, "filename": "TokenRepository.php", "line": "28"}, "connection": "wallet_db", "start_percent": 89.734, "width_percent": 3.042}, {"sql": "update `oauth_access_tokens` set `name` = 'CustomerAuthToken' where `id` = '4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946'", "type": "query", "params": [], "bindings": ["CustomerAuthToken", "4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 78}, {"index": 14, "namespace": null, "name": "vendor/laravel/passport/src/PersonalAccessTokenFactory.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PersonalAccessTokenFactory.php", "line": 78}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/HasApiTokens.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\HasApiTokens.php", "line": 66}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\LoginController.php", "line": 92}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.638987, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "TokenRepository.php:78", "source": "vendor/laravel/passport/src/TokenRepository.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FTokenRepository.php&line=78", "ajax": false, "filename": "TokenRepository.php", "line": "78"}, "connection": "wallet_db", "start_percent": 92.776, "width_percent": 7.224}]}, "models": {"data": {"Laravel\\Passport\\Client": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "Laravel\\Passport\\PersonalAccessClient": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FPersonalAccessClient.php&line=1", "ajax": false, "filename": "PersonalAccessClient.php", "line": "?"}}, "Laravel\\Passport\\Token": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FToken.php&line=1", "ajax": false, "filename": "Token.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/v1/customer/auth/login", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>dial_country_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">+880</span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1234567890</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-989514768 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>device-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">test-device-123</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>browser</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">Mobile App</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>device-model</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Test Device</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>os</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Android</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Dart/2.17 (dart:io)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">93</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expect</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">100-continue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">Keep-Alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-989514768\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-161037616 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-161037616\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1272780653 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:48:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272780653\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2059798996 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2059798996\", {\"maxDepth\":0})</script>\n"}}