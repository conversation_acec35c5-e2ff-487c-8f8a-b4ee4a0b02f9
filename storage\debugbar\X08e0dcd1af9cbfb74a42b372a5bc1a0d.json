{"__meta": {"id": "X08e0dcd1af9cbfb74a42b372a5bc1a0d", "datetime": "2025-07-07 14:45:53", "utime": 1751877953.426031, "method": "POST", "uri": "/api/v1/customer/auth/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751877953.115485, "end": 1751877953.426046, "duration": 0.310560941696167, "duration_str": "311ms", "measures": [{"label": "Booting", "start": 1751877953.115485, "relative_start": 0, "end": 1751877953.376961, "relative_end": 1751877953.376961, "duration": 0.2614760398864746, "duration_str": "261ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751877953.376972, "relative_start": 0.2614870071411133, "end": 1751877953.426048, "relative_end": 2.1457672119140625e-06, "duration": 0.049076080322265625, "duration_str": "49.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24684296, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/customer/auth/login", "middleware": "api, deviceVerify", "controller": "App\\Http\\Controllers\\Api\\V1\\LoginController@customerLogin", "namespace": "App\\Http\\Controllers\\Api\\V1\\Auth\\Auth", "prefix": "api/v1/customer/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FLoginController.php&line=29\" onclick=\"\">app/Http/Controllers/Api/V1/LoginController.php:29-94</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/v1/customer/auth/login", "status_code": "<pre class=sf-dump id=sf-dump-484491955 data-indent-pad=\"  \"><span class=sf-dump-num>400</span>\n</pre><script>Sfdump(\"sf-dump-484491955\", {\"maxDepth\":0})</script>\n", "status_text": "Bad Request", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1206366250 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1206366250\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1232185820 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">+8801234567890</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232185820\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-113259023 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Dart/2.17 (dart:io)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expect</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">100-continue</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113259023\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-85134923 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-85134923\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-747991477 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:45:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">57</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747991477\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1066107228 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1066107228\", {\"maxDepth\":0})</script>\n"}}