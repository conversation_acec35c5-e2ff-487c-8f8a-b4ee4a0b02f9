{"__meta": {"id": "Xc342243ee3efd3e47f0ff29d8a78c58b", "datetime": "2025-07-07 14:27:27", "utime": 1751876847.2673, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876846.906504, "end": 1751876847.267322, "duration": 0.36081814765930176, "duration_str": "361ms", "measures": [{"label": "Booting", "start": 1751876846.906504, "relative_start": 0, "end": 1751876847.132374, "relative_end": 1751876847.132374, "duration": 0.22587013244628906, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876847.132385, "relative_start": 0.22588109970092773, "end": 1751876847.267324, "relative_end": 1.9073486328125e-06, "duration": 0.13493895530700684, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24001992, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876847.163706, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876847.25362, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-666546127 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-666546127\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-752186739 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-752186739\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-800161774 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-800161774\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1866653518 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InAwUFhvVDh3UnFpZGpHLzFCQW1ra0E9PSIsInZhbHVlIjoiNnFhOGhFelhJTFlwcU5xaldkb25KdnlNK25OdlY2TXBkYWFsYzIvbzBZREF0MU9jSEZEWlo3YXZZSjBjeUFFcnhMTVFDdE12NnNwb0hJT0xHYW5XNTF4V0FCQ2prQndIV3ZxUnVBRDZQTkwwdmhLZmYzbDV4OERwYU44RDBVRk8iLCJtYWMiOiI2Mzg3YjQ4YzkzZTQ4OWFjNzYzZTk2MmIwMTI5NGU2OTZlNzkyNjI4MzBkNDhlMDkwZjEzMDY2ZDM0YTJmMzcxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlZVbXNOa2dwaUZtQjZTU2loaG1Senc9PSIsInZhbHVlIjoicFlwd2I3YnFoQXpjbWlBZjVZSzk5S09SSUxiZmJJb3pWWFhFZE9TNkJaemlOVEMzbzVuN2svZXBMejdzNnBqRjhoYUxMUnB2MTJhU2VuNkRNc2pBZTNlZ2N2d2xyZHRGMnlrK01zMGExQytGOWhDMlpMblU5aGZ2U3JLTThaRVoiLCJtYWMiOiIxODkxNzU1NTFiNGM4YzhhOWFjZTQ5NTM1ZjBkYjY4ZDI2YTJjYWRmNDA1YjM2MjRlNGZiZTRiNDVhYzllYjQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866653518\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-497216623 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-497216623\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1065409221 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Inp2QXpmcVZuOXlRQ2tMc1FxZXFmelE9PSIsInZhbHVlIjoiMkRXeWU4d0FWdDc0dndDY1l4bUU1QUFFZ3ZJejdPbWgyVnYwVWJMeHp5dlZsYml0M3E1R1lWVXA3ZzZNTXluUFNnWk5PVlc0d202dmtHMHIxR0o0cGtudExoeURwSklUUHNjMTc0bEtnM3R3U0lOVDFFYlhPbXN3QnNWQk9TWUciLCJtYWMiOiJkN2ZiNjU1MDE3NGQ5YjA0MzEwMTNlMDlhNTI0MTFmNjk1NzllNjc1Yzg4MzgyNWIzOWRkODE4MGRmM2NhZTk3IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:27 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IklWNVB6Y0VmdU1pRE5YLzhHRzJoNkE9PSIsInZhbHVlIjoia1hJWm5OTFhTdFptMDcwaC8xTjJNVXk5ekE2UWJvMnZIM0VZbHdyR3NoQlJFMU8zMWtDb29QaTJJSjBHcnZaV2FDdnB4VjRSeHFzVVlHY3ZwNUZMUjNpa2NHK3E4c1cwem9taFpSVm1JR2lWODR5L3NxdkpMdjVOWUhGNHNvd1IiLCJtYWMiOiIxYzhhNGVjZjlmYmFmZjFiNGM5ZDAyZjdkOWU2Zjk3YjUxZWIyMmRmYjczNDA4NzNiMzI4MGFlMTg0NDcwZTA4IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:27 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Inp2QXpmcVZuOXlRQ2tMc1FxZXFmelE9PSIsInZhbHVlIjoiMkRXeWU4d0FWdDc0dndDY1l4bUU1QUFFZ3ZJejdPbWgyVnYwVWJMeHp5dlZsYml0M3E1R1lWVXA3ZzZNTXluUFNnWk5PVlc0d202dmtHMHIxR0o0cGtudExoeURwSklUUHNjMTc0bEtnM3R3U0lOVDFFYlhPbXN3QnNWQk9TWUciLCJtYWMiOiJkN2ZiNjU1MDE3NGQ5YjA0MzEwMTNlMDlhNTI0MTFmNjk1NzllNjc1Yzg4MzgyNWIzOWRkODE4MGRmM2NhZTk3IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IklWNVB6Y0VmdU1pRE5YLzhHRzJoNkE9PSIsInZhbHVlIjoia1hJWm5OTFhTdFptMDcwaC8xTjJNVXk5ekE2UWJvMnZIM0VZbHdyR3NoQlJFMU8zMWtDb29QaTJJSjBHcnZaV2FDdnB4VjRSeHFzVVlHY3ZwNUZMUjNpa2NHK3E4c1cwem9taFpSVm1JR2lWODR5L3NxdkpMdjVOWUhGNHNvd1IiLCJtYWMiOiIxYzhhNGVjZjlmYmFmZjFiNGM5ZDAyZjdkOWU2Zjk3YjUxZWIyMmRmYjczNDA4NzNiMzI4MGFlMTg0NDcwZTA4IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065409221\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1479045255 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1479045255\", {\"maxDepth\":0})</script>\n"}}