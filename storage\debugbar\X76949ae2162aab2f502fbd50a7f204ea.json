{"__meta": {"id": "X76949ae2162aab2f502fbd50a7f204ea", "datetime": "2025-07-07 14:48:25", "utime": 1751878105.281424, "method": "GET", "uri": "/api/v1/customer/get-customer", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751878104.71563, "end": 1751878105.281441, "duration": 0.5658109188079834, "duration_str": "566ms", "measures": [{"label": "Booting", "start": 1751878104.71563, "relative_start": 0, "end": 1751878105.122426, "relative_end": 1751878105.122426, "duration": 0.4067959785461426, "duration_str": "407ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751878105.122445, "relative_start": 0.4068150520324707, "end": 1751878105.281443, "relative_end": 2.1457672119140625e-06, "duration": 0.1589980125427246, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23892736, "peak_usage_str": "23MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Illuminate\\Http\\Exceptions\\HttpResponseException", "message": "", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "line": 40, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:33</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"36 characters\">app/Http/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">abort</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\JsonResponse\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>JsonResponse</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21734 title=\"3 occurrences\">#1734</a><samp data-depth=4 id=sf-dump-**********-ref21734 class=sf-dump-compact>\n        +<span class=sf-dump-public title=\"Public property\">headers</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ResponseHeaderBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ResponseHeaderBag</span> {<a class=sf-dump-ref>#1735</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">headers</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:48:25 GMT</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n            </samp>]\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">cacheControl</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">computedCacheControl</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>no-cache</span>\" => <span class=sf-dump-const>true</span>\n            \"<span class=sf-dump-key>private</span>\" => <span class=sf-dump-const>true</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">cookies</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">headerNames</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>cache-control</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Cache-Control</span>\"\n            \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Date</span>\"\n            \"<span class=sf-dump-key>content-type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Content-Type</span>\"\n          </samp>]\n        </samp>}\n        #<span class=sf-dump-protected title=\"Protected property\">content</span>: \"<span class=sf-dump-str title=\"58 characters\">{&quot;errors&quot;:[{&quot;code&quot;:&quot;auth-001&quot;,&quot;message&quot;:&quot;Unauthorized.&quot;}]}</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">version</span>: \"<span class=sf-dump-str title=\"3 characters\">1.1</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">statusCode</span>: <span class=sf-dump-num>401</span>\n        #<span class=sf-dump-protected title=\"Protected property\">statusText</span>: \"<span class=sf-dump-str title=\"12 characters\">Unauthorized</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">charset</span>: <span class=sf-dump-const>null</span>\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Response`\">sentHeaders</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n        #<span class=sf-dump-protected title=\"Protected property\">data</span>: \"<span class=sf-dump-str title=\"58 characters\">{&quot;errors&quot;:[{&quot;code&quot;:&quot;auth-001&quot;,&quot;message&quot;:&quot;Unauthorized.&quot;}]}</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">callback</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">encodingOptions</span>: <span class=sf-dump-num>0</span>\n        +<span class=sf-dump-public title=\"Public property\">original</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"8 characters\">auth-001</span>\"\n              \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Unauthorized.</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        +<span class=sf-dump-public title=\"Public property\">exception</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Exceptions\\HttpResponseException\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HttpResponseException</span> {<a class=sf-dump-ref>#1736</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">message</span>: \"\"\n          #<span class=sf-dump-protected title=\"Protected property\">code</span>: <span class=sf-dump-num>0</span>\n          #<span class=sf-dump-protected title=\"Protected property\">file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php\n97 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\helpers.php</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">line</span>: <span class=sf-dump-num>40</span>\n          #<span class=sf-dump-protected title=\"Protected property\">response</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\JsonResponse\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>JsonResponse</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21734 title=\"3 occurrences\">#1734</a>}\n          <span class=sf-dump-meta>trace</span>: {<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php:40\nStack level 34.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\helpers.php:40</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note>abort($code, $message = &#039;&#039;, array $headers = [])</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>if ($code instanceof Response) {</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in abort($code, $message = &#039;&#039;, array $headers = [])\">    throw new HttpResponseException($code);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>} elseif ($code instanceof Responsable) {</span></code>\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\Authenticate.php:20\nStack level 33.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>app\\Http\\Middleware\\Authenticate.php:20</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"App\\Http\\Middleware\\Authenticate-&gt;redirectTo($request)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Authenticate-&gt;redirectTo($request)</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>array_push($errors, [&#039;code&#039; =&gt; &#039;auth-001&#039;, &#039;message&#039; =&gt; &#039;Unauthorized.&#039;]);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in App\\Http\\Middleware\\Authenticate-&gt;redirectTo($request)\">abort(response()-&gt;json([</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>    &#039;errors&#039; =&gt; $errors</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$code</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\JsonResponse\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>JsonResponse</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21734 title=\"3 occurrences\">#1734</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php:83\nStack level 32.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Auth\\Middleware\\Authenticate.php:83</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Auth\\Middleware\\Authenticate-&gt;unauthenticated($request, array $guards)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Authenticate-&gt;unauthenticated($request, array $guards)</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>throw new AuthenticationException(</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Auth\\Middleware\\Authenticate-&gt;unauthenticated($request, array $guards)\">    &#039;Unauthenticated.&#039;, $guards, $this-&gt;redirectTo($request)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>);</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a><samp data-depth=9 id=sf-dump-**********-ref247 class=sf-dump-compact>\n                  +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ParameterBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ParameterBag</span> {<a class=sf-dump-ref>#49</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n                  </samp>}\n                  +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref239 title=\"2 occurrences\">#39</a><samp data-depth=10 id=sf-dump-**********-ref239 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n                  </samp>}\n                  +<span class=sf-dump-public title=\"Public property\">query</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#55</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n                  </samp>}\n                  +<span class=sf-dump-public title=\"Public property\">server</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ServerBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ServerBag</span> {<a class=sf-dump-ref>#51</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:\\laragon\\www\\arefan_wallet_admin\\public</span>\"\n                      \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n                      \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57547</span>\"\n                      \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n                      \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n                      \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n                      \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n                      \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/api/v1/customer/get-customer</span>\"\n                      \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                      \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n                      \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"51 characters\">C:\\laragon\\www\\arefan_wallet_admin\\public\\index.php</span>\"\n                      \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/api/v1/customer/get-customer</span>\"\n                      \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"39 characters\">/index.php/api/v1/customer/get-customer</span>\"\n                      \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Bearer</span>\"\n                      \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n                      \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n                      \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Dart/2.17 (dart:io)</span>\"\n                      \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n                      \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751878104.7156</span>\n                      \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751878104</span>\n                    </samp>]\n                  </samp>}\n                  +<span class=sf-dump-public title=\"Public property\">files</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\FileBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>FileBag</span> {<a class=sf-dump-ref>#52</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n                  </samp>}\n                  +<span class=sf-dump-public title=\"Public property\">cookies</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#50</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n                  </samp>}\n                  +<span class=sf-dump-public title=\"Public property\">headers</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\HeaderBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HeaderBag</span> {<a class=sf-dump-ref>#53</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">headers</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">Bearer</span>\"\n                      </samp>]\n                      \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n                      </samp>]\n                      \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Dart/2.17 (dart:io)</span>\"\n                      </samp>]\n                      \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n                      </samp>]\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">cacheControl</span>: []\n                  </samp>}\n                  #<span class=sf-dump-protected title=\"Protected property\">content</span>: \"\"\n                  #<span class=sf-dump-protected title=\"Protected property\">languages</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">charsets</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">encodings</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">acceptableContentTypes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">pathInfo</span>: \"<span class=sf-dump-str title=\"29 characters\">/api/v1/customer/get-customer</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">requestUri</span>: \"<span class=sf-dump-str title=\"29 characters\">/api/v1/customer/get-customer</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">baseUrl</span>: \"\"\n                  #<span class=sf-dump-protected title=\"Protected property\">basePath</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">method</span>: \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">format</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">session</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">locale</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">preferredFormat</span>: <span class=sf-dump-const>null</span>\n                  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isHostValid</span>: <span class=sf-dump-const>true</span>\n                  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isForwardedValid</span>: <span class=sf-dump-const>true</span>\n                  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isSafeContentPreferred</span>: <span class=sf-dump-const title=\"Uninitialized property\">? bool</span>\n                  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">trustedValuesCache</span>: []\n                  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isIisRewrite</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">json</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref239 title=\"2 occurrences\">#39</a>}\n                  #<span class=sf-dump-protected title=\"Protected property\">convertedFiles</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">userResolver</span>: <span class=sf-dump-note>Closure($guard = null)</span> {<a class=sf-dump-ref>#1658</a><samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Auth\\AuthServiceProvider\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>AuthServiceProvider</span>\"\n                    <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Auth\\AuthServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>AuthServiceProvider</span> {<a class=sf-dump-ref>#150</a> &#8230;}\n                    <span class=sf-dump-meta>use</span>: {<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-meta>$app</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"2 occurrences\">#2</a> &#8230;37}\n                    </samp>}\n                    <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php\n103 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Auth\\AuthServiceProvider.php</span>\"\n                    <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">88 to 90</span>\"\n                  </samp>}\n                  #<span class=sf-dump-protected title=\"Protected property\">routeResolver</span>: <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref>#1665</a><samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Router\n25 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Router</span>\"\n                    <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref230 title=\"3 occurrences\">#30</a> &#8230;}\n                    <span class=sf-dump-meta>use</span>: {<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-meta>$route</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2314 title=\"6 occurrences\">#314</a><samp data-depth=12 id=sf-dump-**********-ref2314 class=sf-dump-compact>\n                        +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"28 characters\">api/v1/customer/get-customer</span>\"\n                        +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                        </samp>]\n                        +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=14 class=sf-dump-compact>\n                            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">api</span>\"\n                            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"12 characters\">deviceVerify</span>\"\n                            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"17 characters\">inactiveAuthCheck</span>\"\n                            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"17 characters\">trackLastActiveAt</span>\"\n                            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">auth:api</span>\"\n                            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">customerAuth</span>\"\n                            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">checkDeviceId</span>\"\n                          </samp>]\n                          \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"76 characters\">App\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController@getCustomer</span>\"\n                          \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"76 characters\">App\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController@getCustomer</span>\"\n                          \"<span class=sf-dump-key>namespace</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Http\\Controllers\\Api\\V1\\Auth</span>\"\n                          \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"15 characters\">api/v1/customer</span>\"\n                          \"<span class=sf-dump-key>where</span>\" => []\n                        </samp>]\n                        +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                        +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-note title=\"App\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Controllers\\Api\\V1\\Customer\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>CustomerAuthController</span> {<a class=sf-dump-ref>#1694</a><samp data-depth=13 class=sf-dump-compact>\n                          #<span class=sf-dump-protected title=\"Protected property\">middleware</span>: []\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController`\">user</span>: <span class=sf-dump-note title=\"App\\Models\\User\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>User</span> {<a class=sf-dump-ref>#1695</a><samp data-depth=14 class=sf-dump-compact>\n                            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                            #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                            +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                            +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>\n                            +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">original</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:17</span> [<samp data-depth=15 class=sf-dump-compact>\n                              \"<span class=sf-dump-key>f_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>l_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>dial_country_code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                              \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                              \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>is_phone_verified</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                              \"<span class=sf-dump-key>is_email_verified</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                              \"<span class=sf-dump-key>last_active_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                              \"<span class=sf-dump-key>unique_id</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>referral_id</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                              \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                              \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">image_fullpath</span>\"\n                              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">identification_image_fullpath</span>\"\n                              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"38 characters\">merchant_identification_image_fullpath</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n                              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">last_active_at</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n                            #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                          </samp>}\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController`\">businessSetting</span>: <span class=sf-dump-note title=\"App\\Models\\BusinessSetting\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>BusinessSetting</span> {<a class=sf-dump-ref>#1698</a><samp data-depth=14 class=sf-dump-compact>\n                            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                            #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                            +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                            +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>\n                            +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">original</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=15 class=sf-dump-compact>\n                              \"<span class=sf-dump-key>key</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                              \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                            </samp>]\n                          </samp>}\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController`\">phoneVerification</span>: <span class=sf-dump-note title=\"App\\Models\\PhoneVerification\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>PhoneVerification</span> {<a class=sf-dump-ref>#1699</a><samp data-depth=14 class=sf-dump-compact>\n                            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                            #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                            +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                            +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>\n                            +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">original</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=15 class=sf-dump-compact>\n                              \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>otp</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                              \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                            </samp>]\n                          </samp>}\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController`\">linkedWebsite</span>: <span class=sf-dump-note title=\"App\\Models\\LinkedWebsite\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>LinkedWebsite</span> {<a class=sf-dump-ref>#1700</a><samp data-depth=14 class=sf-dump-compact>\n                            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                            #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                            +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                            +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>\n                            +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">original</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=15 class=sf-dump-compact>\n                              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                              \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">image_fullpath</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                            </samp>]\n                          </samp>}\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController`\">requestMoney</span>: <span class=sf-dump-note title=\"App\\Models\\RequestMoney\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>RequestMoney</span> {<a class=sf-dump-ref>#1701</a><samp data-depth=14 class=sf-dump-compact>\n                            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                            #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                            +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                            +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>\n                            +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">original</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=15 class=sf-dump-compact>\n                              \"<span class=sf-dump-key>from_user_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                              \"<span class=sf-dump-key>to_user_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                              \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                              \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"7 characters\">float:4</span>\"\n                              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                              \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                            </samp>]\n                          </samp>}\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController`\">purpose</span>: <span class=sf-dump-note title=\"App\\Models\\Purpose\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Purpose</span> {<a class=sf-dump-ref>#1702</a><samp data-depth=14 class=sf-dump-compact>\n                            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                            #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                            +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                            +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>\n                            +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">original</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">logo_fullpath</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                            </samp>]\n                          </samp>}\n                          +<span class=sf-dump-public title=\"Public property\">withdrawRequest</span>: <span class=sf-dump-note title=\"App\\Models\\WithdrawRequest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>WithdrawRequest</span> {<a class=sf-dump-ref>#1703</a><samp data-depth=14 class=sf-dump-compact>\n                            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                            #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                            +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                            +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>\n                            +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">original</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=15 class=sf-dump-compact>\n                              \"<span class=sf-dump-key>withdrawal_method_fields</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                            +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                            #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                            #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">amount</span>\"\n                              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"14 characters\">request_status</span>\"\n                              <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">is_paid</span>\"\n                              <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"11 characters\">sender_note</span>\"\n                              <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">admin_note</span>\"\n                              <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"20 characters\">withdrawal_method_id</span>\"\n                              <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"24 characters\">withdrawal_method_fields</span>\"\n                            </samp>]\n                            #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                            </samp>]\n                          </samp>}\n                        </samp>}\n                        +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">parameters</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                        +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">api</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"12 characters\">deviceVerify</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"17 characters\">inactiveAuthCheck</span>\"\n                          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"17 characters\">trackLastActiveAt</span>\"\n                          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">auth:api</span>\"\n                          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">customerAuth</span>\"\n                          <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">checkDeviceId</span>\"\n                        </samp>]\n                        +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-note title=\"Symfony\\Component\\Routing\\CompiledRoute\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>CompiledRoute</span> {<a class=sf-dump-ref>#1685</a><samp data-depth=13 class=sf-dump-compact>\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">variables</span>: []\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">tokens</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=14 class=sf-dump-compact>\n                            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=15 class=sf-dump-compact>\n                              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">/api/v1/customer/get-customer</span>\"\n                            </samp>]\n                          </samp>]\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">staticPrefix</span>: \"<span class=sf-dump-str title=\"29 characters\">/api/v1/customer/get-customer</span>\"\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">regex</span>: \"<span class=sf-dump-str title=\"37 characters\">{^/api/v1/customer/get\\-customer$}sDu</span>\"\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">pathVariables</span>: []\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostVariables</span>: []\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostRegex</span>: <span class=sf-dump-const>null</span>\n                          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostTokens</span>: []\n                        </samp>}\n                        #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref230 title=\"3 occurrences\">#30</a> &#8230;}\n                        #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"2 occurrences\">#2</a> &#8230;37}\n                        #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                      </samp>}\n                    </samp>}\n                    <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php\n93 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php</span>\"\n                    <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">771 to 771</span>\"\n                  </samp>}\n                  <span class=sf-dump-meta>basePath</span>: \"\"\n                  <span class=sf-dump-meta>format</span>: \"<span class=sf-dump-str title=\"4 characters\">html</span>\"\n                </samp>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php:68\nStack level 31.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Auth\\Middleware\\Authenticate.php:68</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Auth\\Middleware\\Authenticate-&gt;authenticate($request, array $guards)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Authenticate-&gt;authenticate($request, array $guards)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Auth\\Middleware\\Authenticate-&gt;authenticate($request, array $guards)\">    $this-&gt;unauthenticated($request, $guards);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$guards</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php:42\nStack level 30.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Auth\\Middleware\\Authenticate.php:42</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Auth\\Middleware\\Authenticate-&gt;handle($request, Closure $next, ...$guards)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Authenticate-&gt;handle($request, Closure $next, ...$guards)</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Auth\\Middleware\\Authenticate-&gt;handle($request, Closure $next, ...$guards)\">    $this-&gt;authenticate($request, $guards);</span></code>\n              &#8250; \n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$guards</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\nStack level 29.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php:180</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>{closure}</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$next</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21593 title=\"9 occurrences\">#1593</a><samp data-depth=9 id=sf-dump-**********-ref21593 class=sf-dump-compact>\n                  <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n                  <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2146 title=\"7 occurrences\">#146</a> &#8230;}\n                  <span class=sf-dump-meta>use</span>: {<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21595 title=\"2 occurrences\">#1595</a><samp data-depth=11 id=sf-dump-**********-ref21595 class=sf-dump-compact>\n                      <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n                      <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2146 title=\"7 occurrences\">#146</a> &#8230;}\n                      <span class=sf-dump-meta>use</span>: {<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21591 title=\"2 occurrences\">#1591</a><samp data-depth=13 id=sf-dump-**********-ref21591 class=sf-dump-compact>\n                          <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n                          <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2146 title=\"7 occurrences\">#146</a> &#8230;}\n                          <span class=sf-dump-meta>use</span>: {<samp data-depth=14 class=sf-dump-compact>\n                            <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21587 title=\"2 occurrences\">#1587</a><samp data-depth=15 id=sf-dump-**********-ref21587 class=sf-dump-compact>\n                              <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n                              <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2146 title=\"7 occurrences\">#146</a> &#8230;}\n                              <span class=sf-dump-meta>use</span>: {<samp data-depth=16 class=sf-dump-compact>\n                                <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2152 title=\"4 occurrences\">#152</a><samp data-depth=17 id=sf-dump-**********-ref2152 class=sf-dump-compact>\n                                  <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n                                  <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2146 title=\"7 occurrences\">#146</a> &#8230;}\n                                  <span class=sf-dump-meta>use</span>: {<samp data-depth=18 class=sf-dump-compact>\n                                    <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2142 title=\"4 occurrences\">#142</a><samp data-depth=19 id=sf-dump-**********-ref2142 class=sf-dump-compact>\n                                      <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n                                      <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2146 title=\"7 occurrences\">#146</a> &#8230;}\n                                      <span class=sf-dump-meta>use</span>: {<samp data-depth=20 class=sf-dump-compact>\n                                        <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21581 title=\"2 occurrences\">#1581</a> &#8230;5}\n                                        <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n                                      </samp>}\n                                      <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n96 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n                                      <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n                                    </samp>}\n                                    <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n                                  </samp>}\n                                  <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n96 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n                                  <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n                                </samp>}\n                                <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"31 characters\">App\\Http\\Middleware\\TrimStrings</span>\"\n                              </samp>}\n                              <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n96 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n                              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n                            </samp>}\n                            <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n                          </samp>}\n                          <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n96 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n                          <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n                        </samp>}\n                        <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"52 characters\">App\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n                      </samp>}\n                      <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n96 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n                      <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n                    </samp>}\n                    <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"25 characters\">Fruitcake\\Cors\\HandleCors</span>\"\n                  </samp>}\n                  <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n96 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n                  <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n                </samp>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\nStack level 28.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php:116</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline-&gt;then(Closure $destination)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)\">    return $pipeline($this-&gt;passable);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:797\nStack level 27.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php:797</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Routing\\Router-&gt;runRouteWithinStack(Route $route, Request $request)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router-&gt;runRouteWithinStack(Route $route, Request $request)</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>-&gt;through($middleware)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;runRouteWithinStack(Route $route, Request $request)\">-&gt;then(fn ($request) =&gt; $this-&gt;prepareResponse(</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>    $request, $route-&gt;run()</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$destination</span>: <span class=sf-dump-note>Closure($request)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21689 title=\"2 occurrences\">#1689</a><samp data-depth=9 id=sf-dump-**********-ref21689 class=sf-dump-compact>\n                  <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Router\n25 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Router</span>\"\n                  <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref230 title=\"3 occurrences\">#30</a> &#8230;}\n                  <span class=sf-dump-meta>use</span>: {<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-meta>$route</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2314 title=\"6 occurrences\">#314</a>}\n                  </samp>}\n                  <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php\n93 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php</span>\"\n                  <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">797 to 799</span>\"\n                </samp>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:776\nStack level 26.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php:776</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Routing\\Router-&gt;runRoute(Request $request, Route $route)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router-&gt;runRoute(Request $request, Route $route)</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>return $this-&gt;prepareResponse($request,</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;runRoute(Request $request, Route $route)\">    $this-&gt;runRouteWithinStack($route, $request)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>);</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$route</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2314 title=\"6 occurrences\">#314</a>}\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:740\nStack level 25.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php:740</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Routing\\Router-&gt;dispatchToRoute(Request $request)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router-&gt;dispatchToRoute(Request $request)</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;dispatchToRoute(Request $request)\">    return $this-&gt;runRoute($request, $this-&gt;findRoute($request));</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$route</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2314 title=\"6 occurrences\">#314</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:729\nStack level 24.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php:729</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Routing\\Router-&gt;dispatch(Request $request)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router-&gt;dispatch(Request $request)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;dispatch(Request $request)\">    return $this-&gt;dispatchToRoute($request);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:190\nStack level 23.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:190</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;Illuminate\\Foundation\\Http\\{closure}\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Kernel-&gt;Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>{closure}</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;Illuminate\\Foundation\\Http\\{closure}\">    return $this-&gt;router-&gt;dispatch($request);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>};</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\nStack level 22.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php:141</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>{closure}</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>try {</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">    return $destination($passable);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>} catch (Throwable $e) {</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php:66\nStack level 21.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\barryvdh\\laravel-debugbar\\</span>src\\Middleware\\InjectDebugbar.php:66</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Barryvdh\\Debugbar\\Middleware\\InjectDebugbar-&gt;handle($request, Closure $next)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Barryvdh\\Debugbar\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InjectDebugbar-&gt;handle($request, Closure $next)</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>    /** @var \\Illuminate\\Http\\Response $response */</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Barryvdh\\Debugbar\\Middleware\\InjectDebugbar-&gt;handle($request, Closure $next)\">    $response = $next($request);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>} catch (Throwable $e) {</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\nStack level 20.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php:180</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>{closure}</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$next</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21593 title=\"9 occurrences\">#1593</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21\nStack level 19.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>TransformsRequest-&gt;handle($request, Closure $next)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php:31\nStack level 18.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php:31</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull-&gt;handle($request, Closure $next)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ConvertEmptyStringsToNull-&gt;handle($request, Closure $next)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull-&gt;handle($request, Closure $next)\">    return parent::handle($request, $next);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$next</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2142 title=\"4 occurrences\">#142</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\nStack level 17.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php:180</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>{closure}</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$next</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21593 title=\"9 occurrences\">#1593</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21\nStack level 16.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>TransformsRequest-&gt;handle($request, Closure $next)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php:40\nStack level 15.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php:40</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Middleware\\TrimStrings-&gt;handle($request, Closure $next)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>TrimStrings-&gt;handle($request, Closure $next)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\TrimStrings-&gt;handle($request, Closure $next)\">    return parent::handle($request, $next);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$next</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2152 title=\"4 occurrences\">#152</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\nStack level 14.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php:180</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>{closure}</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$next</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21593 title=\"9 occurrences\">#1593</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php:27\nStack level 13.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php:27</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize-&gt;handle($request, Closure $next)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ValidatePostSize-&gt;handle($request, Closure $next)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\nStack level 12.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php:180</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>{closure}</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$next</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21593 title=\"9 occurrences\">#1593</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php:86\nStack level 11.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php:86</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance-&gt;handle($request, Closure $next)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>PreventRequestsDuringMaintenance-&gt;handle($request, Closure $next)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\nStack level 10.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php:180</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>{closure}</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$next</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21593 title=\"9 occurrences\">#1593</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php:52\nStack level 9.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\fruitcake\\laravel-cors\\</span>src\\HandleCors.php:52</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Fruitcake\\Cors\\HandleCors-&gt;handle($request, Closure $next)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Fruitcake\\Cors</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HandleCors-&gt;handle($request, Closure $next)</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>// Handle the request</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Fruitcake\\Cors\\HandleCors-&gt;handle($request, Closure $next)\">$response = $next($request);</span></code>\n              &#8250; \n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\nStack level 8.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php:180</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>{closure}</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$next</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21593 title=\"9 occurrences\">#1593</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php:39\nStack level 7.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Http\\Middleware\\TrustProxies.php:39</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Http\\Middleware\\TrustProxies-&gt;handle(Request $request, Closure $next)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>TrustProxies-&gt;handle(Request $request, Closure $next)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Http\\Middleware\\TrustProxies-&gt;handle(Request $request, Closure $next)\">    return $next($request);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\nStack level 6.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php:180</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>{closure}</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n                <span class=sf-dump-meta>$next</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21593 title=\"9 occurrences\">#1593</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\nStack level 5.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php:116</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline-&gt;then(Closure $destination)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)\">    return $pipeline($this-&gt;passable);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:165\nStack level 4.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:165</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;sendRequestThroughRouter($request)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel-&gt;sendRequestThroughRouter($request)</span> &#8230;\n              &#8250; <code class=\"php\"><span class=sf-dump-default>                -&gt;through($this-&gt;app-&gt;shouldSkipMiddleware() ? [] : $this-&gt;middleware)</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;sendRequestThroughRouter($request)\">                -&gt;then($this-&gt;dispatchToRouter());</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$destination</span>: <span class=sf-dump-note>Closure($request)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21590 title=\"3 occurrences\">#1590</a><samp data-depth=9 id=sf-dump-**********-ref21590 class=sf-dump-compact>\n                  <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"App\\Http\\Kernel\n15 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">App\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Kernel</span>\"\n                  <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"App\\Http\\Kernel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel</span> {<a class=sf-dump-ref>#34</a> &#8230;}\n                  <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php\n101 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php</span>\"\n                  <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">187 to 191</span>\"\n                </samp>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:134\nStack level 3.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php:134</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel-&gt;handle($request)</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)\">    $response = $this-&gt;sendRequestThroughRouter($request);</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>} catch (Throwable $e) {</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\public\\index.php:51\nStack level 2.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>public\\index.php:51</span> {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-note>require_once</span> &#8230;\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in require_once\">$response = $kernel-&gt;handle(</span></code>\n              &#8250; <code class=\"php\"><span class=sf-dump-default>    $request = Request::capture()</span></code>\n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$request</span>: <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n              </samp>}\n            </samp>}\n            <span class=sf-dump-meta title=\"C:\\laragon\\www\\arefan_wallet_admin\\server.php:21\nStack level 1.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>server.php:21</span> {<samp data-depth=7 class=sf-dump-compact>\n              &#8250; \n              &#8250; <code class=\"php\"><span class=sf-dump-const title=\"\">require_once __DIR__.&#039;/public/index.php&#039;;</span></code>\n              &#8250; \n              <span class=sf-dump-meta>arguments</span>: {<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-str title=\"51 characters\">C:\\laragon\\www\\arefan_wallet_admin\\public\\index.php</span>\"\n              </samp>}\n            </samp>}\n          </samp>}\n        </samp>}\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>83</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">redirectTo</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Http\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>68</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">unauthenticated</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">api</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>42</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">api</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref>#1692</a><samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref>#1690</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref>#1693</a> &#8230;}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"51 characters\">Illuminate\\Routing\\Middleware\\ThrottleRequests:60,1</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n96 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\arefan_wallet_admin\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n      </samp>}\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">api</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>797</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>Closure($request)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21689 title=\"2 occurrences\">#1689</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>776</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2314 title=\"6 occurrences\">#314</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>740</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2314 title=\"6 occurrences\">#314</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>729</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21581 title=\"2 occurrences\">#1581</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2142 title=\"4 occurrences\">#142</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2142 title=\"4 occurrences\">#142</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2152 title=\"4 occurrences\">#152</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2152 title=\"4 occurrences\">#152</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21587 title=\"2 occurrences\">#1587</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21591 title=\"2 occurrences\">#1591</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"48 characters\">vendor/fruitcake/laravel-cors/src/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>52</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Fruitcake\\Cors\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21595 title=\"2 occurrences\">#1595</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21593 title=\"9 occurrences\">#1593</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>Closure($request)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21590 title=\"3 occurrences\">#1590</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref247 title=\"58 occurrences\">#47</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"10 characters\">server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">C:\\laragon\\www\\arefan_wallet_admin\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["    function abort($code, $message = '', array $headers = [])\n", "    {\n", "        if ($code instanceof Response) {\n", "            throw new HttpResponseException($code);\n", "        } elseif ($code instanceof Responsable) {\n", "            throw new HttpResponseException($code->toResponse(request()));\n", "        }\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fhelpers.php&line=40", "ajax": false, "filename": "helpers.php", "line": "40"}}]}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/customer/get-customer", "middleware": "api, deviceVerify, inactiveAuthCheck, trackLastActiveAt, auth:api, customerAuth, checkDeviceId", "controller": "App\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController@getCustomer", "namespace": "App\\Http\\Controllers\\Api\\V1\\Auth", "prefix": "api/v1/customer", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FCustomer%2FAuth%2FCustomerAuthController.php&line=369\" onclick=\"\">app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php:369-452</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/v1/customer/get-customer", "status_code": "<pre class=sf-dump id=sf-dump-272900881 data-indent-pad=\"  \"><span class=sf-dump-num>401</span>\n</pre><script>Sfdump(\"sf-dump-272900881\", {\"maxDepth\":0})</script>\n", "status_text": "Unauthorized", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1447319227 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1447319227\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-575725463 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-575725463\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1050802926 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Bearer******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Dart/2.17 (dart:io)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050802926\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-246701570 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-246701570\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1810248308 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:48:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810248308\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-912824605 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-912824605\", {\"maxDepth\":0})</script>\n"}}