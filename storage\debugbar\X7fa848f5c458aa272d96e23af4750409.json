{"__meta": {"id": "X7fa848f5c458aa272d96e23af4750409", "datetime": "2025-07-07 14:27:25", "utime": **********.042142, "method": "GET", "uri": "/public/assets/installation/assets/img/svg-icons/database-username.svg", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876844.785103, "end": **********.042167, "duration": 0.2570638656616211, "duration_str": "257ms", "measures": [{"label": "Booting", "start": 1751876844.785103, "relative_start": 0, "end": **********.012334, "relative_end": **********.012334, "duration": 0.22723102569580078, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.01235, "relative_start": 0.*****************, "end": **********.04217, "relative_end": 3.0994415283203125e-06, "duration": 0.029819965362548828, "duration_str": "29.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/img/svg-icons/database-username.svg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/img/svg-icons/database-username.svg", "status_code": "<pre class=sf-dump id=sf-dump-299856589 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-299856589\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1665057678 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1665057678\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2139530605 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2139530605\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-466617847 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikhldno4SEN1NFZmY1NKYXhrWjNTQUE9PSIsInZhbHVlIjoiZXBLYjhnQzR4TW5TT1V4cWVqdzNIQXRPZ3VBQlAxWndqcTF2emMxRGR0dEc1R00vK3hYYXgwNkpuNjVFMVQyTlhDQVhwQVBEcEVvNVREZm8wWTV1cWVEbGpRSzNnRHI4UmUrWTNvUWJ4YklCcUJNSTZHeXRPc0VZU3JaYmd2eWUiLCJtYWMiOiI3M2YwNzc2ODcwODUxNjFjMmQ4YTJkMzFmMmRlMTM1YzAwYWZhYzFmNWFhYWY5ZTIxYjk5OWMyYjM4NTQwMjYxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imcxd09BSUY0blhxVk9QZGx1RENjcFE9PSIsInZhbHVlIjoidFcyRUtRSEtmdXRzekcyRis3Q01TOFV4NVpPRlE2c2JDa0p6QmlTSVc4OFRQSjdZekRmTWlYZUhBRFpBN0dTdy9RL1lnYzlHMHZac2QwNTNBSDJRNVNRSnJseVlNbncyV05jTGI3RkVrY24renJ1V1A3cnpzejZ6TUFMeUpIeU4iLCJtYWMiOiIyYTEyODBiZjVjNGZiYjU5YzlmZGMyMmZmZDAyYmJiZDQ5NzkzN2I5NzcwYWMyZmU4ZmE4MzFlMThjZmE1OWJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-466617847\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-700413937 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-700413937\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-693802063 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InAwUFhvVDh3UnFpZGpHLzFCQW1ra0E9PSIsInZhbHVlIjoiNnFhOGhFelhJTFlwcU5xaldkb25KdnlNK25OdlY2TXBkYWFsYzIvbzBZREF0MU9jSEZEWlo3YXZZSjBjeUFFcnhMTVFDdE12NnNwb0hJT0xHYW5XNTF4V0FCQ2prQndIV3ZxUnVBRDZQTkwwdmhLZmYzbDV4OERwYU44RDBVRk8iLCJtYWMiOiI2Mzg3YjQ4YzkzZTQ4OWFjNzYzZTk2MmIwMTI5NGU2OTZlNzkyNjI4MzBkNDhlMDkwZjEzMDY2ZDM0YTJmMzcxIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:25 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlZVbXNOa2dwaUZtQjZTU2loaG1Senc9PSIsInZhbHVlIjoicFlwd2I3YnFoQXpjbWlBZjVZSzk5S09SSUxiZmJJb3pWWFhFZE9TNkJaemlOVEMzbzVuN2svZXBMejdzNnBqRjhoYUxMUnB2MTJhU2VuNkRNc2pBZTNlZ2N2d2xyZHRGMnlrK01zMGExQytGOWhDMlpMblU5aGZ2U3JLTThaRVoiLCJtYWMiOiIxODkxNzU1NTFiNGM4YzhhOWFjZTQ5NTM1ZjBkYjY4ZDI2YTJjYWRmNDA1YjM2MjRlNGZiZTRiNDVhYzllYjQwIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:25 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InAwUFhvVDh3UnFpZGpHLzFCQW1ra0E9PSIsInZhbHVlIjoiNnFhOGhFelhJTFlwcU5xaldkb25KdnlNK25OdlY2TXBkYWFsYzIvbzBZREF0MU9jSEZEWlo3YXZZSjBjeUFFcnhMTVFDdE12NnNwb0hJT0xHYW5XNTF4V0FCQ2prQndIV3ZxUnVBRDZQTkwwdmhLZmYzbDV4OERwYU44RDBVRk8iLCJtYWMiOiI2Mzg3YjQ4YzkzZTQ4OWFjNzYzZTk2MmIwMTI5NGU2OTZlNzkyNjI4MzBkNDhlMDkwZjEzMDY2ZDM0YTJmMzcxIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlZVbXNOa2dwaUZtQjZTU2loaG1Senc9PSIsInZhbHVlIjoicFlwd2I3YnFoQXpjbWlBZjVZSzk5S09SSUxiZmJJb3pWWFhFZE9TNkJaemlOVEMzbzVuN2svZXBMejdzNnBqRjhoYUxMUnB2MTJhU2VuNkRNc2pBZTNlZ2N2d2xyZHRGMnlrK01zMGExQytGOWhDMlpMblU5aGZ2U3JLTThaRVoiLCJtYWMiOiIxODkxNzU1NTFiNGM4YzhhOWFjZTQ5NTM1ZjBkYjY4ZDI2YTJjYWRmNDA1YjM2MjRlNGZiZTRiNDVhYzllYjQwIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-693802063\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1314421990 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"91 characters\">http://127.0.0.1:8000/public/assets/installation/assets/img/svg-icons/database-username.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314421990\", {\"maxDepth\":0})</script>\n"}}