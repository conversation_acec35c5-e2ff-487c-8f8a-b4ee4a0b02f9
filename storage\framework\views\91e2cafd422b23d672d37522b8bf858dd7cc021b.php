<div class="card-header">
    <h5 class="card-header-title">
        <?php echo e(translate('Top Transactions')); ?>

    </h5>
    <a href="<?php echo e(route('admin.transaction.index', ['trx_type'=>'all'])); ?>" class="fs-12px"><?php echo e(translate('View All')); ?></a>
</div>

<div class="card-body">
    <?php $__currentLoopData = $top_transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=>$top_transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if(isset($top_transaction->user)): ?>
            <a
                <?php if($top_transaction->user['type']==1): ?>
                    href="<?php echo e(route('admin.agent.view',[$top_transaction->user['id']])); ?>"
                <?php elseif($top_transaction->user['type']==2): ?>
                    href="<?php echo e(route('admin.customer.view',[$top_transaction->user['id']])); ?>"
                <?php elseif($top_transaction->user['type']==3): ?>
                    href="<?php echo e(route('admin.merchant.view',[$top_transaction->user['id']])); ?>"
                <?php endif; ?>
                class="cursor-pointer d-flex justify-content-between gap-2 align-items-center mb-4">
                <div class="d-flex gap-3 align-items-center">
                    <div class="avatar rounded border">
                        <img class="rounded img-fit"
                            src="<?php echo e($top_transaction->user['image_fullpath']); ?>"
                            alt="<?php echo e(translate('image')); ?>">
                    </div>
                    <span class="hover-to-primary">
                        <?php echo e(Str::limit($top_transaction->user->f_name . ' (' . $top_transaction->user->phone . ')', 20)); ?>

                    </span>
                </div>
                <div>
                    <span class="fs-18">
                        <?php echo e(Helpers::set_symbol($top_transaction['total_transaction'])); ?>

                    </span>
                </div>
            </a>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>
<?php /**PATH C:\laragon\www\arefan_wallet_admin\resources\views/admin-views/partials/_top-transactions.blade.php ENDPATH**/ ?>