{"__meta": {"id": "Xdcaae5c1d6502af65eca142b5545a9ff", "datetime": "2025-07-07 14:27:49", "utime": 1751876869.573802, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876869.194816, "end": 1751876869.573819, "duration": 0.37900280952453613, "duration_str": "379ms", "measures": [{"label": "Booting", "start": 1751876869.194816, "relative_start": 0, "end": 1751876869.428023, "relative_end": 1751876869.428023, "duration": 0.23320698738098145, "duration_str": "233ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876869.428037, "relative_start": 0.23322081565856934, "end": 1751876869.573821, "relative_end": 2.1457672119140625e-06, "duration": 0.1457841396331787, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002280, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876869.469588, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876869.561178, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1078563505 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1078563505\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1972805391 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1972805391\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-841469255 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-841469255\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-239482033 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"106 characters\">http://127.0.0.1:8000/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImNTUmJNSk9xVGZhbWtQZ2FxSnFLTVE9PSIsInZhbHVlIjoiWXJxVk5uc0ptMHNCVEdmMEFqUEVDNThrSDlxWmRPcitpc0R3cForTi8yc2xSMDRlU1NveW9jV25hbWNpenNmS3RlTGEwY05Ld3VBblFXbDcrYUtZMnJOL0NGbGdiejkxcVpMeGI1d3Z1djcwS2d1emkwRnNadUJxWk9NY09OUGIiLCJtYWMiOiI1OWIzZjY0MTY0YzMwZWRjMjQzN2Y1ZjRkZWIyY2Q1YmFkYjRlYzA1ZTZkZGMzODk1ZjQxMTYyZTk5NmNlOWM0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkRONHpSdkZ3cXV2MTVEdVpXRG16UUE9PSIsInZhbHVlIjoiUFordmdSUDRsQ25wMC9rc0Z2QnlvMTA3WnU2TEJhYWpFZ1NpU3NXOWFSanlHaHJJVWVlT1hiOWpRYWhYbmN0Z0ZkaFdEeC9pbCsyVDcwU2tnVFhUL2piMzV2ZmZlTkM5OFljNm4zTG44c1NSb0MvR3RRTGZCdmhpbjFWYlBPOUYiLCJtYWMiOiI0MmY2M2QyYTU4NWI3MzQ0MmQ3ZmNiYzBhZjhjNjJjMzA1MmIzYmZkYWY0NDdiMmMxNTkzOGRmYTI5OTFjMmNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-239482033\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-802900038 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802900038\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1499384019 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJIemphb0cvQ0VaNjAya0FTN28xL0E9PSIsInZhbHVlIjoiMDJ3RXlaenJRWEM4QUdUWHhUTEZ2M21WSHl2Wk9meTVSTW5ZUHZST0dlSW5XeitGU3JtNHdkbm1PRytEMkEvb1FsUUZRT2ZoVEsza3ZnNlg3ejZnTE4veXF3WUxwZXU5UldzMVNyTE9Na1Y3cU5HTWJqL05SaC9zTW9YVzBVcWMiLCJtYWMiOiI2YjYyOTYyODQ5NGJmMzk0MzRkYWFjMjYwOWUzYWVmNDA1ODc2ZDZiYTAxMDliZWU3Mjg4YTJkNTE2NDU0MDk0IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImZnQm1CNVRWYWFVeTdqTzlJMVdDNnc9PSIsInZhbHVlIjoiR3duLzVHWTI3c0tYY3V1d2lHUk9TL003N3hkTEJmaXEzRlBjMlA1eGl6MW5hUG9wZnN2SU1GOURmemNFbWREUCtENWo0QjdTSGlYR29HSVRVSEQ4NlBCaVh0R3FUNXpNOWxscmlCSXBLNDREYVBKSktIYTdWRkF3SEpDbSszVUoiLCJtYWMiOiJhMTY2YmZjODAxNTEwYTFjNzNlNjFiYmY4ZDRjMzM4MTUyZTg1MDlmMTdhMzNiYjU3MDBmZWMzOTRjY2NhNjljIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJIemphb0cvQ0VaNjAya0FTN28xL0E9PSIsInZhbHVlIjoiMDJ3RXlaenJRWEM4QUdUWHhUTEZ2M21WSHl2Wk9meTVSTW5ZUHZST0dlSW5XeitGU3JtNHdkbm1PRytEMkEvb1FsUUZRT2ZoVEsza3ZnNlg3ejZnTE4veXF3WUxwZXU5UldzMVNyTE9Na1Y3cU5HTWJqL05SaC9zTW9YVzBVcWMiLCJtYWMiOiI2YjYyOTYyODQ5NGJmMzk0MzRkYWFjMjYwOWUzYWVmNDA1ODc2ZDZiYTAxMDliZWU3Mjg4YTJkNTE2NDU0MDk0IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImZnQm1CNVRWYWFVeTdqTzlJMVdDNnc9PSIsInZhbHVlIjoiR3duLzVHWTI3c0tYY3V1d2lHUk9TL003N3hkTEJmaXEzRlBjMlA1eGl6MW5hUG9wZnN2SU1GOURmemNFbWREUCtENWo0QjdTSGlYR29HSVRVSEQ4NlBCaVh0R3FUNXpNOWxscmlCSXBLNDREYVBKSktIYTdWRkF3SEpDbSszVUoiLCJtYWMiOiJhMTY2YmZjODAxNTEwYTFjNzNlNjFiYmY4ZDRjMzM4MTUyZTg1MDlmMTdhMzNiYjU3MDBmZWMzOTRjY2NhNjljIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499384019\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1087932208 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087932208\", {\"maxDepth\":0})</script>\n"}}