{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0", "ext-json": "*", "brian2694/laravel-toastr": "^5.56", "doctrine/dbal": "^3.3", "fruitcake/laravel-cors": "^2.0", "gregwar/captcha": "^1.1", "guzzlehttp/guzzle": "^7.0.1", "iyzico/iyzipay-php": "^2.0", "kingflamez/laravelrave": "*", "laravel/framework": "^9.0", "laravel/passport": "^10.2", "laravel/sanctum": "^2.11", "laravel/tinker": "^2.5", "laravel/vonage-notification-channel": "^3.0", "laravelpkg/laravelchk": "dev-master", "madnest/madzipper": "*", "mercadopago/dx-php": "2.4.4", "nwidart/laravel-modules": "9.*", "paypal/rest-api-sdk-php": "^1.14", "ramsey/uuid": "^4.2", "rap2hpoutre/fast-excel": "*", "razorpay/razorpay": "^2.8", "simplesoftwareio/simple-qrcode": "^4.2", "stevebauman/location": "^6.5", "stripe/stripe-php": "^7.103", "twilio/sdk": "^6.31", "unicodeveloper/laravel-paystack": "^1.0", "xendit/xendit-php": "^2.19", "ext-openssl": "*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "spatie/laravel-ignition": "^1.0", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10"}, "autoload": {"files": ["app/Lib/Helper.php", "app/Lib/Transaction.php", "app/Lib/Responses.php", "app/Lib/Constant.php", "app/Lib/PaymentResponse.php", "app/Lib/PaymentSuccess.php", "app/CentralLogics/helpers.php", "app/CentralLogics/sms_module.php", "app/CentralLogics/Translation.php", "vendor/laravelpkg/laravelchk/src/Http/Controllers/LaravelchkController.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "Mo<PERSON>les/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}