<?php if($data['business_statistics_section']['status'] == '1'): ?>
    <section class="mt-5 overflow-hidden">
        <div class="container">
            <div class="row align-items-center gy-5 gx-5">
                <div class="col-lg-6">
                    <h2 class="mb-2 mb-sm-4"><?php echo change_text_color_or_bg($data['business_statistics_section']['download_data']['title']); ?></h2>
                    <p><?php echo $data['business_statistics_section']['download_data']['sub_title']; ?></p>

                    <div class="d-flex gap-4 gap-xl-5 mt-4">
                        <div class="d-flex flex-column gap-3">
                            <img width="65"
                                 src="<?php echo e(asset('storage/app/public/landing-page/business-statistics/'.$data['business_statistics_section']['download_data']['download_icon'])); ?>"
                                 alt="">
                            <h2 class="text-primary"><?php echo e(format_number((int)$data['business_statistics_section']['download_data']['download_count'])); ?></h2>
                            <p><?php echo $data['business_statistics_section']['download_data']['download_sort_description']; ?></p>
                        </div>
                        <div class="border-start d-none d-sm-block"></div>
                        <div class="d-flex flex-column gap-3">
                            <img width="65"
                                 src="<?php echo e(asset('storage/app/public/landing-page/business-statistics/'.$data['business_statistics_section']['download_data']['review_icon'])); ?>"
                                 alt="">
                            <h2 class="text-primary"><?php echo e($data['business_statistics_section']['download_data']['review_count']); ?>

                                /5</h2>
                            <p><?php echo $data['business_statistics_section']['download_data']['review_sort_description']; ?></p>
                        </div>
                        <div class="border-start d-none d-sm-block"></div>
                        <div class="d-flex flex-column gap-3">
                            <img width="65"
                                 src="<?php echo e(asset('storage/app/public/landing-page/business-statistics/'.$data['business_statistics_section']['download_data']['country_icon'])); ?>"
                                 alt="">
                            <h2 class="text-primary"><?php echo e(format_number((int)$data['business_statistics_section']['download_data']['country_count'])); ?></h2>
                            <p><?php echo $data['business_statistics_section']['download_data']['country_sort_description']; ?> </p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="d-none d-lg-flex gap-3 flex-column position-relative" data-aos="fade-left"
                         data-aos-duration="1000" data-aos-delay="300">
                        <?php $__currentLoopData = $data['business_statistics_section']['testimonial_data'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="testimonial-card bg-white rounded-10 testimonial-content">
                                <div class="p-4">
                                    <div class="media gap-3">
                                        <img width="90"
                                             src="<?php echo e(asset('storage/app/public/landing-page/testimonial')); ?>/<?php echo e($review['image']); ?>"
                                             class="rounded-circle" alt="">
                                        <div class="media-body">
                                            <div class="star-rating text-warning fs-12 mb-3">
                                                <?php
                                                    $rating = $review['rating'];
                                                    $fullStars = floor($rating);
                                                    $halfStar = $rating - $fullStars;
                                                ?>

                                                <?php for($i = 0; $i < $fullStars; $i++): ?>
                                                    <i class="bi bi-star-fill"></i>
                                                <?php endfor; ?>

                                                <?php if($halfStar > 0): ?>
                                                    <i class="bi bi-star-half"></i>
                                                <?php endif; ?>

                                                <?php for($i = 0; $i < (5 - ceil($rating)); $i++): ?>
                                                    <i class="bi bi-star"></i>
                                                <?php endfor; ?>
                                            </div>

                                            <h6 class="mb-2"><?php echo $review['opinion']; ?></h6>
                                            <p class="fs-12"><span class="text-uppercase"><?php echo e($review['name']); ?></span>
                                                <span class="text-muted">/ <?php echo e($review['user_type']); ?></span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <div class="d-lg-none" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="300">
                        <!-- Swiper -->
                        <div class="swiper" data-swiper-loop="true" data-swiper-margin="16">
                            <div class="swiper-wrapper">
                                <?php $__currentLoopData = $data['business_statistics_section']['testimonial_data'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="swiper-slide">
                                        <div class="bg-white shadow-sm rounded-10 me-sm-5">
                                            <div class="p-4">
                                                <div class="media gap-3">
                                                    <img width="90"
                                                         src="<?php echo e(asset('storage/app/public/landing-page/testimonial')); ?>/<?php echo e($review['image']); ?>"
                                                         class="rounded-circle" alt="">
                                                    <div class="media-body">
                                                        <div class="star-rating text-warning fs-12 mb-3">
                                                            <?php
                                                                $rating = $review['rating']; // Assuming you have a 'rating' column in your database.
                                                                $fullStars = floor($rating);
                                                                $halfStar = $rating - $fullStars;
                                                            ?>

                                                            <?php for($i = 0; $i < $fullStars; $i++): ?>
                                                                <i class="bi bi-star-fill"></i>
                                                            <?php endfor; ?>

                                                            <?php if($halfStar > 0): ?>
                                                                <i class="bi bi-star-half"></i>
                                                            <?php endif; ?>

                                                            <?php for($i = 0; $i < (5 - ceil($rating)); $i++): ?>
                                                                <i class="bi bi-star"></i>
                                                            <?php endfor; ?>
                                                        </div>

                                                        <h6 class="mb-2"><?php echo $review['opinion']; ?></h6>
                                                        <p class="fs-12"><span
                                                                class="text-uppercase"><?php echo e($review['name']); ?></span>
                                                            <span class="text-muted">/ <?php echo e(translate('Agents')); ?></span></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <div class="swiper-pagination position-relative mt-3"></div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <img src="<?php echo e(asset('public/assets/landing/img/media/testimonial-bottom.svg')); ?>"
             class="svg testimonial-bottom-svg w-100" alt="<?php echo e(translate('image')); ?>">
    </section>
<?php endif; ?>
<?php /**PATH C:\laragon\www\arefan_wallet_admin\resources\views/landing/partials/home/<USER>/ ?>