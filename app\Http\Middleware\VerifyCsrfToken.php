<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $except = [
        'get-token',
        'create-payment',
        'execute-payment',
        'query-payment',
        'success',
        'fail',
        'success',
        'pay-paypal',
        'paypal-status',
        'paywithrazorpay',
        'payment-razor'
    ];
}
