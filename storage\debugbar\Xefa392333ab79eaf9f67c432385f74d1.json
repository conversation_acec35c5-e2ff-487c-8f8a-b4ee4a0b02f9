{"__meta": {"id": "Xefa392333ab79eaf9f67c432385f74d1", "datetime": "2025-07-07 14:48:59", "utime": **********.662894, "method": "GET", "uri": "/api/v1/customer/transaction-history", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.260677, "end": **********.662909, "duration": 0.40223193168640137, "duration_str": "402ms", "measures": [{"label": "Booting", "start": **********.260677, "relative_start": 0, "end": **********.534806, "relative_end": **********.534806, "duration": 0.27412891387939453, "duration_str": "274ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.53482, "relative_start": 0.2741429805755615, "end": **********.662911, "relative_end": 1.9073486328125e-06, "duration": 0.12809085845947266, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24629840, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/customer/transaction-history", "middleware": "api, deviceVerify, inactiveAuthCheck, trackLastActiveAt, auth:api, customerAuth, checkDeviceId", "controller": "App\\Http\\Controllers\\Api\\V1\\Customer\\TransactionController@transactionHistory", "namespace": "App\\Http\\Controllers\\Api\\V1\\Auth", "prefix": "api/v1/customer", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FCustomer%2FTransactionController.php&line=401\" onclick=\"\">app/Http/Controllers/Api/V1/Customer/TransactionController.php:401-439</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.008459999999999999, "accumulated_duration_str": "8.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `oauth_access_tokens` where `id` = '4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946' limit 1", "type": "query", "params": [], "bindings": ["4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 100}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Bridge/AccessTokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Bridge\\AccessTokenRepository.php", "line": 88}, {"index": 18, "namespace": null, "name": "vendor/league/oauth2-server/src/AuthorizationValidators/BearerTokenValidator.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/league/oauth2-server/src/ResourceServer.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\ResourceServer.php", "line": 84}], "start": **********.5898209, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "TokenRepository.php:28", "source": "vendor/laravel/passport/src/TokenRepository.php:28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FTokenRepository.php&line=28", "ajax": false, "filename": "TokenRepository.php", "line": "28"}, "connection": "wallet_db", "start_percent": 0, "width_percent": 14.775}, {"sql": "select * from `oauth_access_tokens` where `id` = '4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946' limit 1", "type": "query", "params": [], "bindings": ["4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 100}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Bridge/AccessTokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Bridge\\AccessTokenRepository.php", "line": 88}, {"index": 18, "namespace": null, "name": "vendor/league/oauth2-server/src/AuthorizationValidators/BearerTokenValidator.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/league/oauth2-server/src/ResourceServer.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\ResourceServer.php", "line": 84}], "start": **********.5967321, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "TokenRepository.php:28", "source": "vendor/laravel/passport/src/TokenRepository.php:28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FTokenRepository.php&line=28", "ajax": false, "filename": "TokenRepository.php", "line": "28"}, "connection": "wallet_db", "start_percent": 14.775, "width_percent": 7.565}, {"sql": "select * from `oauth_clients` where `id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 93}, {"index": 19, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 152}], "start": **********.603806, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ClientRepository.php:47", "source": "vendor/laravel/passport/src/ClientRepository.php:47", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClientRepository.php&line=47", "ajax": false, "filename": "ClientRepository.php", "line": "47"}, "connection": "wallet_db", "start_percent": 22.34, "width_percent": 10.875}, {"sql": "select * from `users` where `id` = '2' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/PassportUserProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PassportUserProvider.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 111}, {"index": 19, "namespace": null, "name": "vendor/laravel/passport/src/PassportServiceProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php", "line": 309}], "start": **********.6096902, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wallet_db", "start_percent": 33.215, "width_percent": 11.702}, {"sql": "select * from `oauth_access_tokens` where `id` = '4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946' limit 1", "type": "query", "params": [], "bindings": ["4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 170}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/PassportServiceProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php", "line": 309}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}], "start": **********.6150348, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "TokenRepository.php:28", "source": "vendor/laravel/passport/src/TokenRepository.php:28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FTokenRepository.php&line=28", "ajax": false, "filename": "TokenRepository.php", "line": "28"}, "connection": "wallet_db", "start_percent": 44.917, "width_percent": 10.993}, {"sql": "select * from `oauth_clients` where `id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 111}, {"index": 19, "namespace": null, "name": "vendor/laravel/passport/src/PassportServiceProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php", "line": 309}], "start": **********.6201038, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ClientRepository.php:47", "source": "vendor/laravel/passport/src/ClientRepository.php:47", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClientRepository.php&line=47", "ajax": false, "filename": "ClientRepository.php", "line": "47"}, "connection": "wallet_db", "start_percent": 55.91, "width_percent": 6.738}, {"sql": "select * from `business_settings` where (`key` = 'inactive_auth_minute') limit 1", "type": "query", "params": [], "bindings": ["inactive_auth_minute"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": "middleware", "name": "inactiveAuthCheck", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\InactiveAuthCheck.php", "line": 23}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": "middleware", "name": "deviceVerify", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\DeviceVerifyMiddleware.php", "line": 27}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.630004, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 62.648, "width_percent": 6.619}, {"sql": "select * from `user_log_histories` where `user_id` = 2 and `device_id` = 'test-device-123' and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["2", "test-device-123", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "middleware", "name": "checkDeviceId", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\CheckDeviceId.php", "line": 29}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": "middleware", "name": "customerAuth", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\CustomerMiddleware.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": "middleware", "name": "trackLastActiveAt", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\TrackLastActiveAt.php", "line": 26}], "start": **********.636163, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "checkDeviceId:29", "source": "middleware::checkDeviceId:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FMiddleware%2FCheckDeviceId.php&line=29", "ajax": false, "filename": "CheckDeviceId.php", "line": "29"}, "connection": "wallet_db", "start_percent": 69.267, "width_percent": 11.348}, {"sql": "select count(*) as aggregate from `transactions` where `user_id` = 2 and exists (select * from `users` where `transactions`.`user_id` = `users`.`id` and `type` = 2 and `users`.`deleted_at` is null) and `transaction_type` != 'admin_charge' and `transaction_type` != 'agent_commission'", "type": "query", "params": [], "bindings": ["2", "2", "admin_charge", "agent_commission"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/Customer/TransactionController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\Customer\\TransactionController.php", "line": 429}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6447868, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "TransactionController.php:429", "source": "app/Http/Controllers/Api/V1/Customer/TransactionController.php:429", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FCustomer%2FTransactionController.php&line=429", "ajax": false, "filename": "TransactionController.php", "line": "429"}, "connection": "wallet_db", "start_percent": 80.615, "width_percent": 19.385}]}, "models": {"data": {"Laravel\\Passport\\Token": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FToken.php&line=1", "ajax": false, "filename": "Token.php", "line": "?"}}, "Laravel\\Passport\\Client": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\UserLogHistory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FUserLogHistory.php&line=1", "ajax": false, "filename": "UserLogHistory.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/v1/customer/transaction-history", "status_code": "<pre class=sf-dump id=sf-dump-1544350469 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1544350469\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1922911808 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1922911808\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1818667472 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1818667472\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1945684642 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>device-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">test-device-123</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>device-model</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Test Device</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>browser</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">Mobile App</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>os</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Android</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Dart/2.17 (dart:io)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1945684642\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1694553868 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1694553868\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1055661919 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:48:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055661919\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-498136888 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-498136888\", {\"maxDepth\":0})</script>\n"}}