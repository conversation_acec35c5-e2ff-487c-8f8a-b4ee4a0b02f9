{"__meta": {"id": "X72cda07f7c34fe13cd46abfdd7f4dc4a", "datetime": "2025-07-07 14:27:41", "utime": **********.442723, "method": "GET", "uri": "/public/assets/installation/assets/img/svg-icons/php-version.svg", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.159945, "end": **********.442742, "duration": 0.28279709815979004, "duration_str": "283ms", "measures": [{"label": "Booting", "start": **********.159945, "relative_start": 0, "end": **********.410257, "relative_end": **********.410257, "duration": 0.25031208992004395, "duration_str": "250ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.410268, "relative_start": 0.****************, "end": **********.442744, "relative_end": 1.9073486328125e-06, "duration": 0.032475948333740234, "duration_str": "32.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/img/svg-icons/php-version.svg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/img/svg-icons/php-version.svg", "status_code": "<pre class=sf-dump id=sf-dump-2141122947 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2141122947\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1066870119 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1066870119\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-930969540 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-930969540\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-727847162 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"106 characters\">http://127.0.0.1:8000/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImU0SHpRY3B0YzI3K2RhVkd4TlFaREE9PSIsInZhbHVlIjoialMyRVNKU29ZWDVTZmZwRU03NGtiWVczTkc3ZmVibFY4d3AzRS9qUlJrUzhlWDh6R0k0RElQdVJ3dEI5TzlOaG9CWmttc0FjaGVuVTNyNTBHZStnMjBFVmJ2bU1aNFVpcWpQTFRsZDh5dG9oakRzbFBZeVZWQ2xWRE1hR2hmWngiLCJtYWMiOiJmYjk5YTc3MmVmNTViN2ZiMTZjMGM0Mzc2Yzk2OTc3ZDBlOTI2M2E0YmQyNDRjOWM2ZTY3NDFmM2U0YTFmNGRmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlJWSGkrcWVSM3ZuWGRHWHRBcHJDOHc9PSIsInZhbHVlIjoiQ3hLOTQrWTl3ZW1KZWdDS3AraTBtcmxPeEpVKzAreGNVQUEvQWhiL3RHM3dSRzlPeDFrWnc3dEhOekVYRVlJWnNQM05wOXozY0U1cUVSZzduaHg1NWpuSVd4cm9WYUNQbno0V25DUGZRT0ZCTXdJOE4wZXY1RWJTVXNFZzUzSGUiLCJtYWMiOiIxYTcxZjM3YzQzODEzZDFlYWQ0NTE3MDBjZjIwYThkOTUwNzJlN2NkZTc2YWM3MDg4YjE2ODgxMDM0YTc1N2RmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727847162\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-71573904 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71573904\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-917969306 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik9zQ3JFVzZKUUlycGZURUNvMGZ5Qnc9PSIsInZhbHVlIjoiU2ttLzBMK0xMbGxBM2FZTDVUMmNPaDNsOC9NYzR0VFFzN0FBSGp5cVNVbVFYNjRFMHlVNkt6UGVlSWsrcWZMRzB4aW1PQlZWU09GNjlwU2tBK2ZyNWtPYWR6NSsyTjRqTFJwdXgxMm03V3pkNWhBYy96Y21JbWdtWno3L2t5YkMiLCJtYWMiOiIyZDg5YzljNGY4MTc2M2U0M2JlYjgxN2U5YjIxN2VmZGE4NmRmMWE5MTcxZGMwMTc4ZTZmNTYzMGU5ZTI5MjEzIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkFkZlV0UDRJVjFualVBZnhVcWJFdkE9PSIsInZhbHVlIjoiU2p3VDJzUGZKKzFkWFhXL2dRRCtremxTSnB1MkpYTUpkOFlQMmtQYnFab2Q2RXZIZWRIL1pZSXFQRVRPWXJIazUzNitDa2hFMnhYcG1UWUJtcnFOKzNFTFlnUENRZDVXM0k4cCs5ZWRTV3pHSGs3d25NNWRzYTJvc0pVMU9GbFIiLCJtYWMiOiIzNTIxOTU0MGQ5M2JmNmVkYjgzZGE4ZTAwNTk3YWRkNGQ2YWJmZWM4NmM1ZjM3OTU0MjQyNTM5ZDkzMzU4ZmIwIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik9zQ3JFVzZKUUlycGZURUNvMGZ5Qnc9PSIsInZhbHVlIjoiU2ttLzBMK0xMbGxBM2FZTDVUMmNPaDNsOC9NYzR0VFFzN0FBSGp5cVNVbVFYNjRFMHlVNkt6UGVlSWsrcWZMRzB4aW1PQlZWU09GNjlwU2tBK2ZyNWtPYWR6NSsyTjRqTFJwdXgxMm03V3pkNWhBYy96Y21JbWdtWno3L2t5YkMiLCJtYWMiOiIyZDg5YzljNGY4MTc2M2U0M2JlYjgxN2U5YjIxN2VmZGE4NmRmMWE5MTcxZGMwMTc4ZTZmNTYzMGU5ZTI5MjEzIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkFkZlV0UDRJVjFualVBZnhVcWJFdkE9PSIsInZhbHVlIjoiU2p3VDJzUGZKKzFkWFhXL2dRRCtremxTSnB1MkpYTUpkOFlQMmtQYnFab2Q2RXZIZWRIL1pZSXFQRVRPWXJIazUzNitDa2hFMnhYcG1UWUJtcnFOKzNFTFlnUENRZDVXM0k4cCs5ZWRTV3pHSGs3d25NNWRzYTJvc0pVMU9GbFIiLCJtYWMiOiIzNTIxOTU0MGQ5M2JmNmVkYjgzZGE4ZTAwNTk3YWRkNGQ2YWJmZWM4NmM1ZjM3OTU0MjQyNTM5ZDkzMzU4ZmIwIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-917969306\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1143647293 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"85 characters\">http://127.0.0.1:8000/public/assets/installation/assets/img/svg-icons/php-version.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143647293\", {\"maxDepth\":0})</script>\n"}}