{"__meta": {"id": "X1519729557235d9fb5a049c905c2f865", "datetime": "2025-07-07 14:27:22", "utime": 1751876842.744726, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876842.368153, "end": 1751876842.744746, "duration": 0.37659287452697754, "duration_str": "377ms", "measures": [{"label": "Booting", "start": 1751876842.368153, "relative_start": 0, "end": 1751876842.600176, "relative_end": 1751876842.600176, "duration": 0.23202300071716309, "duration_str": "232ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876842.600189, "relative_start": 0.23203587532043457, "end": 1751876842.744748, "relative_end": 2.1457672119140625e-06, "duration": 0.14455914497375488, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24001944, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876842.632686, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876842.727951, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-923373259 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-923373259\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-251279647 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-251279647\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1939617672 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1939617672\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjJPZEl5U1Y3bnM2L2dLYW05Y3JFZ0E9PSIsInZhbHVlIjoieFQvZHYyMERDVXZmc2d5ZDdmTTZGRWwvT3ZNWEFsUTlCUEdyV0pYdCt5ZERiVFBlbmU3U2RManVDSWV3OFhObkJtUk5yU2xNNExFNjdwa0lPMGF3WDJITlV2T1VoMEtUNEdDSGd2M1ZVU2swVEpWd2FmRy9OR2hxK2FDbjJVdXIiLCJtYWMiOiI2OTRkM2E0M2UxNzkyZDM4MWJjODhkZmE3ZTBmMTEwOGM1NGE2MGQzYjQ1YzBhYmU5NGY2ZDEwZDZlOTJlZGEyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImdBdWRqN2pwZlhOamFpYXh0M2NWOVE9PSIsInZhbHVlIjoiRFkvNXhrRE5EU2M1VmRlZm9kdWtZVGlST2dVVnNIbGZ0bTBSRFlqZWlubGNHWEp2dnBabk9XUlAyMW9YTmVoYnpsOUM1ZURWOXY4T3NKZlh4SGptQlZ0dldiM2F5Z2NSajZqQzh2aDMwaDUvREFSTDE3TnhRWmNxWWp1cUtML2ciLCJtYWMiOiI1ZjIxN2I1YzA0MDA5ZTRlMWRiZWUyNmM4NTg4OGM5NWRkYmE3ZDZiOWZmYWM0NDIyM2UxMTZlN2I0N2NmZDM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1889408959 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkZFbGEwMlJtS2NmdDgyRXEyVlVjOEE9PSIsInZhbHVlIjoiREJaZ2ozcmVMRm8zUk14UERuNXF1QWNBckpyc0dUSy84QkM5RldZV3o4V0VUV1U4SUxJQ0xRMFBOQ1R0MEdOeUlhQ3BkbjVBVStBejlha2Vqd1RHZmFVZVFHUldmRjlkRldjbmE5SS96L2tyWFVnZkhwUWI0UU1wcWFMNVNtdjciLCJtYWMiOiJhMzdjODkwNDE4YzUzYWExZGI4OGNjYmYyMDk1YTA3YjIyODEyYThlMDFiOGI3NTBjNDg1OGJkNTUwMjAzMGI3IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:22 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjFiUGZwbXBGUzdSTXdYdUk1cURXSlE9PSIsInZhbHVlIjoiUVo2cGhnYWhibWFPVjY1L05JTzVkSXlUcFhDM1Btd1RvYjN5Y0JRcmdudG5jU0NWQjRNck16UXIxemRoMmZDcXlrSGZaWUxiaEM2YnZtcURXUWhtRDlZbXpRRW8ra3NVOThGMDlFRDh3Mm5iT0ZNUWhjb3BTWFRXZVhGUy9XUWIiLCJtYWMiOiJjNzZkZGFkYjRiMTk5OThjYTEwM2Y1MDJlOTI2YmFmMzA0MzhhZjM0OWZiNzUzMDU5OTAyMzY0ZDQyNDZmZmYwIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:22 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkZFbGEwMlJtS2NmdDgyRXEyVlVjOEE9PSIsInZhbHVlIjoiREJaZ2ozcmVMRm8zUk14UERuNXF1QWNBckpyc0dUSy84QkM5RldZV3o4V0VUV1U4SUxJQ0xRMFBOQ1R0MEdOeUlhQ3BkbjVBVStBejlha2Vqd1RHZmFVZVFHUldmRjlkRldjbmE5SS96L2tyWFVnZkhwUWI0UU1wcWFMNVNtdjciLCJtYWMiOiJhMzdjODkwNDE4YzUzYWExZGI4OGNjYmYyMDk1YTA3YjIyODEyYThlMDFiOGI3NTBjNDg1OGJkNTUwMjAzMGI3IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjFiUGZwbXBGUzdSTXdYdUk1cURXSlE9PSIsInZhbHVlIjoiUVo2cGhnYWhibWFPVjY1L05JTzVkSXlUcFhDM1Btd1RvYjN5Y0JRcmdudG5jU0NWQjRNck16UXIxemRoMmZDcXlrSGZaWUxiaEM2YnZtcURXUWhtRDlZbXpRRW8ra3NVOThGMDlFRDh3Mm5iT0ZNUWhjb3BTWFRXZVhGUy9XUWIiLCJtYWMiOiJjNzZkZGFkYjRiMTk5OThjYTEwM2Y1MDJlOTI2YmFmMzA0MzhhZjM0OWZiNzUzMDU5OTAyMzY0ZDQyNDZmZmYwIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889408959\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1834246773 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1834246773\", {\"maxDepth\":0})</script>\n"}}