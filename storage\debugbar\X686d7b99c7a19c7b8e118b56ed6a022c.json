{"__meta": {"id": "X686d7b99c7a19c7b8e118b56ed6a022c", "datetime": "2025-08-12 16:40:47", "utime": 1754995247.409464, "method": "GET", "uri": "/admin/auth/code/captcha/1", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754995246.908477, "end": 1754995247.409481, "duration": 0.5010039806365967, "duration_str": "501ms", "measures": [{"label": "Booting", "start": 1754995246.908477, "relative_start": 0, "end": 1754995247.320973, "relative_end": 1754995247.320973, "duration": 0.41249585151672363, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754995247.320992, "relative_start": 0.41251492500305176, "end": 1754995247.409483, "relative_end": 1.9073486328125e-06, "duration": 0.08849096298217773, "duration_str": "88.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23595032, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/auth/code/captcha/{tmp}", "middleware": "web, guest:user", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@captcha", "as": "admin.auth.default-captcha", "namespace": "App\\Http\\Controllers\\Admin\\Auth", "prefix": "admin/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=28\" onclick=\"\">app/Http/Controllers/Admin/Auth/LoginController.php:28-48</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/auth/code/captcha/1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "default_captcha_code": "tKYh", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/auth/code/captcha/1", "status_code": "<pre class=sf-dump id=sf-dump-1620755694 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1620755694\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-284551907 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-284551907\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1721518419 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1721518419\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2043759990 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik8rOTlVY3ptcTVPaE5xZ28wUjlxeUE9PSIsInZhbHVlIjoiQ2FpQ1ZlaEFPL2dFNVgyQ3BBMnhwcGJUK1h2TmlRTEhSVHJoNTlGdHVXTklWM2NGNStSMGhvbi92NFdTWFRUaEhBQ1c3RHJ1YzJta0ovbm5WTXVpUEs1R0NKbXB4SXBpM0VrK3JvVUk0eWpRcXJDc09OcXhrMHFKZmdJcDUxT00iLCJtYWMiOiJlZTBmZTVmNmU5YzdmZjgyMmQwZjljZmU1MWU2MTM5MmFjZDMxODAxODA0ODFkYzg5YTI4YWFkYmYzNDA0YTUzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlNKNUMwOVp1ODJ0dFgvc2NyelBkMkE9PSIsInZhbHVlIjoiandnV0dHNHhQa2k1ekFBUDRaSHg4dTZMblFBeitxUVlJaDhBS0Q5Vzc0dldnR0g0U3gySm9sSHhmM0o5Z09SV3kvdzNZKzRNaXNreHdsRzIvZEFhYTQwdkw4ZHJLMVZBNTd3SER4M0VSYzkreFk3dVBjYU4xWklxS1lnQ3ppZHYiLCJtYWMiOiIwMGM3Yzg3M2JmMTBhMTkwYTMxODgzOGY5N2M0NTJjYjRlZTg1NTdmYzNkNmJlZGYzZmI4Yjc3YmEzMWFmNzZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043759990\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2117887737 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xXBna3nsIuOcOZy2mqUA7gFRJDRGHJs4xurDvGRe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117887737\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-384330091 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 10:40:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imc3M0VFaURQUmtteFhiYWFVd1pqOXc9PSIsInZhbHVlIjoiNHp1cHRodUxnb0gvZTBMaXpVSDZ3VnF2YkhzMklvSFNZek5CK0FqVGVnQ0ltOUlLd2VSMzFmaUh2TE1keWJLTFdJZTFUQ2JXR2RKUmZDT1owKzUwKzNmSWhVUkU5Uk9IcTBXYWpwVVRpQVFJQUNFbUFsblJ3Q0RqREh3Q0pRLzEiLCJtYWMiOiJmYWZjMmY1MDUxNDQ5OTE5NDU0NDBjNzY1OTlmZTI1ZjMzZWE0OGRkZTBjODE1ZTEwZTk3YWEyZTY1MTkxZmI4IiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:40:47 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkNVOWhvYm1Qc0ZvaEh1ZzIzTUpJSFE9PSIsInZhbHVlIjoiYldBVFBoRW1YUlJFY2ppQmN0Y2M0VEk4bExHaHpoNllENmU0aW5hVnRQQ21rbFdqbXdNNUgxTnlQRVFrZVdQYW9sY1RVZ3d0V2Z2WlNDZFV2UkdCaUlRS0JsRVRyd2c3STFjZEkvRkdJZVRuVngxTTI0aGN4U0hDQitxeGh1ZU0iLCJtYWMiOiJlMjU5ZTM2MGQzZjIyYzAwNGE0ZGRiN2M4OWQ5YTViYjI1YTZmNjIxNDViZTU2OTU3NDc1MjNjN2E1ZDI3OGZlIiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:40:47 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imc3M0VFaURQUmtteFhiYWFVd1pqOXc9PSIsInZhbHVlIjoiNHp1cHRodUxnb0gvZTBMaXpVSDZ3VnF2YkhzMklvSFNZek5CK0FqVGVnQ0ltOUlLd2VSMzFmaUh2TE1keWJLTFdJZTFUQ2JXR2RKUmZDT1owKzUwKzNmSWhVUkU5Uk9IcTBXYWpwVVRpQVFJQUNFbUFsblJ3Q0RqREh3Q0pRLzEiLCJtYWMiOiJmYWZjMmY1MDUxNDQ5OTE5NDU0NDBjNzY1OTlmZTI1ZjMzZWE0OGRkZTBjODE1ZTEwZTk3YWEyZTY1MTkxZmI4IiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:40:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkNVOWhvYm1Qc0ZvaEh1ZzIzTUpJSFE9PSIsInZhbHVlIjoiYldBVFBoRW1YUlJFY2ppQmN0Y2M0VEk4bExHaHpoNllENmU0aW5hVnRQQ21rbFdqbXdNNUgxTnlQRVFrZVdQYW9sY1RVZ3d0V2Z2WlNDZFV2UkdCaUlRS0JsRVRyd2c3STFjZEkvRkdJZVRuVngxTTI0aGN4U0hDQitxeGh1ZU0iLCJtYWMiOiJlMjU5ZTM2MGQzZjIyYzAwNGE0ZGRiN2M4OWQ5YTViYjI1YTZmNjIxNDViZTU2OTU3NDc1MjNjN2E1ZDI3OGZlIiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:40:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384330091\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-549189388 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/admin/auth/code/captcha/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>default_captcha_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">tKYh</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549189388\", {\"maxDepth\":0})</script>\n"}}