{"__meta": {"id": "X66b77d51f2896064378a76194131bb83", "datetime": "2025-07-07 14:27:33", "utime": 1751876853.127286, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876852.716145, "end": 1751876853.127304, "duration": 0.41115903854370117, "duration_str": "411ms", "measures": [{"label": "Booting", "start": 1751876852.716145, "relative_start": 0, "end": 1751876852.979356, "relative_end": 1751876852.979356, "duration": 0.2632110118865967, "duration_str": "263ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876852.979372, "relative_start": 0.2632269859313965, "end": 1751876853.127306, "relative_end": 1.9073486328125e-06, "duration": 0.1479339599609375, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002136, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876853.020676, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876853.114367, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1172709766 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1172709766\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-155801687 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-155801687\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1436334625 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1436334625\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1617244714 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikt0WXo4bWg3bXA5Q2QxY3grNmdUUUE9PSIsInZhbHVlIjoianhTUWlaek5MLzhNV1l3ZXdSTkNQNGkzVDBZQmZxdGJRWTVEdHl3Z0tGODA3ZTVxSjY4T0UvMnVYcHJUd0JxWmhkTjN0WDl5YnFDQVZ6SG5XN3M4V0lYS01ZbTJMRzhJZU9JZDVHUlRMRjRleVprRGZWZ3ozUmdqMytnWmRUczUiLCJtYWMiOiI5ZmFkNTdhYWRmN2Q0NGI0ZGNhNzJmNTNmNzQ3OWI5MzY1ZDk0NGQ0NGEzZjk3NjI4Y2RmN2M0YTkzM2Q4MDM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImdrdkNiUGtuaW8vYU94eVJwb3d6eUE9PSIsInZhbHVlIjoicXpOYUN1OXBrSVhMYmZMYWhkdXorMVNMN2doQkRVS0ZCQVNha3RZMWU5SGRrTkJxcm1RRTgvREd6dmorQzhKVWloWUhheXBmNEtVYzhtOHA5OXdieko1ckY2TEkvWTVWcW5kdFBJdkdLNDVxUVBTZmtobm9hSHRSUEY2V2tMZmMiLCJtYWMiOiJiMGIwOWI3ZjFkYzAwNDljN2I1NGY1MGQ3MTJlNzQ1MzIzMTQ3YWE1Yjc4ZmI5MDBjOGQ1ZjQzOWRjOWRkNzNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1617244714\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1855595197 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855595197\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1347341505 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlhTVlc5RkVpd0pLcStFMTZKNlFyQnc9PSIsInZhbHVlIjoieXZ1VXl3dnFUdkt2Z2tXejM0QXVoMElVc3haMzFvYnhiN1lrS2RCOWkwU1lERkhQcVJIdUZHc0JGV0YvamZaY3dtbi9rK1MxYnpSK0paWG9xaWV6UzIzZGZJZTRFTkIwRGZ2UXNlbWdONGQ3WlhHMVZ2MXA5NzdUWkQ1QnZSQ1IiLCJtYWMiOiI3MzcyMmM0MDU0NGJjYTkwNDM2ZDk2MTRkZDFiYTc3OWM0NWE5NWQwNzU1ZThkNDliODFhZWFlNTQyMzNlZWFkIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:33 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IldGa050TzI1RDd4YUZtRnNodHFOMGc9PSIsInZhbHVlIjoiOVJvNk5FVURkejV1MkVScjVDTWxkN3crZ0pyZlk5UHhjc2NyTTVYOTkxNkZUZWtwRldwUU1MMHJYMEowV3E0cnBQOEtVMGVPZ3pObTdnSC9hKzZyVHI1NGVnaWhpSVlvR1ZqRzk0VElhMExudGVSclg5a0k0eTBsd2VHeUhSK2giLCJtYWMiOiI3YzUxODAyZjkyMDVkYTgyYjI2ZGQzZTNjYWJiYjJmY2NiOWQ1Y2Y5ODI2NGVkNjc3NzNkNzg5OTg0ZDRlMGYxIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:33 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlhTVlc5RkVpd0pLcStFMTZKNlFyQnc9PSIsInZhbHVlIjoieXZ1VXl3dnFUdkt2Z2tXejM0QXVoMElVc3haMzFvYnhiN1lrS2RCOWkwU1lERkhQcVJIdUZHc0JGV0YvamZaY3dtbi9rK1MxYnpSK0paWG9xaWV6UzIzZGZJZTRFTkIwRGZ2UXNlbWdONGQ3WlhHMVZ2MXA5NzdUWkQ1QnZSQ1IiLCJtYWMiOiI3MzcyMmM0MDU0NGJjYTkwNDM2ZDk2MTRkZDFiYTc3OWM0NWE5NWQwNzU1ZThkNDliODFhZWFlNTQyMzNlZWFkIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IldGa050TzI1RDd4YUZtRnNodHFOMGc9PSIsInZhbHVlIjoiOVJvNk5FVURkejV1MkVScjVDTWxkN3crZ0pyZlk5UHhjc2NyTTVYOTkxNkZUZWtwRldwUU1MMHJYMEowV3E0cnBQOEtVMGVPZ3pObTdnSC9hKzZyVHI1NGVnaWhpSVlvR1ZqRzk0VElhMExudGVSclg5a0k0eTBsd2VHeUhSK2giLCJtYWMiOiI3YzUxODAyZjkyMDVkYTgyYjI2ZGQzZTNjYWJiYjJmY2NiOWQ1Y2Y5ODI2NGVkNjc3NzNkNzg5OTg0ZDRlMGYxIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347341505\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-134053319 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134053319\", {\"maxDepth\":0})</script>\n"}}