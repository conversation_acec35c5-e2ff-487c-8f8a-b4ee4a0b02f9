{"__meta": {"id": "Xf9a0417ccad58fe356cf26a9bf45f0a5", "datetime": "2025-07-07 14:27:21", "utime": **********.418487, "method": "GET", "uri": "/public/assets/installation/assets/css/bootstrap.min.css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876840.980456, "end": **********.418512, "duration": 0.43805599212646484, "duration_str": "438ms", "measures": [{"label": "Booting", "start": 1751876840.980456, "relative_start": 0, "end": **********.380229, "relative_end": **********.380229, "duration": 0.39977288246154785, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.380241, "relative_start": 0.*****************, "end": **********.418514, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "38.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/css/bootstrap.min.css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/css/bootstrap.min.css", "status_code": "<pre class=sf-dump id=sf-dump-899379564 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-899379564\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1399202242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1399202242\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-21726829 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-21726829\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im9teFlKbk9GSE5haHpyaWhvbUhhU0E9PSIsInZhbHVlIjoib0ZTRHdBcW5VeW5TYnQvbU11WjdTK1VzN0ZsVzY2VG9Ec1RWMzhyVUFhOE1meUpxT1lxWTZjakhNZkJTUTAxV3pNYVZST04wNXpzUnBPaVBnMTg2d3RMekxTcnFkVDFaVHU5eGtqVysveXBRcE1Dc1BSMjFmcmd2VUQrQ1NPOWIiLCJtYWMiOiI1MjlmYjk2M2MwZGZlMDMyNjM0YWYzOWJmNDRkZTE5ZmM1ZDYzNzJmOTgzYWQyOThkMDI3MDI4NGI3MzM3MGY0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVVdmFvUTJCZXZSdktVTG93OW9jSHc9PSIsInZhbHVlIjoiZzkwbHlkbFByc3VhR1pmWDBXZ0xvb3JhT0d3Z3N4azNOSTgzNmRIM0l4TVJuOWNqd1QzeXpUd2x5SzY0WU05RnNsQk1ySnRoMDBON3E5aUdkcWo5dkhzZm5Oc3l0Rm8vbjZ4eWRXam4vaTcyak1pYUJCSlNBR2VWcnZwUExpVm4iLCJtYWMiOiJiOTM1NmM5NjgyZTk0YmVhNGRjMTIwZGJiYTM0Y2FlZGM1YzhmMGU1YzM5ODBhNzM5NDg3ZGZlYzliNjhlMWY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjJPZEl5U1Y3bnM2L2dLYW05Y3JFZ0E9PSIsInZhbHVlIjoieFQvZHYyMERDVXZmc2d5ZDdmTTZGRWwvT3ZNWEFsUTlCUEdyV0pYdCt5ZERiVFBlbmU3U2RManVDSWV3OFhObkJtUk5yU2xNNExFNjdwa0lPMGF3WDJITlV2T1VoMEtUNEdDSGd2M1ZVU2swVEpWd2FmRy9OR2hxK2FDbjJVdXIiLCJtYWMiOiI2OTRkM2E0M2UxNzkyZDM4MWJjODhkZmE3ZTBmMTEwOGM1NGE2MGQzYjQ1YzBhYmU5NGY2ZDEwZDZlOTJlZGEyIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:21 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImdBdWRqN2pwZlhOamFpYXh0M2NWOVE9PSIsInZhbHVlIjoiRFkvNXhrRE5EU2M1VmRlZm9kdWtZVGlST2dVVnNIbGZ0bTBSRFlqZWlubGNHWEp2dnBabk9XUlAyMW9YTmVoYnpsOUM1ZURWOXY4T3NKZlh4SGptQlZ0dldiM2F5Z2NSajZqQzh2aDMwaDUvREFSTDE3TnhRWmNxWWp1cUtML2ciLCJtYWMiOiI1ZjIxN2I1YzA0MDA5ZTRlMWRiZWUyNmM4NTg4OGM5NWRkYmE3ZDZiOWZmYWM0NDIyM2UxMTZlN2I0N2NmZDM3IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:21 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJPZEl5U1Y3bnM2L2dLYW05Y3JFZ0E9PSIsInZhbHVlIjoieFQvZHYyMERDVXZmc2d5ZDdmTTZGRWwvT3ZNWEFsUTlCUEdyV0pYdCt5ZERiVFBlbmU3U2RManVDSWV3OFhObkJtUk5yU2xNNExFNjdwa0lPMGF3WDJITlV2T1VoMEtUNEdDSGd2M1ZVU2swVEpWd2FmRy9OR2hxK2FDbjJVdXIiLCJtYWMiOiI2OTRkM2E0M2UxNzkyZDM4MWJjODhkZmE3ZTBmMTEwOGM1NGE2MGQzYjQ1YzBhYmU5NGY2ZDEwZDZlOTJlZGEyIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImdBdWRqN2pwZlhOamFpYXh0M2NWOVE9PSIsInZhbHVlIjoiRFkvNXhrRE5EU2M1VmRlZm9kdWtZVGlST2dVVnNIbGZ0bTBSRFlqZWlubGNHWEp2dnBabk9XUlAyMW9YTmVoYnpsOUM1ZURWOXY4T3NKZlh4SGptQlZ0dldiM2F5Z2NSajZqQzh2aDMwaDUvREFSTDE3TnhRWmNxWWp1cUtML2ciLCJtYWMiOiI1ZjIxN2I1YzA0MDA5ZTRlMWRiZWUyNmM4NTg4OGM5NWRkYmE3ZDZiOWZmYWM0NDIyM2UxMTZlN2I0N2NmZDM3IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1124190812 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"77 characters\">http://127.0.0.1:8000/public/assets/installation/assets/css/bootstrap.min.css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124190812\", {\"maxDepth\":0})</script>\n"}}