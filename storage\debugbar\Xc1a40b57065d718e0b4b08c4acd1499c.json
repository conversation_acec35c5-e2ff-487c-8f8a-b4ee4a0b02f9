{"__meta": {"id": "Xc1a40b57065d718e0b4b08c4acd1499c", "datetime": "2025-07-07 14:40:48", "utime": **********.562191, "method": "GET", "uri": "/api/v1/config", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 5, "messages": [{"message": "[14:40:48] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php on line 125", "message_html": null, "is_string": false, "label": "warning", "time": **********.322345, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:48] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php on line 125", "message_html": null, "is_string": false, "label": "warning", "time": **********.323617, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:48] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php on line 125", "message_html": null, "is_string": false, "label": "warning", "time": **********.324015, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:48] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php on line 246", "message_html": null, "is_string": false, "label": "warning", "time": **********.410043, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:48] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php on line 246", "message_html": null, "is_string": false, "label": "warning", "time": **********.448082, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751877647.636835, "end": **********.562234, "duration": 0.9253988265991211, "duration_str": "925ms", "measures": [{"label": "Booting", "start": 1751877647.636835, "relative_start": 0, "end": **********.138358, "relative_end": **********.138358, "duration": 0.5015230178833008, "duration_str": "502ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.138383, "relative_start": 0.5015478134155273, "end": **********.562237, "relative_end": 3.0994415283203125e-06, "duration": 0.42385411262512207, "duration_str": "424ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24049240, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/config", "middleware": "api, deviceVerify", "controller": "App\\Http\\Controllers\\Api\\V1\\ConfigController@configuration", "namespace": "App\\Http\\Controllers\\Api\\V1", "prefix": "api/v1", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FConfigController.php&line=18\" onclick=\"\">app/Http/Controllers/Api/V1/ConfigController.php:18-110</a>"}, "queries": {"nb_statements": 45, "nb_failed_statements": 0, "accumulated_duration": 0.04372, "accumulated_duration_str": "43.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where (`key` = 'currency') limit 1", "type": "query", "params": [], "bindings": ["currency"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 220}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 20}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2032459, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "helpers.php:220", "source": "app/CentralLogics/helpers.php:220", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=220", "ajax": false, "filename": "helpers.php", "line": "220"}, "connection": "wallet_db", "start_percent": 0, "width_percent": 1.738}, {"sql": "select * from `currencies` where (`currency_code` = 'BDT') limit 1", "type": "query", "params": [], "bindings": ["BDT"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 20}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.211703, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "ConfigController.php:20", "source": "app/Http/Controllers/Api/V1/ConfigController.php:20", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FConfigController.php&line=20", "ajax": false, "filename": "ConfigController.php", "line": "20"}, "connection": "wallet_db", "start_percent": 1.738, "width_percent": 4.346}, {"sql": "select * from `business_settings` where (`key` = 'language') limit 1", "type": "query", "params": [], "bindings": ["language"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 23}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2186801, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 6.084, "width_percent": 1.464}, {"sql": "select * from `business_settings` where `key` in ('ssl_commerz', 'razor_pay', 'paypal', 'stripe', 'senang_pay', 'paystack', 'bkash', 'paymob', 'flutterwave', 'mercadopago')", "type": "query", "params": [], "bindings": ["ssl_commerz", "razor_pay", "paypal", "stripe", "senang_pay", "paystack", "bkash", "paymob", "flutterwave", "mercadopago"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 32}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.224276, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "ConfigController.php:32", "source": "app/Http/Controllers/Api/V1/ConfigController.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FConfigController.php&line=32", "ajax": false, "filename": "ConfigController.php", "line": "32"}, "connection": "wallet_db", "start_percent": 7.548, "width_percent": 4.231}, {"sql": "select * from information_schema.tables where table_schema = 'wallet_db' and table_name = 'addon_settings' and table_type = 'BASE TABLE'", "type": "query", "params": [], "bindings": ["wallet_db", "addon_settings"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 114}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 41}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2972791, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "ConfigController.php:114", "source": "app/Http/Controllers/Api/V1/ConfigController.php:114", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FConfigController.php&line=114", "ajax": false, "filename": "ConfigController.php", "line": "114"}, "connection": "wallet_db", "start_percent": 11.78, "width_percent": 9.584}, {"sql": "select * from `addon_settings` where `settings_type` = 'payment_config'", "type": "query", "params": [], "bindings": ["payment_config"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 118}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 41}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.308949, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "ConfigController.php:118", "source": "app/Http/Controllers/Api/V1/ConfigController.php:118", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FConfigController.php&line=118", "ajax": false, "filename": "ConfigController.php", "line": "118"}, "connection": "wallet_db", "start_percent": 21.363, "width_percent": 4.414}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": ["business_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3343961, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 25.778, "width_percent": 2.242}, {"sql": "select * from `business_settings` where (`key` = 'logo') limit 1", "type": "query", "params": [], "bindings": ["logo"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3416579, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 28.019, "width_percent": 3.797}, {"sql": "select * from `business_settings` where (`key` = 'address') limit 1", "type": "query", "params": [], "bindings": ["address"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.349844, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 31.816, "width_percent": 1.967}, {"sql": "select * from `business_settings` where (`key` = 'phone') limit 1", "type": "query", "params": [], "bindings": ["phone"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.357412, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 33.783, "width_percent": 1.853}, {"sql": "select * from `business_settings` where (`key` = 'email') limit 1", "type": "query", "params": [], "bindings": ["email"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.364644, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 35.636, "width_percent": 2.333}, {"sql": "select * from `business_settings` where (`key` = 'currency_symbol_position') limit 1", "type": "query", "params": [], "bindings": ["currency_symbol_position"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3708322, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 37.969, "width_percent": 2.653}, {"sql": "select * from `business_settings` where (`key` = 'cashout_charge_percent') limit 1", "type": "query", "params": [], "bindings": ["cashout_charge_percent"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 70}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3778248, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 40.622, "width_percent": 1.372}, {"sql": "select * from `business_settings` where (`key` = 'sendmoney_charge_flat') limit 1", "type": "query", "params": [], "bindings": ["sendmoney_charge_flat"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 71}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.384865, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 41.995, "width_percent": 1.921}, {"sql": "select * from `business_settings` where (`key` = 'agent_commission_percent') limit 1", "type": "query", "params": [], "bindings": ["agent_commission_percent"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 72}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.391221, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 43.916, "width_percent": 2.264}, {"sql": "select * from `business_settings` where (`key` = 'withdraw_charge_percent') limit 1", "type": "query", "params": [], "bindings": ["withdraw_charge_percent"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 73}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.397454, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 46.18, "width_percent": 3.751}, {"sql": "select * from `business_settings` where (`key` = 'admin_commission') limit 1", "type": "query", "params": [], "bindings": ["admin_commission"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.40432, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 49.931, "width_percent": 2.402}, {"sql": "select * from `business_settings` where (`key` = 'two_factor') limit 1", "type": "query", "params": [], "bindings": ["two_factor"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.410336, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 52.333, "width_percent": 1.258}, {"sql": "select * from `business_settings` where (`key` = 'country') limit 1", "type": "query", "params": [], "bindings": ["country"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 76}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.415426, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 53.591, "width_percent": 2.173}, {"sql": "select * from `business_settings` where (`key` = 'terms_and_conditions') limit 1", "type": "query", "params": [], "bindings": ["terms_and_conditions"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 78}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.420085, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 55.764, "width_percent": 1.578}, {"sql": "select * from `business_settings` where (`key` = 'privacy_policy') limit 1", "type": "query", "params": [], "bindings": ["privacy_policy"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 79}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.42574, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 57.342, "width_percent": 1.693}, {"sql": "select * from `business_settings` where (`key` = 'about_us') limit 1", "type": "query", "params": [], "bindings": ["about_us"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 80}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.432515, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 59.035, "width_percent": 1.67}, {"sql": "select * from `business_settings` where (`key` = 'phone_verification') limit 1", "type": "query", "params": [], "bindings": ["phone_verification"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 81}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4378982, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 60.704, "width_percent": 2.219}, {"sql": "select * from `business_settings` where (`key` = 'email_verification') limit 1", "type": "query", "params": [], "bindings": ["email_verification"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4432049, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 62.923, "width_percent": 1.258}, {"sql": "select * from `business_settings` where (`key` = 'app_theme') limit 1", "type": "query", "params": [], "bindings": ["app_theme"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 83}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.448515, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 64.181, "width_percent": 1.578}, {"sql": "select * from `business_settings` where (`key` = 'otp_resend_time') limit 1", "type": "query", "params": [], "bindings": ["otp_resend_time"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 87}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4530098, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 65.759, "width_percent": 1.349}, {"sql": "select * from `business_settings` where (`key` = 'agent_self_registration') limit 1", "type": "query", "params": [], "bindings": ["agent_self_registration"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 88}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.45825, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 67.109, "width_percent": 1.51}, {"sql": "select * from `business_settings` where (`key` = 'customer_self_delete') limit 1", "type": "query", "params": [], "bindings": ["customer_self_delete"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.46284, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 68.618, "width_percent": 2.287}, {"sql": "select * from `business_settings` where (`key` = 'agent_self_delete') limit 1", "type": "query", "params": [], "bindings": ["agent_self_delete"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.468186, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 70.906, "width_percent": 1.395}, {"sql": "select * from `business_settings` where (`key` = 'add_money_status') limit 1", "type": "query", "params": [], "bindings": ["add_money_status"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 92}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.473453, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 72.301, "width_percent": 1.464}, {"sql": "select * from `business_settings` where (`key` = 'send_money_status') limit 1", "type": "query", "params": [], "bindings": ["send_money_status"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 93}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4779499, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 73.765, "width_percent": 1.349}, {"sql": "select * from `business_settings` where (`key` = 'cash_out_status') limit 1", "type": "query", "params": [], "bindings": ["cash_out_status"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 94}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.483723, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 75.114, "width_percent": 1.807}, {"sql": "select * from `business_settings` where (`key` = 'send_money_request_status') limit 1", "type": "query", "params": [], "bindings": ["send_money_request_status"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 95}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.488821, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 76.921, "width_percent": 3.477}, {"sql": "select * from `business_settings` where (`key` = 'withdraw_request_status') limit 1", "type": "query", "params": [], "bindings": ["withdraw_request_status"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 96}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.494221, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 80.398, "width_percent": 1.395}, {"sql": "select * from `business_settings` where (`key` = 'linked_website_status') limit 1", "type": "query", "params": [], "bindings": ["linked_website_status"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 97}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.499476, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 81.793, "width_percent": 2.425}, {"sql": "select * from `business_settings` where (`key` = 'banner_status') limit 1", "type": "query", "params": [], "bindings": ["banner_status"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.504401, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 84.218, "width_percent": 1.898}, {"sql": "select * from `business_settings` where (`key` = 'customer_add_money_limit') limit 1", "type": "query", "params": [], "bindings": ["customer_add_money_limit"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 100}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.510721, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 86.116, "width_percent": 1.372}, {"sql": "select * from `business_settings` where (`key` = 'customer_send_money_limit') limit 1", "type": "query", "params": [], "bindings": ["customer_send_money_limit"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 101}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.516489, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 87.489, "width_percent": 1.876}, {"sql": "select * from `business_settings` where (`key` = 'customer_send_money_request_limit') limit 1", "type": "query", "params": [], "bindings": ["customer_send_money_request_limit"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 102}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.521665, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 89.364, "width_percent": 1.921}, {"sql": "select * from `business_settings` where (`key` = 'customer_cash_out_limit') limit 1", "type": "query", "params": [], "bindings": ["customer_cash_out_limit"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.526467, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 91.285, "width_percent": 1.418}, {"sql": "select * from `business_settings` where (`key` = 'customer_withdraw_request_limit') limit 1", "type": "query", "params": [], "bindings": ["customer_withdraw_request_limit"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 104}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.531538, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 92.704, "width_percent": 1.349}, {"sql": "select * from `business_settings` where (`key` = 'agent_add_money_limit') limit 1", "type": "query", "params": [], "bindings": ["agent_add_money_limit"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.535719, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 94.053, "width_percent": 1.304}, {"sql": "select * from `business_settings` where (`key` = 'agent_send_money_limit') limit 1", "type": "query", "params": [], "bindings": ["agent_send_money_limit"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.541365, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 95.357, "width_percent": 1.395}, {"sql": "select * from `business_settings` where (`key` = 'agent_send_money_request_limit') limit 1", "type": "query", "params": [], "bindings": ["agent_send_money_request_limit"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5457551, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 96.752, "width_percent": 1.898}, {"sql": "select * from `business_settings` where (`key` = 'agent_withdraw_request_limit') limit 1", "type": "query", "params": [], "bindings": ["agent_withdraw_request_limit"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/ConfigController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\ConfigController.php", "line": 108}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.551002, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 98.651, "width_percent": 1.349}]}, "models": {"data": {"App\\Models\\BusinessSetting": {"value": 50, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 51, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/v1/config", "status_code": "<pre class=sf-dump id=sf-dump-1895191033 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1895191033\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1108822354 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1108822354\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2069507774 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2069507774\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1798792608 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Dart/2.17 (dart:io)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">Keep-Alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1798792608\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-80497049 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-80497049\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1657977418 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:40:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657977418\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1246983270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1246983270\", {\"maxDepth\":0})</script>\n"}}