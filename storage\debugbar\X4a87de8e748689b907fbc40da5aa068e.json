{"__meta": {"id": "X4a87de8e748689b907fbc40da5aa068e", "datetime": "2025-07-07 14:27:31", "utime": 1751876851.224622, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876850.689154, "end": 1751876851.22464, "duration": 0.5354859828948975, "duration_str": "535ms", "measures": [{"label": "Booting", "start": 1751876850.689154, "relative_start": 0, "end": 1751876851.073113, "relative_end": 1751876851.073113, "duration": 0.3839590549468994, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876851.073128, "relative_start": 0.3839740753173828, "end": 1751876851.224642, "relative_end": 2.1457672119140625e-06, "duration": 0.15151405334472656, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002704, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876851.112236, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876851.211345, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-730545031 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-730545031\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-78998814 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-78998814\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-738838943 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-738838943\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-219628897 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlBReWlEUWJrY083QzlGOGxWelVWbGc9PSIsInZhbHVlIjoiRHFhbVNQVmNWQVBlTkxldGF5RmViSWZibExlQWFza0ZoUDlFamRDemtyMUk3ZWZUZDNmWXVQbG5XbVU1SFJVRk1xMkg2dXJRNGZwb0JTSzR0cDI5RXV0Z3ZpUGZpcjRxaTF0VHRIUDlRakFmbGFsTm5jOWhadzB2cHY2cVFvYWYiLCJtYWMiOiIxZTQxMWEyNjllNzk1OWE4OGM4YzUxMDg4MWM4MDAzYjc2YmU2Mzk3YmNjYjZmMWUzODk3NWVlNWEyZGJiNjIwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkdBK04xS2ZuclRBTm9HT2RLbnNJWnc9PSIsInZhbHVlIjoidWplZXJ6bHdidnVXaE1jUXdWc3hZRzVVanNPQ3RwTkJrTTN4dU9XNVk5Q1MzRi9VQ2hoaXYwVFIvWE9WMmJVOEx1Lys1SlZUa01zNEFFSFltMUQrT0JBSmZUTU5mWEluNWtFaHB0dEtoZFVZcHRDNW1vWm5RbFFMU2FyMFJmaloiLCJtYWMiOiI0MjAxZjAxMDk2ZWM1NTVkNmE4MWViNmRlZjhmZjk0NGM2NzViODZlM2NiZjk0YmUzODU5ZjRjOTNmZmU4NWYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219628897\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-517665468 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517665468\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2052781696 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImsrOUNkcFQwbzdNaDl1NmxxVGRxb3c9PSIsInZhbHVlIjoibTFJa1BNL1lHSnlsUnRZOXFyM1ZDVWNhdGExNmU1dzNNYm91SjNjd0QrMnVRdXZTU3cvdXVBa1piSWlCOUQ3NkllUGNERll1UUVBdXJQd0J4V254MzFWQytaRVFnOVJpb1hjSDhwYWJVNjA5Wk5SY0tsYTZjTWFLSFQyQU9CTUgiLCJtYWMiOiI0MWE2ZmQwMTIwZDE2ZGQ4ODczNDQ3ZDM1ZmU4OTg3ZGUyZGRiYWU4ZmQyYWVhMWY2OTFlNmRmMWI3YzdlZGQ4IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im5Pb3dWbVFRMS9MSWJiOWM4eGRRZ1E9PSIsInZhbHVlIjoibW4yMXRxazFXWklqN1AzWFY5ek40QjQxclhtM2dpNEt5M09uMDlxUDRuTFhMNW81bXVFcDNQN1pBRkxNUGtUR0YvWWZLQmxwcnhMS3d3NVJselFpbWlhZU5hU0RqVjBTNEh5NzlIYnpndk0zK3BiendkQjJZd0hlZG9IQnhrRVEiLCJtYWMiOiJkYTkxOTQzOGYxMDYxMmFlZDFlZmY5OWM5Y2M0NWExNTZlOTY5ZTRiN2FlZGQyMzY3MmI4MGQxMWExMTNjNWZjIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImsrOUNkcFQwbzdNaDl1NmxxVGRxb3c9PSIsInZhbHVlIjoibTFJa1BNL1lHSnlsUnRZOXFyM1ZDVWNhdGExNmU1dzNNYm91SjNjd0QrMnVRdXZTU3cvdXVBa1piSWlCOUQ3NkllUGNERll1UUVBdXJQd0J4V254MzFWQytaRVFnOVJpb1hjSDhwYWJVNjA5Wk5SY0tsYTZjTWFLSFQyQU9CTUgiLCJtYWMiOiI0MWE2ZmQwMTIwZDE2ZGQ4ODczNDQ3ZDM1ZmU4OTg3ZGUyZGRiYWU4ZmQyYWVhMWY2OTFlNmRmMWI3YzdlZGQ4IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im5Pb3dWbVFRMS9MSWJiOWM4eGRRZ1E9PSIsInZhbHVlIjoibW4yMXRxazFXWklqN1AzWFY5ek40QjQxclhtM2dpNEt5M09uMDlxUDRuTFhMNW81bXVFcDNQN1pBRkxNUGtUR0YvWWZLQmxwcnhMS3d3NVJselFpbWlhZU5hU0RqVjBTNEh5NzlIYnpndk0zK3BiendkQjJZd0hlZG9IQnhrRVEiLCJtYWMiOiJkYTkxOTQzOGYxMDYxMmFlZDFlZmY5OWM5Y2M0NWExNTZlOTY5ZTRiN2FlZGQyMzY3MmI4MGQxMWExMTNjNWZjIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2052781696\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1430740089 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1430740089\", {\"maxDepth\":0})</script>\n"}}