/* ************************
   04.1: Dark Theme CSS
   ********************* */
   
   // [theme="dark"] {
   //    // select {
   //    //    color: #fff;
   //    //    option {
   //    //       color: black;
   //    //    }
   //    // }
   //    .card-header {
   //       background-color: rgba(#fff, .05);
   //       box-shadow: 0;
   //    }
   //    .btn-close {
   //       &:not(.outside) {
   //          filter: invert(1);
   //       }
   //    }
   //    ::-webkit-calendar-picker-indicator {
   //       filter: invert(1);
   //    }
   //    .dark-support {
   //       filter: brightness(.8) contrast(1.2);
   //    }
   //    // .bg-light {
   //    //    --bs-light-rgb: 34, 34, 34;
   //    // }
   // }


   // body.aside-folded[theme="dark"] .aside-body .nav ul.sub-menu {
   //    box-shadow: rgba(145, 158, 171, .1) 0px 0px 2px 0px,
   //       rgba(145, 158, 171, .1) -20px 20px 40px -4px;
   // }