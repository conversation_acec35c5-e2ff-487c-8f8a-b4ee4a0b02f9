.for-web-logo {
    width: 10.9125rem !important;
    display: block !important;
}
.for-seller-logo {
    width: 3rem !important;
    height: 2rem !important;
    display: block !important;
}
.code-example {
    position: relative;
}

.code-example .btn-clipboard {
    outline: none;
    background: transparent;
    border: 0;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    cursor: pointer;
}

.code-example .btn-clipboard:hover {
    color: rgba(0, 0, 0, 0.9);
}

.color-palette .color-entry {
    position: relative;
    display: block;
    height: 170px;
    color: #fff;
    border-radius: 0.25rem;
}

.color-palette a > .color-entry {
    -webkit-transition: all 200ms ease-in;
    -webkit-transform: scale(1);
    -ms-transition: all 200ms ease-in;
    -ms-transform: scale(1);
    -moz-transition: all 200ms ease-in;
    -moz-transform: scale(1);
    transition: all 200ms ease-in;
    transform: scale(1);
}

.color-palette a:hover > .color-entry {
    box-shadow: 0px 0px 80px rgba(0, 0, 0, 0.5);
    z-index: 2;
    -webkit-transform: scale(1.3);
    -ms-transform: scale(1.3);
    -moz-transform: scale(1.3);
    transform: scale(1.3);
}

.color-palette a.active > .color-entry,
.color-palette a.active:hover > .color-entry {
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    -moz-transform: scale(1.1);
    transform: scale(1.1);
}

.color-palette .color-entry .color-code {
    position: absolute;
    bottom: 12px;
    right: 12px;
}

.color-code-preview {
    border: 1px solid 0.25rem;
    padding: 6px;
}

.color-code-preview .color-preview {
    height: 150px;
    position: relative;
}

.color-code-preview .color-code-rgb {
    display: block;
    padding: 0 0 5px;
    text-align: center;
    font-weight: 500;
    font-size: 13px;
}

.color-code-preview .color-code-hex {
    display: block;
    padding: 10px 0 5px;
    text-align: center;
    font-weight: 500;
    font-size: 13px;
    text-transform: uppercase;
}

.color-code-preview .color-text {
    position: absolute;
    width: 100%;
    top: 50%;
    margin-top: -10px;
    display: block;
    text-align: center;
}

.color-code-preview .color-class {
    position: absolute;
    top: 10px;
    left: 10px;
}
.select2-container--default
    .select2-selection--multiple
    .select2-selection__choice {
    background-color: black;
    margin-top: 4px;
    /* color: white; */
}
.select2-container--default .select2-selection--multiple {
    /* height: auto; */
}
.select2-container--default .color-preview {
    height: 12px;
    width: 12px;
    display: inline-block;
    margin-right: 5px;
    margin-left: 3px;
    margin-top: 2px;
}
.sortSelectCustom .select2-selection--single {
    border-color: #ccc;
    height: auto;
    padding: 0 10px;
}

.sortSelectCustom .select2-selection--single .select2-selection__rendered {
    padding: 8px 0;
    color: #777;
    font-size: 13px;
}

.page-item.active .page-link {
    background-color: #014f5b !important;
}
ol.breadcrumb {
    padding: 1px;
}
.__input-grp-select {
    background: transparent;
    border: 1px solid #e5e5e5;
    width: 100%;
    height: 41.9px;
    border-radius: 5px;
}
.__input-grp-input {
    width: 100%;
    border-radius: 5px !important;
}
.__input-grp {
    flex-wrap: wrap;
    gap: 16px;
}

@media (min-width: 576px) {
    .__input-grp .__input-grp-select {
        width: 100px;
        padding-left: 10px;
        padding-right: 10px;
    }
    .__input-grp {
        flex-wrap: nowrap;
        gap: 0;
    }
    .__input-grp-select {
        width: auto;
        border-radius: 5px 0 0 5px;
        border-right: none;
    }
    .__input-grp-input {
        border-radius: 0 5px 5px 0 !important;
    }
}

.__wrap-gap-10 {
    flex-wrap: wrap;
    gap: 10px;
}
.spartan_item_wrapper-area .spartan_item_wrapper {
    width: 136px !important;
    max-width: 136px !important;
    flex: 0 0 100%;
}
.word-break {
    white-space: initial;
    word-break: break-word;
}
.word-nobreak {
    white-space: nowrap;
}
.__badge-break .badge {
    white-space: initial;
}
@media (min-width: 992px) {
    .__language-table {
        max-width: 100%;
    }
}
.min-w-260 {
    min-width: 260px;
}
img#viewer2 {
    max-width: 100%;
    object-fit: contain;
}
