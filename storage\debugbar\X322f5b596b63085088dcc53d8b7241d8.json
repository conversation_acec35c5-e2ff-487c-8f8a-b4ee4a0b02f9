{"__meta": {"id": "X322f5b596b63085088dcc53d8b7241d8", "datetime": "2025-07-07 14:27:40", "utime": 1751876860.70829, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876860.322344, "end": 1751876860.708308, "duration": 0.38596391677856445, "duration_str": "386ms", "measures": [{"label": "Booting", "start": 1751876860.322344, "relative_start": 0, "end": 1751876860.560402, "relative_end": 1751876860.560402, "duration": 0.23805785179138184, "duration_str": "238ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876860.560414, "relative_start": 0.23807001113891602, "end": 1751876860.70831, "relative_end": 1.9073486328125e-06, "duration": 0.14789581298828125, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002312, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876860.596544, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876860.694386, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1224873719 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1224873719\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1014470643 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1014470643\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-495789549 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-495789549\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1476246482 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"106 characters\">http://127.0.0.1:8000/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikkvc1hNbERCYTZUdUtuTzdJY1ZXUnc9PSIsInZhbHVlIjoiV3lYai82WUhkU2VEelJwdy9Kd3RqWlBDU2NIT3JIWWpaZXVvc1ZiNFYvSFRWeUZPTFcxRm9KTk9GbngwMlZ3aUtzVlB2ZjVCVnhISENFZFZEeG4vWXZDYkpva20wbWNvZTFnQzBoSTZXbkVmL0VHS2l3cUpZMllKTFA1bG1MQVoiLCJtYWMiOiJiMmVjMjE5ZjNlMDQ4MGQ0OGY4NDA0ODFlZThjNWRhYmU4MDAzZTkzYjQzNDY2NWFlNTg4OTIxYTBkZTlhNTYyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImROZnF1VW5WeU1zcVM2aGJBUDhBMUE9PSIsInZhbHVlIjoicnM1cEI2WWt2Q011dFN5TXFoNFZOMWMvQ0RYd0daVDVhYVc4YmFyRXU3YVBwc2w4dmthK3E1NGFnZzVWNTZmMFBVaVhyZTUvbDJLMjhpVnlYS3dYVktKQUoxaUVGbkgxcStHZ3NFRkpUZ28ycXZML2s2VjdlM3NTYUNQU3JqbWMiLCJtYWMiOiJjZmU5M2IyYzlmNGE1MmI5MGVhMzcwYTIxNDJkNTU2N2RjNTVmZDJmZDU0NWM4ZTE2MDdkOGVlNTU0MDdhNjE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1476246482\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1017779986 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1017779986\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-159048135 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImU0SHpRY3B0YzI3K2RhVkd4TlFaREE9PSIsInZhbHVlIjoialMyRVNKU29ZWDVTZmZwRU03NGtiWVczTkc3ZmVibFY4d3AzRS9qUlJrUzhlWDh6R0k0RElQdVJ3dEI5TzlOaG9CWmttc0FjaGVuVTNyNTBHZStnMjBFVmJ2bU1aNFVpcWpQTFRsZDh5dG9oakRzbFBZeVZWQ2xWRE1hR2hmWngiLCJtYWMiOiJmYjk5YTc3MmVmNTViN2ZiMTZjMGM0Mzc2Yzk2OTc3ZDBlOTI2M2E0YmQyNDRjOWM2ZTY3NDFmM2U0YTFmNGRmIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:40 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlJWSGkrcWVSM3ZuWGRHWHRBcHJDOHc9PSIsInZhbHVlIjoiQ3hLOTQrWTl3ZW1KZWdDS3AraTBtcmxPeEpVKzAreGNVQUEvQWhiL3RHM3dSRzlPeDFrWnc3dEhOekVYRVlJWnNQM05wOXozY0U1cUVSZzduaHg1NWpuSVd4cm9WYUNQbno0V25DUGZRT0ZCTXdJOE4wZXY1RWJTVXNFZzUzSGUiLCJtYWMiOiIxYTcxZjM3YzQzODEzZDFlYWQ0NTE3MDBjZjIwYThkOTUwNzJlN2NkZTc2YWM3MDg4YjE2ODgxMDM0YTc1N2RmIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:40 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImU0SHpRY3B0YzI3K2RhVkd4TlFaREE9PSIsInZhbHVlIjoialMyRVNKU29ZWDVTZmZwRU03NGtiWVczTkc3ZmVibFY4d3AzRS9qUlJrUzhlWDh6R0k0RElQdVJ3dEI5TzlOaG9CWmttc0FjaGVuVTNyNTBHZStnMjBFVmJ2bU1aNFVpcWpQTFRsZDh5dG9oakRzbFBZeVZWQ2xWRE1hR2hmWngiLCJtYWMiOiJmYjk5YTc3MmVmNTViN2ZiMTZjMGM0Mzc2Yzk2OTc3ZDBlOTI2M2E0YmQyNDRjOWM2ZTY3NDFmM2U0YTFmNGRmIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlJWSGkrcWVSM3ZuWGRHWHRBcHJDOHc9PSIsInZhbHVlIjoiQ3hLOTQrWTl3ZW1KZWdDS3AraTBtcmxPeEpVKzAreGNVQUEvQWhiL3RHM3dSRzlPeDFrWnc3dEhOekVYRVlJWnNQM05wOXozY0U1cUVSZzduaHg1NWpuSVd4cm9WYUNQbno0V25DUGZRT0ZCTXdJOE4wZXY1RWJTVXNFZzUzSGUiLCJtYWMiOiIxYTcxZjM3YzQzODEzZDFlYWQ0NTE3MDBjZjIwYThkOTUwNzJlN2NkZTc2YWM3MDg4YjE2ODgxMDM0YTc1N2RmIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-159048135\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-58114327 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58114327\", {\"maxDepth\":0})</script>\n"}}