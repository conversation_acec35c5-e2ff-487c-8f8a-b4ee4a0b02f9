{"__meta": {"id": "X27ef756a7bde1efd92064688ab3aa37d", "datetime": "2025-07-07 14:27:28", "utime": **********.704908, "method": "GET", "uri": "/public/assets/installation/assets/js/script.js", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.450471, "end": **********.704928, "duration": 0.2544569969177246, "duration_str": "254ms", "measures": [{"label": "Booting", "start": **********.450471, "relative_start": 0, "end": **********.675525, "relative_end": **********.675525, "duration": 0.22505402565002441, "duration_str": "225ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.675535, "relative_start": 0.*****************, "end": **********.70493, "relative_end": 2.1457672119140625e-06, "duration": 0.029395103454589844, "duration_str": "29.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/js/script.js\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/js/script.js", "status_code": "<pre class=sf-dump id=sf-dump-1816309893 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1816309893\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-382805611 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-382805611\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-918940091 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-918940091\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1198283715 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjBCaERoQ0krMXVidUV2YmVxaHdTbGc9PSIsInZhbHVlIjoicWpKcXFTd1lxRDN5YysweHhKNFBhZGlMUnowUUwwQndKNHBhVWdQRGlHVVU1bGVtakxCOHR2MTIyWmNUYllNWlViUEYwRkhQNWxXWWtvRDFCQXNhUSt3T1RKQllrZndTdG16dVBQNjhYZ2pBVXFEZ2RoZ3RtTnNXNElGbEZzQlQiLCJtYWMiOiIyMjk1MjY4Y2U1MmEyN2MxMmVhZDNmYjE4YjljMjUyOGMwMmU2NTQ1ZGY1MDUzYTMwM2I5YzNlZWNlNjA0N2ZkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InhXZjcyZE1XU2ZrVEFiTHhKcG9CWmc9PSIsInZhbHVlIjoidnoxcDV2M2xtV21PaGJsRzBVaFhUbTZ1ZDdPOEIySzZFUENEWUphZ2RXb05OWlRjbTZaZHpjMFE5a0lNYXdSZ0FiSDUwcFUvVXJJUzBBSVVpUWRCcTZwZEtlY3BEOU1tV1VSNHhOUG1UaEFwY3pjbzV0TFZqQnhjR1NtQUw0SEUiLCJtYWMiOiI4NTE1N2UyYWYwMmJhMzY2MGE5MTU3MGMyZmFiYmQ4YjhmZDUxYjVjYjRkYzYyY2RmMjE1Nzg1NDNhMDU5ZGEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198283715\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2005539530 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005539530\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1774478972 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJYNDgrcG5vNDhXbHpKN1BNYjluOEE9PSIsInZhbHVlIjoiaXI4cWxmUUx5NkpjUys0RHd4VlpDUmV4d0g2V0crSFJNeksxbHlxQytpUWFzaXBHS3NlL1lveUlVSU9LS0RqS21ZSkRwQ2IvMkFJbXZwdlJJNmFlR3duRzY3S2JSZDd0c3pGbVMyMmxxcjJmWERSSUxmb1dDV3ZzQWJXRlQwSDYiLCJtYWMiOiI5ZDU0ZGE1ODUwMGM4ZWY4OTU2MDljOTI4NTE1OGI1ZTUwOGYyOTA5MTRmOWY5ZjhjNjM1N2ViNGJjZTlmZDdhIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InQ5a0xXY0djMGJqU3Q2ZVhma2dGeGc9PSIsInZhbHVlIjoiWnRMS0FlWEFiVTJaMGpHOW5zeDEvWHM0dlJJTXZkVllrQ1N1ZlNrTjJsZWtteitOWWxhNTZJRkhDaTQ4SERTUlhvTjJGN0lrRmJ3Z3AxZC80L1BUam9aMWNtSzFIbENoK21hV1N6K1VITkwzVXFUV2UrMGlYTE8rNzlpS2g5S0YiLCJtYWMiOiJiYTE4ZGIyMTY2NWQyNzc0MTA1NzhmOWViNzhjNzI2NGUwNDY4NTU3OTU4Y2ZiYTBiMmM2YzhiMjJmOTY3NDE3IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJYNDgrcG5vNDhXbHpKN1BNYjluOEE9PSIsInZhbHVlIjoiaXI4cWxmUUx5NkpjUys0RHd4VlpDUmV4d0g2V0crSFJNeksxbHlxQytpUWFzaXBHS3NlL1lveUlVSU9LS0RqS21ZSkRwQ2IvMkFJbXZwdlJJNmFlR3duRzY3S2JSZDd0c3pGbVMyMmxxcjJmWERSSUxmb1dDV3ZzQWJXRlQwSDYiLCJtYWMiOiI5ZDU0ZGE1ODUwMGM4ZWY4OTU2MDljOTI4NTE1OGI1ZTUwOGYyOTA5MTRmOWY5ZjhjNjM1N2ViNGJjZTlmZDdhIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InQ5a0xXY0djMGJqU3Q2ZVhma2dGeGc9PSIsInZhbHVlIjoiWnRMS0FlWEFiVTJaMGpHOW5zeDEvWHM0dlJJTXZkVllrQ1N1ZlNrTjJsZWtteitOWWxhNTZJRkhDaTQ4SERTUlhvTjJGN0lrRmJ3Z3AxZC80L1BUam9aMWNtSzFIbENoK21hV1N6K1VITkwzVXFUV2UrMGlYTE8rNzlpS2g5S0YiLCJtYWMiOiJiYTE4ZGIyMTY2NWQyNzc0MTA1NzhmOWViNzhjNzI2NGUwNDY4NTU3OTU4Y2ZiYTBiMmM2YzhiMjJmOTY3NDE3IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774478972\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1522844906 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"68 characters\">http://127.0.0.1:8000/public/assets/installation/assets/js/script.js</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522844906\", {\"maxDepth\":0})</script>\n"}}