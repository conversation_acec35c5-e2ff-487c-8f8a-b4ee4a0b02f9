{"__meta": {"id": "X9e32dec713fbf202977d31e683a59461", "datetime": "2025-07-07 14:44:26", "utime": **********.184241, "method": "POST", "uri": "/api/v1/customer/auth/register", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751877865.75848, "end": **********.184257, "duration": 0.42577695846557617, "duration_str": "426ms", "measures": [{"label": "Booting", "start": 1751877865.75848, "relative_start": 0, "end": **********.099665, "relative_end": **********.099665, "duration": 0.3411848545074463, "duration_str": "341ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.099681, "relative_start": 0.3412008285522461, "end": **********.184258, "relative_end": 9.5367431640625e-07, "duration": 0.08457708358764648, "duration_str": "84.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25021056, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/customer/auth/register", "middleware": "api, deviceVerify", "controller": "App\\Http\\Controllers\\Api\\V1\\RegisterController@customerRegistration", "namespace": "App\\Http\\Controllers\\Api\\V1\\Auth\\Auth", "prefix": "api/v1/customer/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=28\" onclick=\"\">app/Http/Controllers/Api/V1/RegisterController.php:28-113</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00424, "accumulated_duration_str": "4.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `users` where `phone` = '1234567890' and (`deleted_at` is null)", "type": "query", "params": [], "bindings": ["1234567890"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 927}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 453}], "start": **********.149612, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "wallet_db", "start_percent": 0, "width_percent": 34.198}, {"sql": "select * from `users` where (`phone` = '+*************') and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["+*************"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.159408, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "RegisterController.php:54", "source": "app/Http/Controllers/Api/V1/RegisterController.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=54", "ajax": false, "filename": "RegisterController.php", "line": "54"}, "connection": "wallet_db", "start_percent": 34.198, "width_percent": 14.858}, {"sql": "select * from `business_settings` where (`key` = 'phone_verification') limit 1", "type": "query", "params": [], "bindings": ["phone_verification"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 62}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.164671, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 49.057, "width_percent": 16.274}, {"sql": "select * from `phone_verifications` where (`phone` = '+*************' and `otp` = '1234') limit 1", "type": "query", "params": [], "bindings": ["+*************", "1234"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 64}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1691768, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "RegisterController.php:64", "source": "app/Http/Controllers/Api/V1/RegisterController.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=64", "ajax": false, "filename": "RegisterController.php", "line": "64"}, "connection": "wallet_db", "start_percent": 65.33, "width_percent": 34.67}]}, "models": {"data": {"App\\Models\\BusinessSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/v1/customer/auth/register", "status_code": "<pre class=sf-dump id=sf-dump-1775344047 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-1775344047\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1507429202 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1507429202\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1069881322 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>dial_country_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">+880</span>\"\n  \"<span class=sf-dump-key>referral_code</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>otp</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1234</span>\"\n  \"<span class=sf-dump-key>occupation</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Engineer</span>\"\n  \"<span class=sf-dump-key>f_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">John</span>\"\n  \"<span class=sf-dump-key>l_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Doe</span>\"\n  \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1234567890</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1069881322\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-100727271 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Dart/2.17 (dart:io)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">283</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expect</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">100-continue</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-100727271\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-743125495 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-743125495\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-586378733 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:44:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-586378733\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-891589932 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-891589932\", {\"maxDepth\":0})</script>\n"}}