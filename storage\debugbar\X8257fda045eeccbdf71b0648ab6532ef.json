{"__meta": {"id": "X8257fda045eeccbdf71b0648ab6532ef", "datetime": "2025-07-07 14:27:34", "utime": **********.162559, "method": "GET", "uri": "/public/assets/installation/assets/img/svg-icons/database-password.svg", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876853.914742, "end": **********.162575, "duration": 0.2478330135345459, "duration_str": "248ms", "measures": [{"label": "Booting", "start": 1751876853.914742, "relative_start": 0, "end": **********.132908, "relative_end": **********.132908, "duration": 0.21816611289978027, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.132939, "relative_start": 0.*****************, "end": **********.162577, "relative_end": 1.9073486328125e-06, "duration": 0.029637813568115234, "duration_str": "29.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/img/svg-icons/database-password.svg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/img/svg-icons/database-password.svg", "status_code": "<pre class=sf-dump id=sf-dump-2033169048 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2033169048\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1185496234 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1185496234\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-115108012 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-115108012\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-520194017 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjNmdUI2bFFhR2tVQnhRaXZLOVJIVnc9PSIsInZhbHVlIjoiNDBjRXJTeUtKZ3hzUFg4aWN5em0xTnNBQmgyNmxQemEzbStjbXZxdHhGY3hXeHdHSitHdThBLytQSTh3RCs4Tmk1NENxcUZ3ZTZ3MzJ5djNIdDI3dDlRUWJZQjlVTVo2bW9xUWZuMWxBakJDY2VZNjVuNjNONW9OeWtLY2pqckciLCJtYWMiOiIzNDJiMTk5MDQ4MDgzYjVkY2VlOTE0ZmVmZTVjODBhYTNhYTg0MzgyMGM1YmYzODA4ZjZhOGIwNTY5MDNkMWY1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5NVHVONUQyWDQ1OHk0bkRRc0RCZ2c9PSIsInZhbHVlIjoibjk3T0ExeTVaWml6RVNZSEpqZjFQenRHdFo1eEg4VlA1VDlNZGxJcnlwK2d0TkZiQy95djdLbTk0Ym1TNEE5eXhVUHErWkY0YVlONmVoeiszVTYzenlwdUxGNjNIcDFqTTdhYUVza1Z4WXBuSFMyeTk0OUlwQkFsTzQ2WEVzOFAiLCJtYWMiOiI0NzNiYzRhZWYxNzk1NDFiNDkyYjYzOTk2Y2MwMmIyMmMxN2IyYmM4NzQ4ZmIyNjFiOGExZGRjNzFmZGI0MTVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520194017\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-529095752 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529095752\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik90cW9uNFJPRVN6cnJGeE5wd3RYUGc9PSIsInZhbHVlIjoienRuSmJFRDBFS1BVTDg0Z29EMXh6b3h0ZUZua3ZlaFN5VStreTg0NXltc2NyOE05SVNHaVBlQ3pCVTRLbjRrSGZXR0hmMlowbXB5V1Y3bEh3SW1PaHlvMjhFZW5NUGhWS0JNNXlWL2lDVW9GK3UxVE5kK1VrejJ4Z3UxcFFuM2QiLCJtYWMiOiIzODExYTYwM2E0ZDM1YWQ2MjRjZTA0ZWM2MzE0NWIxYzIzN2ZkZDk3MDdhOWEyN2Q2OTNkNDc2N2YyYjlmOTRlIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IktqR2hDV2VWd09yRXU3WXJQU3owdnc9PSIsInZhbHVlIjoiNUtWNGZxeDNJZG42bDQyc3llcnJVaW5BVUllQ2t5eGZaRjg2ZGhFdjZxY2NPRHZsWlJxU2k3a1NGczhUSUljaWdUREhqSHRFWGRxazFsdW9HZW5KV2FDdGRvM0N3aVlGY1QrZmlhU1BaQ3JyUzdRRkxkOUlpYjc5WlEwTWU5cnQiLCJtYWMiOiJiNmJmZGEyMjU2MGY4ZDE4Nzk0OTIzZDQwNjIyYTllNTAwODQxMWMxMWRlYWU2ZGE1YTQzODdjMzI4NDE2NDlhIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik90cW9uNFJPRVN6cnJGeE5wd3RYUGc9PSIsInZhbHVlIjoienRuSmJFRDBFS1BVTDg0Z29EMXh6b3h0ZUZua3ZlaFN5VStreTg0NXltc2NyOE05SVNHaVBlQ3pCVTRLbjRrSGZXR0hmMlowbXB5V1Y3bEh3SW1PaHlvMjhFZW5NUGhWS0JNNXlWL2lDVW9GK3UxVE5kK1VrejJ4Z3UxcFFuM2QiLCJtYWMiOiIzODExYTYwM2E0ZDM1YWQ2MjRjZTA0ZWM2MzE0NWIxYzIzN2ZkZDk3MDdhOWEyN2Q2OTNkNDc2N2YyYjlmOTRlIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IktqR2hDV2VWd09yRXU3WXJQU3owdnc9PSIsInZhbHVlIjoiNUtWNGZxeDNJZG42bDQyc3llcnJVaW5BVUllQ2t5eGZaRjg2ZGhFdjZxY2NPRHZsWlJxU2k3a1NGczhUSUljaWdUREhqSHRFWGRxazFsdW9HZW5KV2FDdGRvM0N3aVlGY1QrZmlhU1BaQ3JyUzdRRkxkOUlpYjc5WlEwTWU5cnQiLCJtYWMiOiJiNmJmZGEyMjU2MGY4ZDE4Nzk0OTIzZDQwNjIyYTllNTAwODQxMWMxMWRlYWU2ZGE1YTQzODdjMzI4NDE2NDlhIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-511916717 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"91 characters\">http://127.0.0.1:8000/public/assets/installation/assets/img/svg-icons/database-password.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-511916717\", {\"maxDepth\":0})</script>\n"}}