{"__meta": {"id": "Xc3e76a540019aa9417533e43cb2278ae", "datetime": "2025-08-12 16:40:46", "utime": 1754995246.78661, "method": "GET", "uri": "/admin/auth/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754995246.336233, "end": 1754995246.786634, "duration": 0.45040106773376465, "duration_str": "450ms", "measures": [{"label": "Booting", "start": 1754995246.336233, "relative_start": 0, "end": 1754995246.653984, "relative_end": 1754995246.653984, "duration": 0.3177511692047119, "duration_str": "318ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754995246.654004, "relative_start": 0.31777119636535645, "end": 1754995246.786637, "relative_end": 3.0994415283203125e-06, "duration": 0.13263297080993652, "duration_str": "133ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24841016, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "admin-views.auth.login", "param_count": null, "params": [], "start": 1754995246.706114, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/admin-views/auth/login.blade.phpadmin-views.auth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Fadmin-views%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/auth/login", "middleware": "web, guest:user", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@login", "as": "admin.auth.login", "namespace": "App\\Http\\Controllers\\Admin\\Auth", "prefix": "admin/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=53\" onclick=\"\">app/Http/Controllers/Admin/Auth/LoginController.php:53-56</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00278, "accumulated_duration_str": "2.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where (`key` = 'favicon') limit 1", "type": "query", "params": [], "bindings": ["favicon"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": "view", "name": "admin-views.auth.login", "file": "C:\\laragon\\www\\arefan_wallet_admin\\resources\\views/admin-views/auth/login.blade.php", "line": 8}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1754995246.727676, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 0, "width_percent": 25.899}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": ["business_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": "view", "name": "admin-views.auth.login", "file": "C:\\laragon\\www\\arefan_wallet_admin\\resources\\views/admin-views/auth/login.blade.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1754995246.734857, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 25.899, "width_percent": 25.899}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": ["business_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": "view", "name": "admin-views.auth.login", "file": "C:\\laragon\\www\\arefan_wallet_admin\\resources\\views/admin-views/auth/login.blade.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1754995246.741722, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 51.799, "width_percent": 23.741}, {"sql": "select * from `business_settings` where (`key` = 'recaptcha') limit 1", "type": "query", "params": [], "bindings": ["recaptcha"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": "view", "name": "admin-views.auth.login", "file": "C:\\laragon\\www\\arefan_wallet_admin\\resources\\views/admin-views/auth/login.blade.php", "line": 79}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1754995246.7591271, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 75.54, "width_percent": 24.46}]}, "models": {"data": {"App\\Models\\BusinessSetting": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/auth/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/auth/login", "status_code": "<pre class=sf-dump id=sf-dump-1528668790 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1528668790\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1249021748 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1249021748\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-625619212 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-625619212\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1169794706 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkY1UWl0WTdXbkY4R05lN0tDLy82OHc9PSIsInZhbHVlIjoiNWZqTzRxUHY3WmN0RzA3NC83dWR1SXkxRzRXanlYQlhwSFpDQmdhZFpPNGx3d0Q4SzVxN0dkZVcvQ0RURkxtMkdUR2VLUlk3SkJjRGVNaWtCVmVBeElUaGFaNzNCbzZvNkNJbGdNSWRFeVNpemNLZkJqMjFmYk9PRDZjMWt0WlMiLCJtYWMiOiI1ZjZlNWJiYjRiMTZhNGQ3OGMyYmFlNGRkNDdhNjAyY2U5YWVmOWFlY2FmM2VmZTI3M2Q2MTc3N2U3ZWQ2NTViIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjNRSEgwUko5eWdYTThha2JOTjdBelE9PSIsInZhbHVlIjoiK2NVbDBoeW9EOEZJTXpyM0xndlIxY0lwQi9RSDVFMVczb1N4ZzlIb1Z6Wk40KzJPTkx4bUlsZk95N09PRG05VHQ2NzhLNVl1Q2FoWHdjMTREc1psWlQ4YnFEUzJIRmdRSFFlVlVNdVllU05SRHlHNjJPQmhRbWl4QkxTWVIwSWwiLCJtYWMiOiIwMjdkMWRjOTg2Y2MwNzYwMzE1NDZhNjBjYmFiZjBkMzU1YWQ5MzViYTRjMDMwNmI1MTM5MzhiNzQzNjBkODc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169794706\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-628266442 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xXBna3nsIuOcOZy2mqUA7gFRJDRGHJs4xurDvGRe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-628266442\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-405604023 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 10:40:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik8rOTlVY3ptcTVPaE5xZ28wUjlxeUE9PSIsInZhbHVlIjoiQ2FpQ1ZlaEFPL2dFNVgyQ3BBMnhwcGJUK1h2TmlRTEhSVHJoNTlGdHVXTklWM2NGNStSMGhvbi92NFdTWFRUaEhBQ1c3RHJ1YzJta0ovbm5WTXVpUEs1R0NKbXB4SXBpM0VrK3JvVUk0eWpRcXJDc09OcXhrMHFKZmdJcDUxT00iLCJtYWMiOiJlZTBmZTVmNmU5YzdmZjgyMmQwZjljZmU1MWU2MTM5MmFjZDMxODAxODA0ODFkYzg5YTI4YWFkYmYzNDA0YTUzIiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:40:46 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlNKNUMwOVp1ODJ0dFgvc2NyelBkMkE9PSIsInZhbHVlIjoiandnV0dHNHhQa2k1ekFBUDRaSHg4dTZMblFBeitxUVlJaDhBS0Q5Vzc0dldnR0g0U3gySm9sSHhmM0o5Z09SV3kvdzNZKzRNaXNreHdsRzIvZEFhYTQwdkw4ZHJLMVZBNTd3SER4M0VSYzkreFk3dVBjYU4xWklxS1lnQ3ppZHYiLCJtYWMiOiIwMGM3Yzg3M2JmMTBhMTkwYTMxODgzOGY5N2M0NTJjYjRlZTg1NTdmYzNkNmJlZGYzZmI4Yjc3YmEzMWFmNzZlIiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:40:46 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik8rOTlVY3ptcTVPaE5xZ28wUjlxeUE9PSIsInZhbHVlIjoiQ2FpQ1ZlaEFPL2dFNVgyQ3BBMnhwcGJUK1h2TmlRTEhSVHJoNTlGdHVXTklWM2NGNStSMGhvbi92NFdTWFRUaEhBQ1c3RHJ1YzJta0ovbm5WTXVpUEs1R0NKbXB4SXBpM0VrK3JvVUk0eWpRcXJDc09OcXhrMHFKZmdJcDUxT00iLCJtYWMiOiJlZTBmZTVmNmU5YzdmZjgyMmQwZjljZmU1MWU2MTM5MmFjZDMxODAxODA0ODFkYzg5YTI4YWFkYmYzNDA0YTUzIiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:40:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlNKNUMwOVp1ODJ0dFgvc2NyelBkMkE9PSIsInZhbHVlIjoiandnV0dHNHhQa2k1ekFBUDRaSHg4dTZMblFBeitxUVlJaDhBS0Q5Vzc0dldnR0g0U3gySm9sSHhmM0o5Z09SV3kvdzNZKzRNaXNreHdsRzIvZEFhYTQwdkw4ZHJLMVZBNTd3SER4M0VSYzkreFk3dVBjYU4xWklxS1lnQ3ppZHYiLCJtYWMiOiIwMGM3Yzg3M2JmMTBhMTkwYTMxODgzOGY5N2M0NTJjYjRlZTg1NTdmYzNkNmJlZGYzZmI4Yjc3YmEzMWFmNzZlIiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:40:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405604023\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-749565060 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-749565060\", {\"maxDepth\":0})</script>\n"}}