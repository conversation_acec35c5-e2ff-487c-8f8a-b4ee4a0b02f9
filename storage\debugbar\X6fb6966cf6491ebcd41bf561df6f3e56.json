{"__meta": {"id": "X6fb6966cf6491ebcd41bf561df6f3e56", "datetime": "2025-07-07 14:27:31", "utime": **********.963099, "method": "GET", "uri": "/public/assets/installation/assets/css/style.css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.625632, "end": **********.963136, "duration": 0.33750391006469727, "duration_str": "338ms", "measures": [{"label": "Booting", "start": **********.625632, "relative_start": 0, "end": **********.926316, "relative_end": **********.926316, "duration": 0.30068397521972656, "duration_str": "301ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.926337, "relative_start": 0.****************, "end": **********.963139, "relative_end": 3.0994415283203125e-06, "duration": 0.036802053451538086, "duration_str": "36.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/css/style.css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/css/style.css", "status_code": "<pre class=sf-dump id=sf-dump-1746786147 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1746786147\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2108227172 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2108227172\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1446775574 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1446775574\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-609675350 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImsrOUNkcFQwbzdNaDl1NmxxVGRxb3c9PSIsInZhbHVlIjoibTFJa1BNL1lHSnlsUnRZOXFyM1ZDVWNhdGExNmU1dzNNYm91SjNjd0QrMnVRdXZTU3cvdXVBa1piSWlCOUQ3NkllUGNERll1UUVBdXJQd0J4V254MzFWQytaRVFnOVJpb1hjSDhwYWJVNjA5Wk5SY0tsYTZjTWFLSFQyQU9CTUgiLCJtYWMiOiI0MWE2ZmQwMTIwZDE2ZGQ4ODczNDQ3ZDM1ZmU4OTg3ZGUyZGRiYWU4ZmQyYWVhMWY2OTFlNmRmMWI3YzdlZGQ4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5Pb3dWbVFRMS9MSWJiOWM4eGRRZ1E9PSIsInZhbHVlIjoibW4yMXRxazFXWklqN1AzWFY5ek40QjQxclhtM2dpNEt5M09uMDlxUDRuTFhMNW81bXVFcDNQN1pBRkxNUGtUR0YvWWZLQmxwcnhMS3d3NVJselFpbWlhZU5hU0RqVjBTNEh5NzlIYnpndk0zK3BiendkQjJZd0hlZG9IQnhrRVEiLCJtYWMiOiJkYTkxOTQzOGYxMDYxMmFlZDFlZmY5OWM5Y2M0NWExNTZlOTY5ZTRiN2FlZGQyMzY3MmI4MGQxMWExMTNjNWZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609675350\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1534968780 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1534968780\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1884448921 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InMvdEZJbktoTFlubUJOd2xjbS9SeWc9PSIsInZhbHVlIjoieUVMeXFrajhDMDhDbWpWOW5MMGdNVTlqeklkNlY4NElOWWUxRTNzeTJuWHhJdk0xZFFvU05HNHgyeXJHZ1RvdUhaY3YyT0xWVXlua1RocjlISnR1U0JHc2V0SitPam0yOXpGQlkvbnF0UU9jYVZTRFp1UklhNzhjeVljdFlnSFQiLCJtYWMiOiJmOTg4Mjk2Njc1NTU0NDY3NGQ5MjQxYThhOGFmN2ZkOGJmZDNhNDdkMjdiOGZlZWQ4NmYxY2I1MmM1MTE1OTI5IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im5tYkJQN0ZlLzJiTk8xODNaY1Y1WXc9PSIsInZhbHVlIjoieFdhdmFCbUF6ZnRUclRtcDVNcTVBeVN1blZFZmZMNFVNYURMNlBvd2JRWVV4RUYxV0JkVko0OFJuZThEaFh4Qzg3bzJPNjJseklmamJWNklxclNqRS82R1drOStLOU9WalVmK2IwTlBXdW8zODR4M1R1THhvQ3lVWVZMTG5yU1IiLCJtYWMiOiI2MzU1OGYzZjJjYmIzMzRkNjBmZjIzOGI4MWYwOGIwN2MwYTRmOTUxZmQ3ZDY4MjdhNWU3OGVkNzk2ZmQ0YWU5IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InMvdEZJbktoTFlubUJOd2xjbS9SeWc9PSIsInZhbHVlIjoieUVMeXFrajhDMDhDbWpWOW5MMGdNVTlqeklkNlY4NElOWWUxRTNzeTJuWHhJdk0xZFFvU05HNHgyeXJHZ1RvdUhaY3YyT0xWVXlua1RocjlISnR1U0JHc2V0SitPam0yOXpGQlkvbnF0UU9jYVZTRFp1UklhNzhjeVljdFlnSFQiLCJtYWMiOiJmOTg4Mjk2Njc1NTU0NDY3NGQ5MjQxYThhOGFmN2ZkOGJmZDNhNDdkMjdiOGZlZWQ4NmYxY2I1MmM1MTE1OTI5IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im5tYkJQN0ZlLzJiTk8xODNaY1Y1WXc9PSIsInZhbHVlIjoieFdhdmFCbUF6ZnRUclRtcDVNcTVBeVN1blZFZmZMNFVNYURMNlBvd2JRWVV4RUYxV0JkVko0OFJuZThEaFh4Qzg3bzJPNjJseklmamJWNklxclNqRS82R1drOStLOU9WalVmK2IwTlBXdW8zODR4M1R1THhvQ3lVWVZMTG5yU1IiLCJtYWMiOiI2MzU1OGYzZjJjYmIzMzRkNjBmZjIzOGI4MWYwOGIwN2MwYTRmOTUxZmQ3ZDY4MjdhNWU3OGVkNzk2ZmQ0YWU5IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1884448921\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-239315960 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"69 characters\">http://127.0.0.1:8000/public/assets/installation/assets/css/style.css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-239315960\", {\"maxDepth\":0})</script>\n"}}