{"__meta": {"id": "Xdbda7c7128b32d750a9de4f948adfbef", "datetime": "2025-07-07 14:39:17", "utime": 1751877557.782933, "method": "GET", "uri": "/admin/auth/code/captcha/1", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751877557.475442, "end": 1751877557.782959, "duration": 0.30751705169677734, "duration_str": "308ms", "measures": [{"label": "Booting", "start": 1751877557.475442, "relative_start": 0, "end": 1751877557.716196, "relative_end": 1751877557.716196, "duration": 0.2407541275024414, "duration_str": "241ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751877557.716208, "relative_start": 0.24076604843139648, "end": 1751877557.782961, "relative_end": 1.9073486328125e-06, "duration": 0.06675291061401367, "duration_str": "66.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23734496, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/auth/code/captcha/{tmp}", "middleware": "web, guest:user", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@captcha", "as": "admin.auth.default-captcha", "namespace": "App\\Http\\Controllers\\Admin\\Auth", "prefix": "admin/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=28\" onclick=\"\">app/Http/Controllers/Admin/Auth/LoginController.php:28-48</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/auth/code/captcha/1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "default_captcha_code": "aBIz", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/auth/code/captcha/1", "status_code": "<pre class=sf-dump id=sf-dump-1956994020 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1956994020\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1150703975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1150703975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-553924790 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-553924790\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1278985037 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InMrUXI4dXZjSXRqdmNVc29SMS93bVE9PSIsInZhbHVlIjoiVnNrMmJqbWpScUhucVVEcDlaU21WTGtXVmlWdjFpUmhRaC81a2tReTN1dEJJZGl4dk9Edk9mZWhTd2VsU2xaNWVMYlFQNXVkeUlORDJjdElIelhTTGFVRmpBTEh6ZjJtaHV2dy9waFlxUnBHbFRUYndjN2dHMWRJbGRKdzlNdVYiLCJtYWMiOiI1ODdhZWYzZjdmYWU5NDZmZjlkMTIwMWFmMjkxMTRmNGEyMjFkMWRlYTUyNDMzMTk3MzRiNzBkNTVkOTU5NjEyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImFYZ2FUNzgvWkJ6NFE5QVJkZFgyUWc9PSIsInZhbHVlIjoialh5bEFWbldXR2VoSUwxaGM0UHhjV1p1VkhQQ1pXWTlIOVFUYjlrVVFrelgzblB2RWtFczdYN2J5WFVNWEhMZ3Y5UkR3c2E5T2ozNnFVNFRBWUNwemJCeEZMM3BRdW5OekphV1lYTGhDNXpTTDNYM3J5ME0wRmVVWWUzWHRvcTAiLCJtYWMiOiJiMGIwZDM1MDdmZjZlNmE3YzFlZTRmMDFhNjA3YjdiMDhjYTQ3NzExMzEzY2Y4YWIyMzgxNDIyNmIxNWMwMjJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278985037\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-558243625 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558243625\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1894848141 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:39:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ind5S3g5Q2ZqNmR6VlFkaGtlOVdFcGc9PSIsInZhbHVlIjoiYkZrUUU3ZVh0QzNmeVU0aHE1bW9LS1RLc3pWby9VZ0tPSGY2MGFlUUEwYXNkNWJ0d25hUWEwcHBoR2d4TWtjbk9WT2o0Mnp1dTIyVHZDL0JWT1VaVzFKN244YU9jM1A1KzZvemloWng5bTlQTDEvbGJYeHE5Z1VxMTYxdGR0SlYiLCJtYWMiOiIxNmE5ZmVkMTBiYTFiZTk2Y2VlMTk1NjkxZDQ4NDk2YTJkOWI1ZmZlYTZhZGJiYTcyMzNlZGM4ZjRmZTM2ODA0IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:39:17 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ik91SGRRdzNhMWdDeEpKbnVOc0pvK0E9PSIsInZhbHVlIjoiOXlaWGFHS3lkSGFBeUtWd0dTZWxZSUhZYVJlTGRLQ281a1JRenZETDRIa1pRbVZ6ZzdlMG1CWFpXYTBSK3pGbURiSWs0ZlduamtINUh1VUVHZnNENk5EMVB0bjhxcVNEd2FaWnMxTkQ5SHp2SmZHVmk4aFFMdlkzSnVOQUdidi8iLCJtYWMiOiJmYmIzMDRhYzA4M2U1NDkxNjVlNGI3YjlhYWYwMWIyNDU1YTQ3Yjk4YTAxM2NjOTI3MjExODNhZWM0MTBmNTRkIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:39:17 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ind5S3g5Q2ZqNmR6VlFkaGtlOVdFcGc9PSIsInZhbHVlIjoiYkZrUUU3ZVh0QzNmeVU0aHE1bW9LS1RLc3pWby9VZ0tPSGY2MGFlUUEwYXNkNWJ0d25hUWEwcHBoR2d4TWtjbk9WT2o0Mnp1dTIyVHZDL0JWT1VaVzFKN244YU9jM1A1KzZvemloWng5bTlQTDEvbGJYeHE5Z1VxMTYxdGR0SlYiLCJtYWMiOiIxNmE5ZmVkMTBiYTFiZTk2Y2VlMTk1NjkxZDQ4NDk2YTJkOWI1ZmZlYTZhZGJiYTcyMzNlZGM4ZjRmZTM2ODA0IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:39:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ik91SGRRdzNhMWdDeEpKbnVOc0pvK0E9PSIsInZhbHVlIjoiOXlaWGFHS3lkSGFBeUtWd0dTZWxZSUhZYVJlTGRLQ281a1JRenZETDRIa1pRbVZ6ZzdlMG1CWFpXYTBSK3pGbURiSWs0ZlduamtINUh1VUVHZnNENk5EMVB0bjhxcVNEd2FaWnMxTkQ5SHp2SmZHVmk4aFFMdlkzSnVOQUdidi8iLCJtYWMiOiJmYmIzMDRhYzA4M2U1NDkxNjVlNGI3YjlhYWYwMWIyNDU1YTQ3Yjk4YTAxM2NjOTI3MjExODNhZWM0MTBmNTRkIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:39:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894848141\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/admin/auth/code/captcha/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>default_captcha_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">aBIz</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}