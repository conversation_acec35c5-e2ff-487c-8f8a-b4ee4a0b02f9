{"__meta": {"id": "Xad19b415db34bdab91951be4cde28403", "datetime": "2025-07-07 14:27:49", "utime": 1751876869.183269, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876868.810853, "end": 1751876869.183304, "duration": 0.3724510669708252, "duration_str": "372ms", "measures": [{"label": "Booting", "start": 1751876868.810853, "relative_start": 0, "end": 1751876869.032602, "relative_end": 1751876869.032602, "duration": 0.22174906730651855, "duration_str": "222ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876869.032613, "relative_start": 0.22176003456115723, "end": 1751876869.183309, "relative_end": 5.0067901611328125e-06, "duration": 0.1506960391998291, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002328, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876869.065543, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876869.169922, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1828390401 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1828390401\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-779694442 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-779694442\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1480189738 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1480189738\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1561377957 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"106 characters\">http://127.0.0.1:8000/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjBIZi9mZThjSnBMR2ZrRUgwR0ZOaEE9PSIsInZhbHVlIjoiMXptZzRDNGFucmk1d1FxREo2aXh5QTBPZzFiMnVZY0wxVUl2Tm0xckVvL2pRSzdWQ2d3ZVVPU3U2YndkanpxaXAra21idTJEK2hVYTh2Ynl0U05ITVVCbnB0WHQ2NWN1QnJrZ3IwRHdHVTd0QUR5UTRuemtESWRweWtvRzhHU1ciLCJtYWMiOiI1MmY3YWNkZGQ2Y2Y4NTlhODk4ODg1ZjI0YzBjZjIwMTI3MTM4ZWE3OTkyYTZjM2I5MTZmYTBkZTRhMmU2NTZkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik52MmVMdVFNak1qZS9XcENTbEcyQUE9PSIsInZhbHVlIjoiaXJ6TGN1WHRxM1NGQ0d4RUlJZXkrZWJRS0xUWlZhR3JTUEhPbVdNaU5ldE8yZG9zN1BlQWZlTWQ4NFZHSzIydXljdlp6V2piZFBxRUwrN0lFeHZmQlZLVHA1T0hUdGlZc2c3NDdVR09TNFhaR0RTN1RCblFVb2ZReE9GOXpuSGsiLCJtYWMiOiI3M2I5MzQ4ZDhiMjgwZTZiYzJiMjE2OGIzMWUxOWZiYjJiNzY1NjU1ZTc2NzcxODEzYzEwYjYxNjc3YThhN2ViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1561377957\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-751465170 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-751465170\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1172008485 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlBhRGMwOC9rd24rbGtIMGU0Y3k3Q0E9PSIsInZhbHVlIjoieTVTVlh2eUs1dEkxd203WjNSQmxBUm9MTjM0bnMzeHd2WDJFbnhzNHZxNDFUTEhWTWZFbERKM0JpNjlSK2gzR3dpdXY3Z1VTWExNd1FPckZhc0RKOG5pL1o4WkhIRUk1N21xamQ1L3VHMFdiYlZzWExuQzdaMFdsUzNtV3ZhUTQiLCJtYWMiOiJiMjVmZjQwNWMwNTQ1YjA2YTg3YTg3MGE1MDRmOGExM2FkNjkwNjg3ZWRlZWQxYmRiN2Q2ZDA4MDc0YzIzN2E3IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImpYa3pUM0pmaDE1T3dkV1ZmOW9QREE9PSIsInZhbHVlIjoibmZPYzF2bnc0bk9GSzc2NW5wamVTZDE0a000NXVPU2RPMmp0Yk45eERtQ3dySUVDY3lkVkRuRGxmYTZSV0QrUWIxWFhubXRIRFJqT3dpV1l3b0ZObC8zZ0Fkeml4ajUrdlNkOFpYUENpVTd6QkorV0I2Z0pDaG03Y0xxb1UxME0iLCJtYWMiOiIzMTkxZDEzMzM2OTg1N2Q1MTQ2MTdiZGZmZjViZWI1N2FiZTdjNWFkNjQyYjEyOTRiNGEzYjI2NDk3MjI1ZmM0IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlBhRGMwOC9rd24rbGtIMGU0Y3k3Q0E9PSIsInZhbHVlIjoieTVTVlh2eUs1dEkxd203WjNSQmxBUm9MTjM0bnMzeHd2WDJFbnhzNHZxNDFUTEhWTWZFbERKM0JpNjlSK2gzR3dpdXY3Z1VTWExNd1FPckZhc0RKOG5pL1o4WkhIRUk1N21xamQ1L3VHMFdiYlZzWExuQzdaMFdsUzNtV3ZhUTQiLCJtYWMiOiJiMjVmZjQwNWMwNTQ1YjA2YTg3YTg3MGE1MDRmOGExM2FkNjkwNjg3ZWRlZWQxYmRiN2Q2ZDA4MDc0YzIzN2E3IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImpYa3pUM0pmaDE1T3dkV1ZmOW9QREE9PSIsInZhbHVlIjoibmZPYzF2bnc0bk9GSzc2NW5wamVTZDE0a000NXVPU2RPMmp0Yk45eERtQ3dySUVDY3lkVkRuRGxmYTZSV0QrUWIxWFhubXRIRFJqT3dpV1l3b0ZObC8zZ0Fkeml4ajUrdlNkOFpYUENpVTd6QkorV0I2Z0pDaG03Y0xxb1UxME0iLCJtYWMiOiIzMTkxZDEzMzM2OTg1N2Q1MTQ2MTdiZGZmZjViZWI1N2FiZTdjNWFkNjQyYjEyOTRiNGEzYjI2NDk3MjI1ZmM0IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172008485\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1602728788 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602728788\", {\"maxDepth\":0})</script>\n"}}