{"__meta": {"id": "X65f3e6df328ec69ad6c975936aaf1ac6", "datetime": "2025-07-07 14:27:39", "utime": **********.286592, "method": "GET", "uri": "/public/assets/installation/assets/css/style.css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876858.926645, "end": **********.286623, "duration": 0.35997796058654785, "duration_str": "360ms", "measures": [{"label": "Booting", "start": 1751876858.926645, "relative_start": 0, "end": **********.238277, "relative_end": **********.238277, "duration": 0.3116319179534912, "duration_str": "312ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.238293, "relative_start": 0.***************, "end": **********.286625, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "48.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/css/style.css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/css/style.css", "status_code": "<pre class=sf-dump id=sf-dump-2127725490 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2127725490\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1231839470 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1231839470\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1743876674 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1743876674\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2052285461 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"106 characters\">http://127.0.0.1:8000/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InBCa3pmMXlhbUxFdFoyN3RLcDhSL1E9PSIsInZhbHVlIjoieVNWdytScjc1L05NN1ZIWUt3bXNCdUNGdi93a2V6TjBTRFppNEYvejdRdW05K2VyUVNVL05pZlMvYVRrbGNyb2U4Z3E0MFd3bXZZMzBTelpPQUE0QTBRYlB6TWpuV1lIY2FlM0VJWlBRaEVCeXc3VXpnV2JmNFBjWkE3d1NkNXYiLCJtYWMiOiJmMDRlNGMwMzE4ZDkwYzk5ZDRiZDI1MWFkYTdkZGIxNTliOTA4ZmQ4MjZmOWQ4YTYwMzBkODgyZWY4NmEwMmJkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlMwcWZUMDhiTlNBK2xyQTVyMHVKS3c9PSIsInZhbHVlIjoiWHI1VWxIbkhac2tqaWh0R0N5SGdVOGNMWTdrTmZLOGxnRW96eHB0YjU2Qk5NdFo3VXphSzBGYVZETE02bjFPdnREbndkckZqTVJvNFlnMEJNNkYzbXZSeXFYQjNFd25hRFRoVDFHUjAwUmpDVWhpcWJBV1g3L081eFNZUkFwR2giLCJtYWMiOiJmNmEzNDJkYmMyY2FlNTUxZGUwOTAyYzcxN2M1NjQzYWRmYzVlZjAyNjU2YzkwY2I4Njg1MDM3ZTFmOGRjMjk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2052285461\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-365583919 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-365583919\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-35582361 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikkvc1hNbERCYTZUdUtuTzdJY1ZXUnc9PSIsInZhbHVlIjoiV3lYai82WUhkU2VEelJwdy9Kd3RqWlBDU2NIT3JIWWpaZXVvc1ZiNFYvSFRWeUZPTFcxRm9KTk9GbngwMlZ3aUtzVlB2ZjVCVnhISENFZFZEeG4vWXZDYkpva20wbWNvZTFnQzBoSTZXbkVmL0VHS2l3cUpZMllKTFA1bG1MQVoiLCJtYWMiOiJiMmVjMjE5ZjNlMDQ4MGQ0OGY4NDA0ODFlZThjNWRhYmU4MDAzZTkzYjQzNDY2NWFlNTg4OTIxYTBkZTlhNTYyIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:39 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImROZnF1VW5WeU1zcVM2aGJBUDhBMUE9PSIsInZhbHVlIjoicnM1cEI2WWt2Q011dFN5TXFoNFZOMWMvQ0RYd0daVDVhYVc4YmFyRXU3YVBwc2w4dmthK3E1NGFnZzVWNTZmMFBVaVhyZTUvbDJLMjhpVnlYS3dYVktKQUoxaUVGbkgxcStHZ3NFRkpUZ28ycXZML2s2VjdlM3NTYUNQU3JqbWMiLCJtYWMiOiJjZmU5M2IyYzlmNGE1MmI5MGVhMzcwYTIxNDJkNTU2N2RjNTVmZDJmZDU0NWM4ZTE2MDdkOGVlNTU0MDdhNjE2IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:39 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikkvc1hNbERCYTZUdUtuTzdJY1ZXUnc9PSIsInZhbHVlIjoiV3lYai82WUhkU2VEelJwdy9Kd3RqWlBDU2NIT3JIWWpaZXVvc1ZiNFYvSFRWeUZPTFcxRm9KTk9GbngwMlZ3aUtzVlB2ZjVCVnhISENFZFZEeG4vWXZDYkpva20wbWNvZTFnQzBoSTZXbkVmL0VHS2l3cUpZMllKTFA1bG1MQVoiLCJtYWMiOiJiMmVjMjE5ZjNlMDQ4MGQ0OGY4NDA0ODFlZThjNWRhYmU4MDAzZTkzYjQzNDY2NWFlNTg4OTIxYTBkZTlhNTYyIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImROZnF1VW5WeU1zcVM2aGJBUDhBMUE9PSIsInZhbHVlIjoicnM1cEI2WWt2Q011dFN5TXFoNFZOMWMvQ0RYd0daVDVhYVc4YmFyRXU3YVBwc2w4dmthK3E1NGFnZzVWNTZmMFBVaVhyZTUvbDJLMjhpVnlYS3dYVktKQUoxaUVGbkgxcStHZ3NFRkpUZ28ycXZML2s2VjdlM3NTYUNQU3JqbWMiLCJtYWMiOiJjZmU5M2IyYzlmNGE1MmI5MGVhMzcwYTIxNDJkNTU2N2RjNTVmZDJmZDU0NWM4ZTE2MDdkOGVlNTU0MDdhNjE2IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35582361\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"69 characters\">http://127.0.0.1:8000/public/assets/installation/assets/css/style.css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}