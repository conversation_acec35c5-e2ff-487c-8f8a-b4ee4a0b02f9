{"__meta": {"id": "Xff3a5946542fa189382cba4e7570a79e", "datetime": "2025-07-07 14:27:48", "utime": **********.788491, "method": "GET", "uri": "/public/assets/installation/assets/js/script.js", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.553669, "end": **********.788508, "duration": 0.23483896255493164, "duration_str": "235ms", "measures": [{"label": "Booting", "start": **********.553669, "relative_start": 0, "end": **********.760443, "relative_end": **********.760443, "duration": 0.20677399635314941, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.760454, "relative_start": 0.*****************, "end": **********.788509, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "28.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/js/script.js\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/js/script.js", "status_code": "<pre class=sf-dump id=sf-dump-1228542901 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1228542901\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-224286350 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-224286350\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1642846286 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1642846286\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-289042735 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"106 characters\">http://127.0.0.1:8000/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjUxT2pkOFVuY09lenBXcUkzeTlERHc9PSIsInZhbHVlIjoiN1lUY0ptS2E1SWtlM1gzVlloSk53eTBiRjlqcmxTYUhRbHNtY25BUmxqcEpGdnRtN2loNnVsVWI3a0toMEVSL2tQWGV4TklqaUNxTnRnSWlzRC9yN21MN09UY29Jd2QvRWdXS1hwRWNkZVNIOVlEQzEzNlFEZTVxRFRkaWtGZ2giLCJtYWMiOiIwZTE2ODhmOGM1ZWZhMjZkMTljNmJiMTE2NzFmNGJjOWYwM2M3OTU0MDk1MTU1ZTRlOTIzZmIxZDcyNGVkZjA0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlJTaGIwOTh6Y1FCanZscU9tWUMwdFE9PSIsInZhbHVlIjoicUJ1Q29vcElCSVBROFNkdTk4QUQzd01JTUpORGRIWGlDclVTMjA1WlVTMXFERW5NQXBUajcyd2h3VituVWdDb2pMeFVITG9TNU9hSzROUlhGMlh6Q1JuZHBGWk5pcUs4RjF4TEhrMDZrUDJhVmE3M3JwdTRscC9yRjhpRnN2NTUiLCJtYWMiOiI0MDc0ZmEyMmNiYjVhMGYzMmI5Y2ZkNGVkZjI4NmRmNjczNzk2MDliNzhjMzU0YWI4MmEyN2RmY2MwOTUzZGQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289042735\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-49871386 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49871386\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1306090773 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImNTUmJNSk9xVGZhbWtQZ2FxSnFLTVE9PSIsInZhbHVlIjoiWXJxVk5uc0ptMHNCVEdmMEFqUEVDNThrSDlxWmRPcitpc0R3cForTi8yc2xSMDRlU1NveW9jV25hbWNpenNmS3RlTGEwY05Ld3VBblFXbDcrYUtZMnJOL0NGbGdiejkxcVpMeGI1d3Z1djcwS2d1emkwRnNadUJxWk9NY09OUGIiLCJtYWMiOiI1OWIzZjY0MTY0YzMwZWRjMjQzN2Y1ZjRkZWIyY2Q1YmFkYjRlYzA1ZTZkZGMzODk1ZjQxMTYyZTk5NmNlOWM0IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:48 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkRONHpSdkZ3cXV2MTVEdVpXRG16UUE9PSIsInZhbHVlIjoiUFordmdSUDRsQ25wMC9rc0Z2QnlvMTA3WnU2TEJhYWpFZ1NpU3NXOWFSanlHaHJJVWVlT1hiOWpRYWhYbmN0Z0ZkaFdEeC9pbCsyVDcwU2tnVFhUL2piMzV2ZmZlTkM5OFljNm4zTG44c1NSb0MvR3RRTGZCdmhpbjFWYlBPOUYiLCJtYWMiOiI0MmY2M2QyYTU4NWI3MzQ0MmQ3ZmNiYzBhZjhjNjJjMzA1MmIzYmZkYWY0NDdiMmMxNTkzOGRmYTI5OTFjMmNmIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:48 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImNTUmJNSk9xVGZhbWtQZ2FxSnFLTVE9PSIsInZhbHVlIjoiWXJxVk5uc0ptMHNCVEdmMEFqUEVDNThrSDlxWmRPcitpc0R3cForTi8yc2xSMDRlU1NveW9jV25hbWNpenNmS3RlTGEwY05Ld3VBblFXbDcrYUtZMnJOL0NGbGdiejkxcVpMeGI1d3Z1djcwS2d1emkwRnNadUJxWk9NY09OUGIiLCJtYWMiOiI1OWIzZjY0MTY0YzMwZWRjMjQzN2Y1ZjRkZWIyY2Q1YmFkYjRlYzA1ZTZkZGMzODk1ZjQxMTYyZTk5NmNlOWM0IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkRONHpSdkZ3cXV2MTVEdVpXRG16UUE9PSIsInZhbHVlIjoiUFordmdSUDRsQ25wMC9rc0Z2QnlvMTA3WnU2TEJhYWpFZ1NpU3NXOWFSanlHaHJJVWVlT1hiOWpRYWhYbmN0Z0ZkaFdEeC9pbCsyVDcwU2tnVFhUL2piMzV2ZmZlTkM5OFljNm4zTG44c1NSb0MvR3RRTGZCdmhpbjFWYlBPOUYiLCJtYWMiOiI0MmY2M2QyYTU4NWI3MzQ0MmQ3ZmNiYzBhZjhjNjJjMzA1MmIzYmZkYWY0NDdiMmMxNTkzOGRmYTI5OTFjMmNmIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306090773\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1722568060 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"68 characters\">http://127.0.0.1:8000/public/assets/installation/assets/js/script.js</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722568060\", {\"maxDepth\":0})</script>\n"}}