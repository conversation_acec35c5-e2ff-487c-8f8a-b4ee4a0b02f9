<?php if($data['intro_section']['status'] == '1'): ?>
    <div class="banner overflow-hidden" data-bg-img="<?php echo e(asset('public/assets/landing/img/media/banner-bg.png')); ?>">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <h1 class="banner-title text-white text-center text-lg-start"><?php echo change_text_color_or_bg($data['intro_section']['data']['title']); ?></h1>
                </div>
                <div class="col-lg-6">
                    <div class="banner-content">
                        <p class="fs-18 text-white text-center text-lg-start"><?php echo $data['intro_section']['data']['description']; ?></p>
                        <div class="d-flex justify-content-center justify-content-lg-start gap-3 mt-4">
                            <a href="<?php echo e($data['intro_section']['data']['download_link']); ?>"
                               class="btn btn-warning"><?php echo e($data['intro_section']['data']['button_name'] ?? translate('Download')); ?></a>
                            <a href="#feature-section" class="btn btn-primary"><?php echo e(translate('Explore More')); ?></a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-5 pt-sm-5">
                <div
                        class="d-flex flex-column flex-sm-row align-items-center justify-content-between gap-3 flex-wrap banner-middle-content">
                    <div class="bg-white p-3 rounded pe-xl-5 star-opacity">
                        <div class="media gap-2 align-items-center">
                            <img width="40"
                                 src="<?php echo e(asset('storage/app/public/landing-page/intro-section/'.$data['intro_section']['rating_and_user_data']['review_user_icon'])); ?>"
                                 class="border border-2 rounded-circle" alt="<?php echo e(translate('image')); ?>">
                            <div class="media-body">
                                <div
                                        class="text-white"><?php echo e($data['intro_section']['rating_and_user_data']['reviewer_name']); ?></div>
                                <div class="star-rating text-warning fs-10">
                                    <?php
                                        $rating = $data['intro_section']['rating_and_user_data']['rating'];
                                    ?>

                                    <?php if($rating >= 0.5 && $rating <= 5): ?>
                                        <?php
                                            $fullStars = floor($rating);
                                            $halfStar = ($rating - $fullStars) >= 0.5;
                                        ?>

                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <?php if($i <= $fullStars): ?>
                                                <i class="bi bi-star-fill"></i>
                                            <?php elseif($halfStar && $i == ($fullStars + 1)): ?>
                                                <i class="bi bi-star-half"></i>
                                            <?php else: ?>
                                                <i class="bi bi-star"></i>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                    <?php else: ?>
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <i class="bi bi-star-fill"></i>
                                        <?php endfor; ?>
                                    <?php endif; ?>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-3">
                        <div class="">
                            <img width="48"
                                 src="<?php echo e($imageSource['user_image_one']); ?>"
                                 class="border border-2 rounded-circle" alt="<?php echo e(translate('image')); ?>">
                            <img width="48"
                                 src="<?php echo e($imageSource['user_image_two']); ?>"
                                 class="border border-2 rounded-circle ms-n3" alt="<?php echo e(translate('image')); ?>">
                            <img width="48"
                                 src="<?php echo e($imageSource['user_image_three']); ?>"
                                 class="border border-2 rounded-circle ms-n3" alt="<?php echo e(translate('image')); ?>">
                        </div>

                        <div class="text-white">
                            <h3 class="text-white mb-2"><?php echo e(format_number($data['intro_section']['rating_and_user_data']['total_user_count'])); ?>

                                + </h3>
                            <h6 class="fs-12 text-white"><?php echo e($data['intro_section']['rating_and_user_data']['total_user_content']); ?></h6>
                        </div>
                    </div>
                </div>
            </div>

            <div class="banner-mobile-frame d-flex gap-2 gap-sm-3 justify-content-center">
                <div class="banner-left-frame">
                    <img width="280"
                         src="<?php echo e($imageSource['intro_left_image']); ?>"
                         class="ss-img" alt="<?php echo e(translate('image')); ?>">
                    <img width="300" src="<?php echo e(asset('public/assets/landing/img/media/banner-mobile-frame.png')); ?>"
                         alt="<?php echo e(translate('image')); ?>">
                </div>
                <div class="banner-middle-frame">
                    <img width="280"
                         src="<?php echo e($imageSource['intro_middle_image']); ?>"
                        class="ss-img"
                         alt="<?php echo e(translate('image')); ?>">
                    <img width="300" src="<?php echo e(asset('public/assets/landing/img/media/banner-mobile-frame.png')); ?>"
                         alt="<?php echo e(translate('image')); ?>">
                </div>
                <div class="banner-right-frame">
                    <img width="280"
                         src="<?php echo e($imageSource['intro_right_image']); ?>"
                         class="ss-img"
                         alt="<?php echo e(translate('image')); ?>">
                    <img width="300" src="<?php echo e(asset('public/assets/landing/img/media/banner-mobile-frame.png')); ?>"
                         alt="<?php echo e(translate('image')); ?>">
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php /**PATH C:\laragon\www\arefan_wallet_admin\resources\views/landing/partials/home/<USER>/ ?>