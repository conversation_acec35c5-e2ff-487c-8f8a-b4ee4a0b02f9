{"__meta": {"id": "Xe9a422ce7e802b00c54f12063ec06f9f", "datetime": "2025-07-07 14:27:33", "utime": 1751876853.898108, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876853.511354, "end": 1751876853.898126, "duration": 0.38677191734313965, "duration_str": "387ms", "measures": [{"label": "Booting", "start": 1751876853.511354, "relative_start": 0, "end": 1751876853.762551, "relative_end": 1751876853.762551, "duration": 0.25119709968566895, "duration_str": "251ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876853.762564, "relative_start": 0.25120997428894043, "end": 1751876853.898128, "relative_end": 2.1457672119140625e-06, "duration": 0.13556408882141113, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002184, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876853.793743, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876853.883011, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-535954312 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-535954312\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1905597637 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1905597637\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-962503284 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-962503284\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2092485979 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Iktkd0J6NWVtdXllWVhuQzNDckZJSVE9PSIsInZhbHVlIjoiTXA3V1RHRThTOUZTTFhiZSt2UE1UY2NCd2JHcHAxQXQ0Y3B5c1FFZGxiczVWRXBtZmI5ZDJ6d3VWd0hKL1ZlSWRlQWh4RkF4S2toSlZWaW5iT3MzVS9CeXpsVnhvM2lkcnFPMTFNODFWSWJmdGNmek50cDg3WU16WHZjLy9OTE8iLCJtYWMiOiI5OWRiMGNkNTJhNzZiNmE1YjA0ZDk1MzNlYWIzNTJiZmY1ODI1ZDRlYzAzNjA0MjEwMzA0YzJjZTEyOTBmMThhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlZTeGwyMVdvM3F1aTI5ZkwzS1B6Mmc9PSIsInZhbHVlIjoiMVBZd1hYWGV2U0E2RzFvUTF6STdLRWZHWloyRWs2eHRxV0R6c3hKWEhGRE9ZMHZwcnkxbnZKdGFSUk9QN1VVRXhTSW9qL00xakNkTHE4cGpJZ1BFZTdTWVhTL0Q3b0N2QytCdE1LVGQzZERiVHFYcnJSS0tqSE1qM3VqUnkvZlUiLCJtYWMiOiJkMDJmY2FmNGE2Y2FjYzZjZDA2NzE3NWNhMjMwMjU3ZDZkZjkzNzEzMGE1YTkzMzYyOWJkODQ0YWY4MTNhMzcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2092485979\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1199351598 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199351598\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1320271136 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImRBR0lRd1orZHNBaUZDQ0lVMDN2a3c9PSIsInZhbHVlIjoiTlI3M1VzdGVRN3psbFNWeWpReWppSXlMK2xtZ1VIdjY5ZDV3MGFWYWR5UnB5d0k1WGM2bm9aYnhuQ3ZjdFlnOGNQSStTUGJYbi83T1BFRHk0S3Z5c3MrZ0d4UU1sUjJ3UXlUTlVDM0hod2ZqbCtMdkh1L0RsUEJ0cmlUdlNZcU8iLCJtYWMiOiIyNmZkYTJmNjE4MTkyNDhiZTgzMTZjZWIyMjZiZWNlNzEzYTViYjVhZjNkMTFhZDA5YzIxY2U2YTA0ODExZWQxIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:33 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InVvSThreW44M2F1S3EyVVJudTBFNFE9PSIsInZhbHVlIjoiT1QzQmoxRE1YcXBlbkdMSlllZnZwL29RWDVjZ1VEVGxWRkQ5bXhHMDBHTFYrWGZmc2h5NmNFU0NVeGVoZG9vbWRYd0dnQUgrNWxMYnMxUVphKy9WRk55SUIwbW1qYW10RFFTaENqWVhDbU1zL1dtQkhtbnVIZmJaMUVIVmppbzciLCJtYWMiOiJlMzUyYzBmNDZkZGRkMTc5Y2EzZjgyZjI4ZDJiOTQ4YTNhODQxM2NmM2NlNjYwZGRkNTBlOThjM2NlZWYzYmFjIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:33 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImRBR0lRd1orZHNBaUZDQ0lVMDN2a3c9PSIsInZhbHVlIjoiTlI3M1VzdGVRN3psbFNWeWpReWppSXlMK2xtZ1VIdjY5ZDV3MGFWYWR5UnB5d0k1WGM2bm9aYnhuQ3ZjdFlnOGNQSStTUGJYbi83T1BFRHk0S3Z5c3MrZ0d4UU1sUjJ3UXlUTlVDM0hod2ZqbCtMdkh1L0RsUEJ0cmlUdlNZcU8iLCJtYWMiOiIyNmZkYTJmNjE4MTkyNDhiZTgzMTZjZWIyMjZiZWNlNzEzYTViYjVhZjNkMTFhZDA5YzIxY2U2YTA0ODExZWQxIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InVvSThreW44M2F1S3EyVVJudTBFNFE9PSIsInZhbHVlIjoiT1QzQmoxRE1YcXBlbkdMSlllZnZwL29RWDVjZ1VEVGxWRkQ5bXhHMDBHTFYrWGZmc2h5NmNFU0NVeGVoZG9vbWRYd0dnQUgrNWxMYnMxUVphKy9WRk55SUIwbW1qYW10RFFTaENqWVhDbU1zL1dtQkhtbnVIZmJaMUVIVmppbzciLCJtYWMiOiJlMzUyYzBmNDZkZGRkMTc5Y2EzZjgyZjI4ZDJiOTQ4YTNhODQxM2NmM2NlNjYwZGRkNTBlOThjM2NlZWYzYmFjIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320271136\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1630217192 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630217192\", {\"maxDepth\":0})</script>\n"}}