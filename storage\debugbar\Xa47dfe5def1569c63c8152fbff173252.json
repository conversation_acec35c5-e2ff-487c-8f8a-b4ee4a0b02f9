{"__meta": {"id": "Xa47dfe5def1569c63c8152fbff173252", "datetime": "2025-07-07 14:27:24", "utime": **********.079249, "method": "GET", "uri": "/public/assets/installation/assets/img/svg-icons/database-name.svg", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876843.66669, "end": **********.0793, "duration": 0.4126098155975342, "duration_str": "413ms", "measures": [{"label": "Booting", "start": 1751876843.66669, "relative_start": 0, "end": **********.017688, "relative_end": **********.017688, "duration": 0.3509979248046875, "duration_str": "351ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.017734, "relative_start": 0.****************, "end": **********.079304, "relative_end": 4.0531158447265625e-06, "duration": 0.061569929122924805, "duration_str": "61.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/img/svg-icons/database-name.svg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/img/svg-icons/database-name.svg", "status_code": "<pre class=sf-dump id=sf-dump-1269453841 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1269453841\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1933900871 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1933900871\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1540078947 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1540078947\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1551257360 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkZFbGEwMlJtS2NmdDgyRXEyVlVjOEE9PSIsInZhbHVlIjoiREJaZ2ozcmVMRm8zUk14UERuNXF1QWNBckpyc0dUSy84QkM5RldZV3o4V0VUV1U4SUxJQ0xRMFBOQ1R0MEdOeUlhQ3BkbjVBVStBejlha2Vqd1RHZmFVZVFHUldmRjlkRldjbmE5SS96L2tyWFVnZkhwUWI0UU1wcWFMNVNtdjciLCJtYWMiOiJhMzdjODkwNDE4YzUzYWExZGI4OGNjYmYyMDk1YTA3YjIyODEyYThlMDFiOGI3NTBjNDg1OGJkNTUwMjAzMGI3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjFiUGZwbXBGUzdSTXdYdUk1cURXSlE9PSIsInZhbHVlIjoiUVo2cGhnYWhibWFPVjY1L05JTzVkSXlUcFhDM1Btd1RvYjN5Y0JRcmdudG5jU0NWQjRNck16UXIxemRoMmZDcXlrSGZaWUxiaEM2YnZtcURXUWhtRDlZbXpRRW8ra3NVOThGMDlFRDh3Mm5iT0ZNUWhjb3BTWFRXZVhGUy9XUWIiLCJtYWMiOiJjNzZkZGFkYjRiMTk5OThjYTEwM2Y1MDJlOTI2YmFmMzA0MzhhZjM0OWZiNzUzMDU5OTAyMzY0ZDQyNDZmZmYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551257360\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-746345224 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-746345224\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InpYUlNTems2MDl2bFM0d0RyZml1TXc9PSIsInZhbHVlIjoiMDlENjl1Q1dqaTZ2T1pOZSs1N1BHM1JrdGd5Wi85dEhRa2RXZ3RTc0N6WkRaTzF0eFo2RXNQSk16b2g0RE1YYUhId3BzWk9haTd6c3k0cE42WCtMQnFXQzBnOUtmcXF2TkZKem1qbk9DK3J2ZEdFQTkzUzRUK0dHYkh2NXoyWEMiLCJtYWMiOiI1NmFjNjM0MDBmZDcyMDZkNjNhZjk5MDQ4MjY4ODVlYjVkMTI1MTE1ZGNmMTFmZjQ5ZWQ0ZTg5NTAxNDIzMjQzIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:24 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkpkaUdtVUduNFhhQkZVcEVKc0RYVUE9PSIsInZhbHVlIjoiZXBuUFNMMmM3bHprdUE1Y3IrbkxwaXNUTTBUb09XV2JFdThtQnc1cGtQVUVrOGhud3dZV2lObTl1bXBwbzR0YkVkYTB1QWV0VnVsOXMyZ3AzeDJZSmZQKzFUOFF2Y3RBVGpYUU95VGxXYjF2T016Ynd2Q1RxcGhKWVdpSnRTenoiLCJtYWMiOiJlNTBmZGYyMjk5Y2YxODRlZTcxYzkwYjFkMzNmYzRhZDZhNmI3N2I4NjljMGEwNGI0NmJhYWNjOWUwZTI5Y2NhIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:24 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InpYUlNTems2MDl2bFM0d0RyZml1TXc9PSIsInZhbHVlIjoiMDlENjl1Q1dqaTZ2T1pOZSs1N1BHM1JrdGd5Wi85dEhRa2RXZ3RTc0N6WkRaTzF0eFo2RXNQSk16b2g0RE1YYUhId3BzWk9haTd6c3k0cE42WCtMQnFXQzBnOUtmcXF2TkZKem1qbk9DK3J2ZEdFQTkzUzRUK0dHYkh2NXoyWEMiLCJtYWMiOiI1NmFjNjM0MDBmZDcyMDZkNjNhZjk5MDQ4MjY4ODVlYjVkMTI1MTE1ZGNmMTFmZjQ5ZWQ0ZTg5NTAxNDIzMjQzIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkpkaUdtVUduNFhhQkZVcEVKc0RYVUE9PSIsInZhbHVlIjoiZXBuUFNMMmM3bHprdUE1Y3IrbkxwaXNUTTBUb09XV2JFdThtQnc1cGtQVUVrOGhud3dZV2lObTl1bXBwbzR0YkVkYTB1QWV0VnVsOXMyZ3AzeDJZSmZQKzFUOFF2Y3RBVGpYUU95VGxXYjF2T016Ynd2Q1RxcGhKWVdpSnRTenoiLCJtYWMiOiJlNTBmZGYyMjk5Y2YxODRlZTcxYzkwYjFkMzNmYzRhZDZhNmI3N2I4NjljMGEwNGI0NmJhYWNjOWUwZTI5Y2NhIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1504206316 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"87 characters\">http://127.0.0.1:8000/public/assets/installation/assets/img/svg-icons/database-name.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504206316\", {\"maxDepth\":0})</script>\n"}}