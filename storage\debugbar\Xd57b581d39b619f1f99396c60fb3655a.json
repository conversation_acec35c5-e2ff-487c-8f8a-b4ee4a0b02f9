{"__meta": {"id": "Xd57b581d39b619f1f99396c60fb3655a", "datetime": "2025-07-07 14:49:00", "utime": **********.120756, "method": "GET", "uri": "/api/v1/customer/get-banner", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751878139.683605, "end": **********.120769, "duration": 0.4371640682220459, "duration_str": "437ms", "measures": [{"label": "Booting", "start": 1751878139.683605, "relative_start": 0, "end": 1751878139.943453, "relative_end": 1751878139.943453, "duration": 0.25984811782836914, "duration_str": "260ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751878139.943468, "relative_start": 0.25986313819885254, "end": **********.120771, "relative_end": 1.9073486328125e-06, "duration": 0.17730283737182617, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24004664, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/customer/get-banner", "middleware": "api, deviceVerify, inactiveAuthCheck, trackLastActiveAt, auth:api, customerAuth, checkDeviceId", "controller": "App\\Http\\Controllers\\Api\\V1\\BannerController@getCustomerBanner", "namespace": "App\\Http\\Controllers\\Api\\V1\\Auth", "prefix": "api/v1/customer", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FBannerController.php&line=20\" onclick=\"\">app/Http/Controllers/Api/V1/BannerController.php:20-24</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.07542, "accumulated_duration_str": "75.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `oauth_access_tokens` where `id` = '4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946' limit 1", "type": "query", "params": [], "bindings": ["4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 100}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Bridge/AccessTokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Bridge\\AccessTokenRepository.php", "line": 88}, {"index": 18, "namespace": null, "name": "vendor/league/oauth2-server/src/AuthorizationValidators/BearerTokenValidator.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/league/oauth2-server/src/ResourceServer.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\ResourceServer.php", "line": 84}], "start": 1751878139.980315, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "TokenRepository.php:28", "source": "vendor/laravel/passport/src/TokenRepository.php:28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FTokenRepository.php&line=28", "ajax": false, "filename": "TokenRepository.php", "line": "28"}, "connection": "wallet_db", "start_percent": 0, "width_percent": 1.392}, {"sql": "select * from `oauth_access_tokens` where `id` = '4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946' limit 1", "type": "query", "params": [], "bindings": ["4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 100}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Bridge/AccessTokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Bridge\\AccessTokenRepository.php", "line": 88}, {"index": 18, "namespace": null, "name": "vendor/league/oauth2-server/src/AuthorizationValidators/BearerTokenValidator.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/league/oauth2-server/src/ResourceServer.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\league\\oauth2-server\\src\\ResourceServer.php", "line": 84}], "start": 1751878139.987386, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "TokenRepository.php:28", "source": "vendor/laravel/passport/src/TokenRepository.php:28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FTokenRepository.php&line=28", "ajax": false, "filename": "TokenRepository.php", "line": "28"}, "connection": "wallet_db", "start_percent": 1.392, "width_percent": 0.756}, {"sql": "select * from `oauth_clients` where `id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 93}, {"index": 19, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 152}], "start": 1751878139.9931061, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ClientRepository.php:47", "source": "vendor/laravel/passport/src/ClientRepository.php:47", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClientRepository.php&line=47", "ajax": false, "filename": "ClientRepository.php", "line": "47"}, "connection": "wallet_db", "start_percent": 2.148, "width_percent": 1.339}, {"sql": "select * from `users` where `id` = '2' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/PassportUserProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PassportUserProvider.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 111}, {"index": 19, "namespace": null, "name": "vendor/laravel/passport/src/PassportServiceProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php", "line": 309}], "start": **********.002065, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wallet_db", "start_percent": 3.487, "width_percent": 1.432}, {"sql": "select * from `oauth_access_tokens` where `id` = '4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946' limit 1", "type": "query", "params": [], "bindings": ["4965df202f80baefacb9c3e2b71db4f7addb27e9da864c9748e41e84cbd3f79cf27144a25f7c8946"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/TokenRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\TokenRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 170}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/PassportServiceProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php", "line": 309}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}], "start": **********.007486, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "TokenRepository.php:28", "source": "vendor/laravel/passport/src/TokenRepository.php:28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FTokenRepository.php&line=28", "ajax": false, "filename": "TokenRepository.php", "line": "28"}, "connection": "wallet_db", "start_percent": 4.919, "width_percent": 2.214}, {"sql": "select * from `oauth_clients` where `id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/passport/src/ClientRepository.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\ClientRepository.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 111}, {"index": 19, "namespace": null, "name": "vendor/laravel/passport/src/PassportServiceProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php", "line": 309}], "start": **********.0151079, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ClientRepository.php:47", "source": "vendor/laravel/passport/src/ClientRepository.php:47", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClientRepository.php&line=47", "ajax": false, "filename": "ClientRepository.php", "line": "47"}, "connection": "wallet_db", "start_percent": 7.133, "width_percent": 1.432}, {"sql": "select * from `business_settings` where (`key` = 'inactive_auth_minute') limit 1", "type": "query", "params": [], "bindings": ["inactive_auth_minute"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": "middleware", "name": "inactiveAuthCheck", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\InactiveAuthCheck.php", "line": 23}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": "middleware", "name": "deviceVerify", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\DeviceVerifyMiddleware.php", "line": 27}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.029421, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 8.565, "width_percent": 0.849}, {"sql": "update `users` set `last_active_at` = '2025-07-07 14:49:00', `users`.`updated_at` = '2025-07-07 14:49:00' where `id` = 2", "type": "query", "params": [], "bindings": ["2025-07-07 14:49:00", "2025-07-07 14:49:00", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "middleware", "name": "trackLastActiveAt", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\TrackLastActiveAt.php", "line": 22}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 16, "namespace": "middleware", "name": "inactiveAuthCheck", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\InactiveAuthCheck.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": "middleware", "name": "deviceVerify", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\DeviceVerifyMiddleware.php", "line": 27}], "start": **********.035505, "duration": 0.06614, "duration_str": "66.14ms", "memory": 0, "memory_str": null, "filename": "trackLastActiveAt:22", "source": "middleware::trackLastActiveAt:22", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FMiddleware%2FTrackLastActiveAt.php&line=22", "ajax": false, "filename": "TrackLastActiveAt.php", "line": "22"}, "connection": "wallet_db", "start_percent": 9.414, "width_percent": 87.696}, {"sql": "select * from `user_log_histories` where `user_id` = 2 and `device_id` = 'test-device-123' and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["2", "test-device-123", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "middleware", "name": "checkDeviceId", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\CheckDeviceId.php", "line": 29}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": "middleware", "name": "customerAuth", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\CustomerMiddleware.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": "middleware", "name": "trackLastActiveAt", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Middleware\\TrackLastActiveAt.php", "line": 26}], "start": **********.106097, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "checkDeviceId:29", "source": "middleware::checkDeviceId:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FMiddleware%2FCheckDeviceId.php&line=29", "ajax": false, "filename": "CheckDeviceId.php", "line": "29"}, "connection": "wallet_db", "start_percent": 97.11, "width_percent": 1.644}, {"sql": "select `title`, `image`, `url`, `receiver` from `banners` where (`receiver` = 'customers' or `receiver` = 'all') and `status` = 1", "type": "query", "params": [], "bindings": ["customers", "all", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/V1/BannerController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\BannerController.php", "line": 22}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1112459, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "BannerController.php:22", "source": "app/Http/Controllers/Api/V1/BannerController.php:22", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FBannerController.php&line=22", "ajax": false, "filename": "BannerController.php", "line": "22"}, "connection": "wallet_db", "start_percent": 98.754, "width_percent": 1.246}]}, "models": {"data": {"Laravel\\Passport\\Token": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FToken.php&line=1", "ajax": false, "filename": "Token.php", "line": "?"}}, "Laravel\\Passport\\Client": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fpassport%2Fsrc%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\UserLogHistory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FUserLogHistory.php&line=1", "ajax": false, "filename": "UserLogHistory.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/v1/customer/get-banner", "status_code": "<pre class=sf-dump id=sf-dump-1907285305 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1907285305\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1167227853 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1167227853\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1592846604 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1592846604\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1205539691 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>device-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">test-device-123</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>device-model</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Test Device</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>browser</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">Mobile App</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>os</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Android</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Dart/2.17 (dart:io)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205539691\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1126349553 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1126349553\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-432338024 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:49:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">57</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432338024\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-76769948 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-76769948\", {\"maxDepth\":0})</script>\n"}}