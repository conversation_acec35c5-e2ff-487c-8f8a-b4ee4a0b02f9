<?php if($data['screenshots_section']['status'] == '1' && $data['screenshots_section']['data']): ?>
    <section class="mt-5 py-5 bg-light overflow-hidden">
        <div class="swiper screenshot-slider" data-swiper-loop="true" data-swiper-items="auto" data-swiper-margin="50"
             data-swiper-center="true">
            <div class="slide-center-frame">
                <img width="250" src="<?php echo e(asset('public/assets/landing/img/media/banner-mobile-frame.png')); ?>"
                     alt="<?php echo e(translate('image')); ?>">
            </div>
            <div class="swiper-wrapper">
                <?php $__currentLoopData = $data['screenshots_section']['data'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="swiper-slide">
                        <img src="<?php echo e(asset('storage/app/public/landing-page/screenshots')); ?>/<?php echo e($item['image']); ?>"
                             alt="<?php echo e(translate('image')); ?>">
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="swiper-pagination position-relative mt-5"></div>
        </div>
    </section>
<?php endif; ?>
<?php /**PATH C:\laragon\www\arefan_wallet_admin\resources\views/landing/partials/home/<USER>/ ?>