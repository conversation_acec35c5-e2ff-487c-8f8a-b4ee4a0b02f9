{"__meta": {"id": "X3e654883c5485c999582f14c8585e18e", "datetime": "2025-07-07 14:27:37", "utime": 1751876857.955677, "method": "GET", "uri": "/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876857.315294, "end": 1751876857.955694, "duration": 0.6403999328613281, "duration_str": "640ms", "measures": [{"label": "Booting", "start": 1751876857.315294, "relative_start": 0, "end": 1751876857.613266, "relative_end": 1751876857.613266, "duration": 0.2979719638824463, "duration_str": "298ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876857.613278, "relative_start": 0.29798388481140137, "end": 1751876857.955696, "relative_end": 2.1457672119140625e-06, "duration": 0.34241819381713867, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24271488, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step1", "param_count": null, "params": [], "start": 1751876857.813497, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step1.blade.phpinstallation.step1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep1.blade.php&line=1", "ajax": false, "filename": "step1.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876857.936259, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET step1", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step1", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step1", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=28\" onclick=\"\">app/Http/Controllers/InstallController.php:28-54</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/step1", "status_code": "<pre class=sf-dump id=sf-dump-1345602189 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1345602189\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-515517691 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$IfmQQouRPyMJDbXprWeAa.c2n8TGp/MKj/uZLk3wXwil/zPHKHP6S</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-515517691\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1529538396 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1529538396\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2112176627 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im9TK3oxM0pCWEpMbGtMTStta25wZFE9PSIsInZhbHVlIjoiSktDMkFrbFVoYzcvTUk0cDNXZExibFRsbkFzOEVCWW84cUhUU09waGNFVjBTaFI1SnNiNDBHS1JPdk96N0NDbDhlcFNNOHEwd0F3N3A2M0pBaWwvUmxzdTRuRjc2a0Y5cHM4OHNIa0hZTG5DWXlHa0JteXdOVGlxVW1xMG5nUUsiLCJtYWMiOiIxMTdhYzU4NzZhNzU0OTcyYzc3ZDQ5MzVkZTEzYjE3OWIzY2FiZTA2YzJiZDIyZjRiODNmODgwNmRlNmYxNjQ1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjNUY3hSRHBWNk9yVjhrUGVPZFZhYVE9PSIsInZhbHVlIjoic3dISzcwcHNiR0Vzcm5yZnBiRlBacTBmWUNmWWwvOVZFWWE5aHdWVVB0ME1SQ05XRDFkU2QxdUtCbTNUWjhNaHMxVjZmM2UyN0ViWW0wMnhMQXlqSlVOeFNXWHRVcjk5UzVOMC9QbkJGUG15UTg4ZlN5RmZwSm83K3VBeTladTUiLCJtYWMiOiI5YzdlOWJjN2Q5ODMzZTlkYmVhMmMwY2FmYzlhZDM4ZDFiNGNmMGJmNTI0MmY1MTc4MmU3MTkyOGE3MjdhMGVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112176627\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-457216139 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-457216139\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1656835836 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InBCa3pmMXlhbUxFdFoyN3RLcDhSL1E9PSIsInZhbHVlIjoieVNWdytScjc1L05NN1ZIWUt3bXNCdUNGdi93a2V6TjBTRFppNEYvejdRdW05K2VyUVNVL05pZlMvYVRrbGNyb2U4Z3E0MFd3bXZZMzBTelpPQUE0QTBRYlB6TWpuV1lIY2FlM0VJWlBRaEVCeXc3VXpnV2JmNFBjWkE3d1NkNXYiLCJtYWMiOiJmMDRlNGMwMzE4ZDkwYzk5ZDRiZDI1MWFkYTdkZGIxNTliOTA4ZmQ4MjZmOWQ4YTYwMzBkODgyZWY4NmEwMmJkIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:37 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlMwcWZUMDhiTlNBK2xyQTVyMHVKS3c9PSIsInZhbHVlIjoiWHI1VWxIbkhac2tqaWh0R0N5SGdVOGNMWTdrTmZLOGxnRW96eHB0YjU2Qk5NdFo3VXphSzBGYVZETE02bjFPdnREbndkckZqTVJvNFlnMEJNNkYzbXZSeXFYQjNFd25hRFRoVDFHUjAwUmpDVWhpcWJBV1g3L081eFNZUkFwR2giLCJtYWMiOiJmNmEzNDJkYmMyY2FlNTUxZGUwOTAyYzcxN2M1NjQzYWRmYzVlZjAyNjU2YzkwY2I4Njg1MDM3ZTFmOGRjMjk1IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:37 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InBCa3pmMXlhbUxFdFoyN3RLcDhSL1E9PSIsInZhbHVlIjoieVNWdytScjc1L05NN1ZIWUt3bXNCdUNGdi93a2V6TjBTRFppNEYvejdRdW05K2VyUVNVL05pZlMvYVRrbGNyb2U4Z3E0MFd3bXZZMzBTelpPQUE0QTBRYlB6TWpuV1lIY2FlM0VJWlBRaEVCeXc3VXpnV2JmNFBjWkE3d1NkNXYiLCJtYWMiOiJmMDRlNGMwMzE4ZDkwYzk5ZDRiZDI1MWFkYTdkZGIxNTliOTA4ZmQ4MjZmOWQ4YTYwMzBkODgyZWY4NmEwMmJkIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlMwcWZUMDhiTlNBK2xyQTVyMHVKS3c9PSIsInZhbHVlIjoiWHI1VWxIbkhac2tqaWh0R0N5SGdVOGNMWTdrTmZLOGxnRW96eHB0YjU2Qk5NdFo3VXphSzBGYVZETE02bjFPdnREbndkckZqTVJvNFlnMEJNNkYzbXZSeXFYQjNFd25hRFRoVDFHUjAwUmpDVWhpcWJBV1g3L081eFNZUkFwR2giLCJtYWMiOiJmNmEzNDJkYmMyY2FlNTUxZGUwOTAyYzcxN2M1NjQzYWRmYzVlZjAyNjU2YzkwY2I4Njg1MDM3ZTFmOGRjMjk1IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656835836\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-238536928 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"106 characters\">http://127.0.0.1:8000/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-238536928\", {\"maxDepth\":0})</script>\n"}}