{"__meta": {"id": "X4b3404afc02c1c01ea12727b8f9402b3", "datetime": "2025-08-12 22:13:44", "utime": 1755015224.528782, "method": "GET", "uri": "/admin/notification/add-new", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755015223.439104, "end": 1755015224.528801, "duration": 1.0896968841552734, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1755015223.439104, "relative_start": 0, "end": 1755015224.401191, "relative_end": 1755015224.401191, "duration": 0.9620869159698486, "duration_str": "962ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1755015224.401211, "relative_start": 0.9621069431304932, "end": 1755015224.528803, "relative_end": 2.1457672119140625e-06, "duration": 0.1275920867919922, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23536960, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/notification/add-new", "middleware": "web, admin", "controller": "App\\Http\\Controllers\\Admin\\NotificationController@index", "as": "admin.notification.add-new", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/notification", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=25\" onclick=\"\">app/Http/Controllers/Admin/NotificationController.php:25-45</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RUXMC3HJeJD4B2tk4KRSmLGszkvjIPFeK6Yu43wH", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/notification/add-new\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/notification/add-new", "status_code": "<pre class=sf-dump id=sf-dump-777126426 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-777126426\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-321444545 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-321444545\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1355945632 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1355945632\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1247429476 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247429476\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-507630077 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-507630077\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2129767417 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 16:13:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlY3MTZLdUovT3pycnVjblpkaHJXZUE9PSIsInZhbHVlIjoiU0thQWhiMzhzWmFVNVFSKzBIWUc5UytoaU5xWFV4eE9YaTV3VHpGVndoME13VjBFdGdtOWowR25CQjc4aW4yT2NaUTlremJjcDhyYUw0TzR2UkZ4TXpiZEYwVjJISXl6WEdCdkxRb1hqRUFzcWI3dlV4a3dsOWJ3OGcwd1JhNkQiLCJtYWMiOiJiZDJjZmZiODRlYjE3YzE0MTAyYzUyNTRhNzA2MDU1MGNiM2RkYjFjNzRiZDA3MmRiYjE1YzI1MjA1MTE2YWQzIiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 18:13:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlVrcS8vV3dZcXd2NWFTc2s2eUNXYmc9PSIsInZhbHVlIjoiQ0xxeG5WblYyM2k0MG82aHhoZ09YenFXK0hHN2xmWEdaSDBwWkFvbW5ScmZIOXFnUmlFUE56QkJuSGVnUEl4Z0ptUjd6QkJWWjgvN0QyWVJuWHVQZ2szWHBBY25qSkltUjVMTE5KNWxaSlYrNkQwM3hTSkVQWDcvOGJBTGFsdHgiLCJtYWMiOiJmMDg0NTI4NzI2NWE1OWNkMDljZTk4ZTI3NzdmMzUyNGVmZjk4NzUzZDM5MTk2OTZhZDE5OTA2Y2I2MmI2OGFiIiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 18:13:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlY3MTZLdUovT3pycnVjblpkaHJXZUE9PSIsInZhbHVlIjoiU0thQWhiMzhzWmFVNVFSKzBIWUc5UytoaU5xWFV4eE9YaTV3VHpGVndoME13VjBFdGdtOWowR25CQjc4aW4yT2NaUTlremJjcDhyYUw0TzR2UkZ4TXpiZEYwVjJISXl6WEdCdkxRb1hqRUFzcWI3dlV4a3dsOWJ3OGcwd1JhNkQiLCJtYWMiOiJiZDJjZmZiODRlYjE3YzE0MTAyYzUyNTRhNzA2MDU1MGNiM2RkYjFjNzRiZDA3MmRiYjE1YzI1MjA1MTE2YWQzIiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 18:13:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlVrcS8vV3dZcXd2NWFTc2s2eUNXYmc9PSIsInZhbHVlIjoiQ0xxeG5WblYyM2k0MG82aHhoZ09YenFXK0hHN2xmWEdaSDBwWkFvbW5ScmZIOXFnUmlFUE56QkJuSGVnUEl4Z0ptUjd6QkJWWjgvN0QyWVJuWHVQZ2szWHBBY25qSkltUjVMTE5KNWxaSlYrNkQwM3hTSkVQWDcvOGJBTGFsdHgiLCJtYWMiOiJmMDg0NTI4NzI2NWE1OWNkMDljZTk4ZTI3NzdmMzUyNGVmZjk4NzUzZDM5MTk2OTZhZDE5OTA2Y2I2MmI2OGFiIiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 18:13:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2129767417\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RUXMC3HJeJD4B2tk4KRSmLGszkvjIPFeK6Yu43wH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/admin/notification/add-new</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}