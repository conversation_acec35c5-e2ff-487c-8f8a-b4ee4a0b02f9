{"__meta": {"id": "X16429cee1ab31f8d15e944543c3b52f8", "datetime": "2025-07-07 14:27:45", "utime": 1751876865.800596, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876865.396585, "end": 1751876865.800613, "duration": 0.40402793884277344, "duration_str": "404ms", "measures": [{"label": "Booting", "start": 1751876865.396585, "relative_start": 0, "end": 1751876865.651012, "relative_end": 1751876865.651012, "duration": 0.2544269561767578, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876865.651024, "relative_start": 0.254439115524292, "end": 1751876865.800615, "relative_end": 2.1457672119140625e-06, "duration": 0.14959096908569336, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002456, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876865.689116, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876865.786986, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-479928720 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-479928720\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-650303329 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-650303329\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1807812209 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1807812209\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-8092050 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"106 characters\">http://127.0.0.1:8000/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjUzQ3dlZnc4ZENJYnZpMjlhSzYzV1E9PSIsInZhbHVlIjoiM0FwcmxxVU03YzlKRlU2Q3AvQm1DWjY4cmRUb1NQZWtqWkMvVGRHaWFtK1RlanRZb3lqVkhkb2xHODE3dDc4Y3J4QmpXdGlOaEdrVlN4MnpIVVo3TzA1bUxwOG1FNnpQdjVZUnkwQmNqcml2cWxRVHdQNUowWm52WVN3YjBJYTMiLCJtYWMiOiJmMGFiNmVlNGQ3MjJlMjc5Y2NjZDllNjkwN2ExNzdlZTc0YThiZDM2MWE0NTViYjRiMTlkMTc0OTk2OGNiMTBiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImdCR0hEbWMvdmcxRlhoa05Qc1dvemc9PSIsInZhbHVlIjoiMHF2bm9GbHlOVnNndVdZSFpSV00xMHJUKy9zelBKYytvQThDNVVBOTVQN0wxazRwUmZEbWdBQVZuQnBHVmpuNHdlQitZeGhablBBY0RTUUMyYzgrVnJvOHhjN3dqczRKSTN1N0RJV3J4TjdjWHRxTHRkbC9ObGhuaVZjWTA2Z2giLCJtYWMiOiJmMjcxZGE0YjBjYmFjNWI4YmU4YTcxZDNkMGM5ZDRhMjIyZDY5MjY2YTMzNzNmZDhmYzIxM2FkN2QwYzRlZTA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8092050\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-860615604 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860615604\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-340501256 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im5nKzl2cDh3L3JLWDhkVHVDSnVDS2c9PSIsInZhbHVlIjoiVFBwWTJBemVPdk1GSTFDTlh4TzBNNEhGYStLRm1hbHZGV21wMVdwYVJuTmxMUkN4cWJrcDNEMkJ3am91K3E0UmN0SnZLQVZ2U0daNHc5ZjdDY1ZaaUZ3RWwzRWJvaHZrWlh0cFpzdElzS3BWMjc5NEVJcDdFaHpMSEV3NU85VmYiLCJtYWMiOiJkOTIwYjA1OTMwMzY2ZGU4OGRkZGJlZjE0NDQ3N2I1MTkxZGNhODM1N2EzZTE4NzM4NWUxNmE0NzczOTZiMWI2IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkVJYitlQXVhT3gzODFmN1ErTmtnOHc9PSIsInZhbHVlIjoiNlo3RytacXVmTnNqcXJiT1BsM05naWhCeFNXNUpjcFY3Y1JrV3BtMTFYZDZHVlg4NWhKR25PNi80a0laTitVenN3T3RSU09EMEM4WFVnKzI2cXdadlZhSXZmYXRWd2loM05HbGtyYllzNlhMSGorSmVrZStTVHozNDBtMUhmT3QiLCJtYWMiOiI5YjkzOTQ0MWQzMDQyMjdkOThlOTUzNzllYTA1ODE4NmYxZjk4Nzc0M2Y1MTg0ZGZlYzk5YjkwMmYzYjU2YWJjIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im5nKzl2cDh3L3JLWDhkVHVDSnVDS2c9PSIsInZhbHVlIjoiVFBwWTJBemVPdk1GSTFDTlh4TzBNNEhGYStLRm1hbHZGV21wMVdwYVJuTmxMUkN4cWJrcDNEMkJ3am91K3E0UmN0SnZLQVZ2U0daNHc5ZjdDY1ZaaUZ3RWwzRWJvaHZrWlh0cFpzdElzS3BWMjc5NEVJcDdFaHpMSEV3NU85VmYiLCJtYWMiOiJkOTIwYjA1OTMwMzY2ZGU4OGRkZGJlZjE0NDQ3N2I1MTkxZGNhODM1N2EzZTE4NzM4NWUxNmE0NzczOTZiMWI2IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkVJYitlQXVhT3gzODFmN1ErTmtnOHc9PSIsInZhbHVlIjoiNlo3RytacXVmTnNqcXJiT1BsM05naWhCeFNXNUpjcFY3Y1JrV3BtMTFYZDZHVlg4NWhKR25PNi80a0laTitVenN3T3RSU09EMEM4WFVnKzI2cXdadlZhSXZmYXRWd2loM05HbGtyYllzNlhMSGorSmVrZStTVHozNDBtMUhmT3QiLCJtYWMiOiI5YjkzOTQ0MWQzMDQyMjdkOThlOTUzNzllYTA1ODE4NmYxZjk4Nzc0M2Y1MTg0ZGZlYzk5YjkwMmYzYjU2YWJjIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340501256\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-352019677 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-352019677\", {\"maxDepth\":0})</script>\n"}}