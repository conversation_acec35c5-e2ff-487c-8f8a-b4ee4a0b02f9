/* ************************
   03.6: RTL
   ********************* */

   // [dir="rtl"] {
   //    .swiper-container, .swiper {
   //       direction: ltr;
   //    }
   //    .swiper-slide * {
   //       direction: rtl;
   //    }
   //    .breadcrumb-item+.breadcrumb-item {
   //      padding-left: 0;
   //      &::before {
   //          padding-left: var(--bs-breadcrumb-item-padding-x);
   //          float: right;
   //      }
   //    }
   //    .flipX-in-rtl {
   //       transform: scaleX(-1);
   //    }
   //    .nav {
   //       padding-inline-start: 0;
   //    }
   //    .aside-body .nav li.has-sub-item:after {
   //       transform: rotate(180deg);
   //    }
   //    .aside-body .nav li.sub-menu-opened:after {
   //       transform: rotate(90deg);
   //    }
   //    .start-0 {
   //       left: auto!important;
   //       right: 0!important;
   //    }
   //    .modal-header .btn-close {
   //       margin: calc(var(--bs-modal-header-padding-y) * -.5) auto calc(var(--bs-modal-header-padding-y) * -.5) calc(var(--bs-modal-header-padding-x) * -.5);
   //    }
   //    .dropdown-menu-end {
   //       right: auto !important;
   //       left: 0 !important;
   //    }
   // }