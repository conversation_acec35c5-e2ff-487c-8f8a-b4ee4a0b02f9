{"__meta": {"id": "Xb1bd23fadc75ed3416b396700e04d675", "datetime": "2025-07-07 14:45:07", "utime": **********.527691, "method": "POST", "uri": "/api/v1/customer/auth/register", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.091507, "end": **********.527721, "duration": 0.43621397018432617, "duration_str": "436ms", "measures": [{"label": "Booting", "start": **********.091507, "relative_start": 0, "end": **********.344983, "relative_end": **********.344983, "duration": 0.2534761428833008, "duration_str": "253ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.344995, "relative_start": 0.25348806381225586, "end": **********.527723, "relative_end": 2.1457672119140625e-06, "duration": 0.18272805213928223, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25101264, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/customer/auth/register", "middleware": "api, deviceVerify", "controller": "App\\Http\\Controllers\\Api\\V1\\RegisterController@customerRegistration", "namespace": "App\\Http\\Controllers\\Api\\V1\\Auth\\Auth", "prefix": "api/v1/customer/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=28\" onclick=\"\">app/Http/Controllers/Api/V1/RegisterController.php:28-113</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.01043, "accumulated_duration_str": "10.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `users` where `phone` = '1234567890' and (`deleted_at` is null)", "type": "query", "params": [], "bindings": ["1234567890"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 927}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 453}], "start": **********.3862278, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "wallet_db", "start_percent": 0, "width_percent": 8.821}, {"sql": "select * from `users` where (`phone` = '+*************') and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["+*************"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.394617, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "RegisterController.php:54", "source": "app/Http/Controllers/Api/V1/RegisterController.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=54", "ajax": false, "filename": "RegisterController.php", "line": "54"}, "connection": "wallet_db", "start_percent": 8.821, "width_percent": 6.136}, {"sql": "select * from `business_settings` where (`key` = 'phone_verification') limit 1", "type": "query", "params": [], "bindings": ["phone_verification"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 62}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.399786, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 14.957, "width_percent": 7.574}, {"sql": "select * from `phone_verifications` where (`phone` = '+*************' and `otp` = '9226') limit 1", "type": "query", "params": [], "bindings": ["+*************", "9226"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 64}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.404277, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "RegisterController.php:64", "source": "app/Http/Controllers/Api/V1/RegisterController.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=64", "ajax": false, "filename": "RegisterController.php", "line": "64"}, "connection": "wallet_db", "start_percent": 22.531, "width_percent": 8.341}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 78}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.415638, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "RegisterController.php:78", "source": "app/Http/Controllers/Api/V1/RegisterController.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=78", "ajax": false, "filename": "RegisterController.php", "line": "78"}, "connection": "wallet_db", "start_percent": 30.872, "width_percent": 0}, {"sql": "delete from `phone_verifications` where `id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 79}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 78}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.415931, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "RegisterController.php:79", "source": "app/Http/Controllers/Api/V1/RegisterController.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=79", "ajax": false, "filename": "RegisterController.php", "line": "79"}, "connection": "wallet_db", "start_percent": 30.872, "width_percent": 25.216}, {"sql": "insert into `users` (`f_name`, `l_name`, `image`, `gender`, `occupation`, `dial_country_code`, `phone`, `email`, `identification_image`, `password`, `type`, `referral_id`, `updated_at`, `created_at`) values ('John', 'Doe', '', 'male', 'Engineer', '+880', '+*************', '<EMAIL>', '[]', '$2y$10$nfx0aaqpGazRMt2erYEYyewzWJhx7hXVaZa0zR9Vjoi/8ryjwA6e6', 2, '', '2025-07-07 14:45:07', '2025-07-07 14:45:07')", "type": "query", "params": [], "bindings": ["<PERSON>", "<PERSON><PERSON>", "", "male", "Engineer", "+880", "+*************", "<EMAIL>", "[]", "$2y$10$nfx0aaqpGazRMt2erYEYyewzWJhx7hXVaZa0zR9Vjoi/8ryjwA6e6", "2", "", "2025-07-07 14:45:07", "2025-07-07 14:45:07"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 94}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 78}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.491997, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "RegisterController.php:94", "source": "app/Http/Controllers/Api/V1/RegisterController.php:94", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=94", "ajax": false, "filename": "RegisterController.php", "line": "94"}, "connection": "wallet_db", "start_percent": 56.088, "width_percent": 7.958}, {"sql": "select * from `users` where `users`.`id` = 2 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 96}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4962282, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "RegisterController.php:96", "source": "app/Http/Controllers/Api/V1/RegisterController.php:96", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=96", "ajax": false, "filename": "RegisterController.php", "line": "96"}, "connection": "wallet_db", "start_percent": 64.046, "width_percent": 5.944}, {"sql": "update `users` set `unique_id` = '267996', `users`.`updated_at` = '2025-07-07 14:45:07' where `id` = 2", "type": "query", "params": [], "bindings": ["267996", "2025-07-07 14:45:07", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 98}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 78}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5008311, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "RegisterController.php:98", "source": "app/Http/Controllers/Api/V1/RegisterController.php:98", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=98", "ajax": false, "filename": "RegisterController.php", "line": "98"}, "connection": "wallet_db", "start_percent": 69.99, "width_percent": 14.957}, {"sql": "insert into `e_money` (`user_id`, `updated_at`, `created_at`) values (2, '2025-07-07 14:45:07', '2025-07-07 14:45:07')", "type": "query", "params": [], "bindings": ["2", "2025-07-07 14:45:07", "2025-07-07 14:45:07"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 102}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 78}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.506691, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "RegisterController.php:102", "source": "app/Http/Controllers/Api/V1/RegisterController.php:102", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=102", "ajax": false, "filename": "RegisterController.php", "line": "102"}, "connection": "wallet_db", "start_percent": 84.947, "width_percent": 15.053}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "app/Http/Controllers/Api/V1/RegisterController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\RegisterController.php", "line": 78}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.518652, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "RegisterController.php:78", "source": "app/Http/Controllers/Api/V1/RegisterController.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FRegisterController.php&line=78", "ajax": false, "filename": "RegisterController.php", "line": "78"}, "connection": "wallet_db", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\BusinessSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\PhoneVerification": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FPhoneVerification.php&line=1", "ajax": false, "filename": "PhoneVerification.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/v1/customer/auth/register", "status_code": "<pre class=sf-dump id=sf-dump-457809420 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-457809420\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2069989255 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2069989255\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-928744661 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>dial_country_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">+880</span>\"\n  \"<span class=sf-dump-key>referral_code</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>otp</span>\" => \"<span class=sf-dump-str title=\"4 characters\">9226</span>\"\n  \"<span class=sf-dump-key>occupation</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Engineer</span>\"\n  \"<span class=sf-dump-key>f_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">John</span>\"\n  \"<span class=sf-dump-key>l_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Doe</span>\"\n  \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1234567890</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928744661\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1392779689 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Dart/2.17 (dart:io)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">283</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expect</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">100-continue</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392779689\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1788352109 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1788352109\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2121653804 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:45:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">56</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121653804\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-722364477 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-722364477\", {\"maxDepth\":0})</script>\n"}}