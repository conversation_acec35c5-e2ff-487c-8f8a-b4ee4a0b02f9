{"__meta": {"id": "X77f241adea1ebe6fd674e0e86b0a0acf", "datetime": "2025-07-07 14:27:36", "utime": **********.156624, "method": "GET", "uri": "/public/assets/installation/assets/img/logo.svg", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876855.559466, "end": **********.156641, "duration": 0.597175121307373, "duration_str": "597ms", "measures": [{"label": "Booting", "start": 1751876855.559466, "relative_start": 0, "end": **********.111463, "relative_end": **********.111463, "duration": 0.551997184753418, "duration_str": "552ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.111487, "relative_start": 0.****************, "end": **********.156643, "relative_end": 1.9073486328125e-06, "duration": 0.045156002044677734, "duration_str": "45.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/img/logo.svg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/img/logo.svg", "status_code": "<pre class=sf-dump id=sf-dump-1201286858 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1201286858\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1185054709 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1185054709\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1256550552 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1256550552\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-189540643 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjNmdUI2bFFhR2tVQnhRaXZLOVJIVnc9PSIsInZhbHVlIjoiNDBjRXJTeUtKZ3hzUFg4aWN5em0xTnNBQmgyNmxQemEzbStjbXZxdHhGY3hXeHdHSitHdThBLytQSTh3RCs4Tmk1NENxcUZ3ZTZ3MzJ5djNIdDI3dDlRUWJZQjlVTVo2bW9xUWZuMWxBakJDY2VZNjVuNjNONW9OeWtLY2pqckciLCJtYWMiOiIzNDJiMTk5MDQ4MDgzYjVkY2VlOTE0ZmVmZTVjODBhYTNhYTg0MzgyMGM1YmYzODA4ZjZhOGIwNTY5MDNkMWY1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5NVHVONUQyWDQ1OHk0bkRRc0RCZ2c9PSIsInZhbHVlIjoibjk3T0ExeTVaWml6RVNZSEpqZjFQenRHdFo1eEg4VlA1VDlNZGxJcnlwK2d0TkZiQy95djdLbTk0Ym1TNEE5eXhVUHErWkY0YVlONmVoeiszVTYzenlwdUxGNjNIcDFqTTdhYUVza1Z4WXBuSFMyeTk0OUlwQkFsTzQ2WEVzOFAiLCJtYWMiOiI0NzNiYzRhZWYxNzk1NDFiNDkyYjYzOTk2Y2MwMmIyMmMxN2IyYmM4NzQ4ZmIyNjFiOGExZGRjNzFmZGI0MTVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-189540643\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-297252476 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297252476\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im9TK3oxM0pCWEpMbGtMTStta25wZFE9PSIsInZhbHVlIjoiSktDMkFrbFVoYzcvTUk0cDNXZExibFRsbkFzOEVCWW84cUhUU09waGNFVjBTaFI1SnNiNDBHS1JPdk96N0NDbDhlcFNNOHEwd0F3N3A2M0pBaWwvUmxzdTRuRjc2a0Y5cHM4OHNIa0hZTG5DWXlHa0JteXdOVGlxVW1xMG5nUUsiLCJtYWMiOiIxMTdhYzU4NzZhNzU0OTcyYzc3ZDQ5MzVkZTEzYjE3OWIzY2FiZTA2YzJiZDIyZjRiODNmODgwNmRlNmYxNjQ1IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjNUY3hSRHBWNk9yVjhrUGVPZFZhYVE9PSIsInZhbHVlIjoic3dISzcwcHNiR0Vzcm5yZnBiRlBacTBmWUNmWWwvOVZFWWE5aHdWVVB0ME1SQ05XRDFkU2QxdUtCbTNUWjhNaHMxVjZmM2UyN0ViWW0wMnhMQXlqSlVOeFNXWHRVcjk5UzVOMC9QbkJGUG15UTg4ZlN5RmZwSm83K3VBeTladTUiLCJtYWMiOiI5YzdlOWJjN2Q5ODMzZTlkYmVhMmMwY2FmYzlhZDM4ZDFiNGNmMGJmNTI0MmY1MTc4MmU3MTkyOGE3MjdhMGVhIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im9TK3oxM0pCWEpMbGtMTStta25wZFE9PSIsInZhbHVlIjoiSktDMkFrbFVoYzcvTUk0cDNXZExibFRsbkFzOEVCWW84cUhUU09waGNFVjBTaFI1SnNiNDBHS1JPdk96N0NDbDhlcFNNOHEwd0F3N3A2M0pBaWwvUmxzdTRuRjc2a0Y5cHM4OHNIa0hZTG5DWXlHa0JteXdOVGlxVW1xMG5nUUsiLCJtYWMiOiIxMTdhYzU4NzZhNzU0OTcyYzc3ZDQ5MzVkZTEzYjE3OWIzY2FiZTA2YzJiZDIyZjRiODNmODgwNmRlNmYxNjQ1IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjNUY3hSRHBWNk9yVjhrUGVPZFZhYVE9PSIsInZhbHVlIjoic3dISzcwcHNiR0Vzcm5yZnBiRlBacTBmWUNmWWwvOVZFWWE5aHdWVVB0ME1SQ05XRDFkU2QxdUtCbTNUWjhNaHMxVjZmM2UyN0ViWW0wMnhMQXlqSlVOeFNXWHRVcjk5UzVOMC9QbkJGUG15UTg4ZlN5RmZwSm83K3VBeTladTUiLCJtYWMiOiI5YzdlOWJjN2Q5ODMzZTlkYmVhMmMwY2FmYzlhZDM4ZDFiNGNmMGJmNTI0MmY1MTc4MmU3MTkyOGE3MjdhMGVhIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-267018680 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"68 characters\">http://127.0.0.1:8000/public/assets/installation/assets/img/logo.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-267018680\", {\"maxDepth\":0})</script>\n"}}