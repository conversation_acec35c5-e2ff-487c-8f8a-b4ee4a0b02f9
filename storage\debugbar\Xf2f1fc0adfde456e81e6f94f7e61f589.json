{"__meta": {"id": "Xf2f1fc0adfde456e81e6f94f7e61f589", "datetime": "2025-07-07 14:27:32", "utime": **********.704764, "method": "GET", "uri": "/public/assets/installation/assets/img/favicon.svg", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.42244, "end": **********.704783, "duration": 0.28234291076660156, "duration_str": "282ms", "measures": [{"label": "Booting", "start": **********.42244, "relative_start": 0, "end": **********.665918, "relative_end": **********.665918, "duration": 0.24347805976867676, "duration_str": "243ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.665935, "relative_start": 0.*****************, "end": **********.704785, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "38.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/img/favicon.svg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/img/favicon.svg", "status_code": "<pre class=sf-dump id=sf-dump-1937740954 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1937740954\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1403436236 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1403436236\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1698112605 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1698112605\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1313617429 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImsrOUNkcFQwbzdNaDl1NmxxVGRxb3c9PSIsInZhbHVlIjoibTFJa1BNL1lHSnlsUnRZOXFyM1ZDVWNhdGExNmU1dzNNYm91SjNjd0QrMnVRdXZTU3cvdXVBa1piSWlCOUQ3NkllUGNERll1UUVBdXJQd0J4V254MzFWQytaRVFnOVJpb1hjSDhwYWJVNjA5Wk5SY0tsYTZjTWFLSFQyQU9CTUgiLCJtYWMiOiI0MWE2ZmQwMTIwZDE2ZGQ4ODczNDQ3ZDM1ZmU4OTg3ZGUyZGRiYWU4ZmQyYWVhMWY2OTFlNmRmMWI3YzdlZGQ4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5Pb3dWbVFRMS9MSWJiOWM4eGRRZ1E9PSIsInZhbHVlIjoibW4yMXRxazFXWklqN1AzWFY5ek40QjQxclhtM2dpNEt5M09uMDlxUDRuTFhMNW81bXVFcDNQN1pBRkxNUGtUR0YvWWZLQmxwcnhMS3d3NVJselFpbWlhZU5hU0RqVjBTNEh5NzlIYnpndk0zK3BiendkQjJZd0hlZG9IQnhrRVEiLCJtYWMiOiJkYTkxOTQzOGYxMDYxMmFlZDFlZmY5OWM5Y2M0NWExNTZlOTY5ZTRiN2FlZGQyMzY3MmI4MGQxMWExMTNjNWZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313617429\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1536676385 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1536676385\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-691318944 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ims2MEJkWTgxcFF1djYrTlJXV0xnRUE9PSIsInZhbHVlIjoiWDVNSmhCNloxdWVDa1Q5K21iNDhaVUgyMTBUaEg5TXM5NVhpcW53Q0lnQjlnMGVGdGZ3aXJRSEQ0MDMxRko4MG00MUlFWmwxTVJITlVramxWdllEMFN2amRpNVdpajFUOTNFdzZpcjg1cWZlRUVPbDNnN0pNQm54VjlIam5NQ0kiLCJtYWMiOiIzZTRmN2IyNTkwZTlkYTY2ZTY1M2I2MGMxZWY0NDQ4MzBkZjJmYjZhMzhkNjdiNDZlZDc1MGZmOTM4YjQ5MDk3IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InNkdTh2MHowbE5XampQbGNRcnZ0Qmc9PSIsInZhbHVlIjoidWxZL1pqZnJxZ04xLzkzbGpJNWpRd0lnOHZpOTk2ZTB4eC9NQkZwOVFnd0d6RFpzaXBVMXAzUi9MT0hNaXlBczI0K2YvR1l3MUNmc3JPZWo5cGE0UjhrdTR1T292V1Rwa09CbENqeDVWWVo2SHNBSUxhOFBlOUJTWDhkSEo4cU8iLCJtYWMiOiIyYjIxZmFiN2VkODFkODgwYTNhNTFiZmM3YWNmZGRhN2M1NzRhZDcwNmZhMWI0OWM5MTBkZWE5MTgzOTQ0Y2FkIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ims2MEJkWTgxcFF1djYrTlJXV0xnRUE9PSIsInZhbHVlIjoiWDVNSmhCNloxdWVDa1Q5K21iNDhaVUgyMTBUaEg5TXM5NVhpcW53Q0lnQjlnMGVGdGZ3aXJRSEQ0MDMxRko4MG00MUlFWmwxTVJITlVramxWdllEMFN2amRpNVdpajFUOTNFdzZpcjg1cWZlRUVPbDNnN0pNQm54VjlIam5NQ0kiLCJtYWMiOiIzZTRmN2IyNTkwZTlkYTY2ZTY1M2I2MGMxZWY0NDQ4MzBkZjJmYjZhMzhkNjdiNDZlZDc1MGZmOTM4YjQ5MDk3IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InNkdTh2MHowbE5XampQbGNRcnZ0Qmc9PSIsInZhbHVlIjoidWxZL1pqZnJxZ04xLzkzbGpJNWpRd0lnOHZpOTk2ZTB4eC9NQkZwOVFnd0d6RFpzaXBVMXAzUi9MT0hNaXlBczI0K2YvR1l3MUNmc3JPZWo5cGE0UjhrdTR1T292V1Rwa09CbENqeDVWWVo2SHNBSUxhOFBlOUJTWDhkSEo4cU8iLCJtYWMiOiIyYjIxZmFiN2VkODFkODgwYTNhNTFiZmM3YWNmZGRhN2M1NzRhZDcwNmZhMWI0OWM5MTBkZWE5MTgzOTQ0Y2FkIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691318944\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-54539688 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"71 characters\">http://127.0.0.1:8000/public/assets/installation/assets/img/favicon.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54539688\", {\"maxDepth\":0})</script>\n"}}