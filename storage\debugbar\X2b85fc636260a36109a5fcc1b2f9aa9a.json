{"__meta": {"id": "X2b85fc636260a36109a5fcc1b2f9aa9a", "datetime": "2025-07-07 14:30:29", "utime": 1751877029.719131, "method": "GET", "uri": "/admin", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751877029.429403, "end": 1751877029.71916, "duration": 0.28975701332092285, "duration_str": "290ms", "measures": [{"label": "Booting", "start": 1751877029.429403, "relative_start": 0, "end": 1751877029.664309, "relative_end": 1751877029.664309, "duration": 0.23490595817565918, "duration_str": "235ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751877029.66432, "relative_start": 0.23491692543029785, "end": 1751877029.719162, "relative_end": 1.9073486328125e-06, "duration": 0.05484199523925781, "duration_str": "54.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23880000, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin", "middleware": "web, admin", "controller": "App\\Http\\Controllers\\Admin\\DashboardController@dashboard", "as": "admin.dashboard", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=33\" onclick=\"\">app/Http/Controllers/Admin/DashboardController.php:33-82</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin", "status_code": "<pre class=sf-dump id=sf-dump-1335879598 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1335879598\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1867394347 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1867394347\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-718093684 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-718093684\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1365496037 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikw0TUFnbVFwRkRhOGlyZE5zL2JxTkE9PSIsInZhbHVlIjoiRERZMUpMdmR5NjA3L3BwNjdpQ1VUODRzSEFta3pjbG1XK0hZbFdGK2F3QU9lQithUkF1UDdJbmJrYUpIK2ZEVzcvMzFMTk11ODZORURJTDFqZ0U1NU1tYm4vL0NJTkx5dzhtcjF6ME15RDEySkRMNTZZMWh2VWZTcG9kcmhIUHUiLCJtYWMiOiJjYjkxNWNlNWU0Yjg2YTNkNzUyYzg2NGQxNWQwNDM1YzU4YjgxZWE2NjI5MTQxNzZiMDIxOTgxMDAzYTAyZGFiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImFOY1pTVUlqbzR1bW1DQmZVSklxMlE9PSIsInZhbHVlIjoiRWxNL0lZWjVsNitjMGhhSHpvbG5wS2JUN1JNQnVnL2IvS0dHRXFOQVAwY0VVNGJvanRoK0RnTnA4d3c1WU1ZM1JMYWpvb3JpcVFaSDUyMm1sZXZPVXA4WUlSejBXTHAwczAzRUJxczlvM0htL01rNEhneW9ORGhjNkFKb3hVd1ciLCJtYWMiOiJhODhlYTU2Y2RjOWNiYzdlN2JlYzRjNzk2YzVlMzRiYjdjODE2MmUzOWY1ZjAzZDQ4ODVmZTY0ZGZhZTJkZWFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365496037\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1178646239 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1178646239\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-805454612 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:30:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjN4MjhXSzFGOE9ESTk1YkM4Wkw2SEE9PSIsInZhbHVlIjoidzRXaEEwa3drWWZLbmdSeEVNOUhVaWJUd053OHN0SUlWcTk4eTZBRHV0MEFBbWFpa1dncTRCYUROOVUyTmFGaGlzMFA0NVN0TEtJMTFoRkhZZ3JZWGwvQ2F0REY3ZkFKZis1ZVVOUG5tcU8wZk5kaEZQUXEveThReVR4bzVsa0oiLCJtYWMiOiI4MGVhOTc3NTNjODMzMWI2OWFiOWViNDBjNDMwOWNmOGViODU0ZjZjN2ZiNzU2MGZiMTQ3YmU0OTgzNWMwYmZmIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:30:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjJ4VkFLeG50azF1byswTmcybmt5UUE9PSIsInZhbHVlIjoielhQK01XNzRKU1RFS3lIWnN0TDRuczhGM2s1cjhCVFVDckdORHVBK1c5ZWpyMjg1UGhMVFpESVQzbnF3Q09BLzYzYVNMSjl6bFR0c21YdEtUQmM2Y2pYSkdxeXhzaVJRREZtY3ZjNkxneE9Ja0NiQU9MTURyMDdiZzJyRXlrUkEiLCJtYWMiOiI4ODFhNDIwNGMzNzRkYzg1MjA3ZWQ4MGRhNmI3M2I5NTQyYWYwYTcyODY5NTI0NDQyNWI1OGY2ZDQ0YjQ1MzFmIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:30:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjN4MjhXSzFGOE9ESTk1YkM4Wkw2SEE9PSIsInZhbHVlIjoidzRXaEEwa3drWWZLbmdSeEVNOUhVaWJUd053OHN0SUlWcTk4eTZBRHV0MEFBbWFpa1dncTRCYUROOVUyTmFGaGlzMFA0NVN0TEtJMTFoRkhZZ3JZWGwvQ2F0REY3ZkFKZis1ZVVOUG5tcU8wZk5kaEZQUXEveThReVR4bzVsa0oiLCJtYWMiOiI4MGVhOTc3NTNjODMzMWI2OWFiOWViNDBjNDMwOWNmOGViODU0ZjZjN2ZiNzU2MGZiMTQ3YmU0OTgzNWMwYmZmIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:30:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjJ4VkFLeG50azF1byswTmcybmt5UUE9PSIsInZhbHVlIjoielhQK01XNzRKU1RFS3lIWnN0TDRuczhGM2s1cjhCVFVDckdORHVBK1c5ZWpyMjg1UGhMVFpESVQzbnF3Q09BLzYzYVNMSjl6bFR0c21YdEtUQmM2Y2pYSkdxeXhzaVJRREZtY3ZjNkxneE9Ja0NiQU9MTURyMDdiZzJyRXlrUkEiLCJtYWMiOiI4ODFhNDIwNGMzNzRkYzg1MjA3ZWQ4MGRhNmI3M2I5NTQyYWYwYTcyODY5NTI0NDQyNWI1OGY2ZDQ0YjQ1MzFmIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:30:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-805454612\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-604733844 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-604733844\", {\"maxDepth\":0})</script>\n"}}