{"__meta": {"id": "X34ead7092eaa8ae4fe843528de867585", "datetime": "2025-07-07 14:27:45", "utime": **********.375855, "method": "GET", "uri": "/public/assets/installation/assets/img/logo.svg", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.107043, "end": **********.375873, "duration": 0.2688300609588623, "duration_str": "269ms", "measures": [{"label": "Booting", "start": **********.107043, "relative_start": 0, "end": **********.339796, "relative_end": **********.339796, "duration": 0.23275303840637207, "duration_str": "233ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.339809, "relative_start": 0.*****************, "end": **********.375876, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "36.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET {fallbackPlaceholder}", "middleware": "web", "uses": "Closure() {#311\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#299 …}\n  file: \"C:\\laragon\\www\\arefan_wallet_admin\\routes\\install.php\"\n  line: \"20 to 22\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Froutes%2Finstall.php&line=20\" onclick=\"\">routes/install.php:20-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/public/assets/installation/assets/img/logo.svg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/public/assets/installation/assets/img/logo.svg", "status_code": "<pre class=sf-dump id=sf-dump-1665571881 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1665571881\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1070523568 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1070523568\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1434023350 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1434023350\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-43436075 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"106 characters\">http://127.0.0.1:8000/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikp6ZXpNM0hEeW1vZXArWjJkaTE1WWc9PSIsInZhbHVlIjoibE41YzFYU1BLL0ZUSDFFUmY1Sk1JWjhsVE5lNG00VDVid0gwcitHQnVhbE1hdnFNcERhcy93dFhaK1BndmVkcXJ6amRiM09IczYwMzZ3ZzRaTFFGVThheC9CRGkwNTFBZDNySG1sd1FxZFhpdGZnK00ySmlVV2JWRUNhd3AyekgiLCJtYWMiOiI5ZGExYmY3MDBiZmU5MmYwNDk5YzIzYjliOWYzYTY3OTYxNDk1ZTkyMWE2MTkwN2EzNDU0MWE3ZWM2MTlhNDMyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkszQjVMUmRLZ3ZyOVRvTUM4WHFUYmc9PSIsInZhbHVlIjoiWTRUMjlzeDBaZEpXWXVFK1AycXJ3L0ZoRHVkKzRMT0JzUEhwR1R1Zi8rZnpLTmFDemlwWmFCVlJjckpyT055b0p5M2hyUVJYYjhoaDVqckxBWVA1RjdzK09ndGFqTUlxeVZxUW9XY3l6N2V6VktCRGhOUDA1RmYwWFBsaFZPN04iLCJtYWMiOiI1ZjUyNzEzNTllYTM2NzcwYmRlOTE5YzBmY2UzYWY0Y2JlNDI4MzFhZGFmNmM1YWRlM2UyN2M1NzRhYzMxOGY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43436075\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1533508405 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533508405\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1830595783 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImoxaWNiVXFWMjBEcGRLUXBxWlI2cWc9PSIsInZhbHVlIjoialUyMCtwWFp1bmtkK1hmTUxrQjc4dzJNeEErNkNaeDlsOUdhLyszOXVlNzl2eFkvekdmT2UvWjJHYXNqNzRkOVBxVE9MS3M0SWR0QkNjSGx0RmttMGtndms4UENXYkdWd1JxRklJTWRpRXVvekRRdFlITlB1c0hINzF2aWNqSUgiLCJtYWMiOiI1ODVkMmVmODk5MGZiNWZhMWNmYzUxNDBmNDk0YzY3YjI1ZWRhMzVhZTRmYzE2MGU4ZmY1YzNmNzE4YmFjZWZiIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InQvTVVwRGJsNjlNN1k3S1FwMzJVNkE9PSIsInZhbHVlIjoiQ2dXOW1MY3ArYmFXWjB6cmRqaXBnUW1tVE9qQXFtY3FXS3U0MVZjU25WOW5XUmV0SDdpNGNlWWMyT3RlWklNUER1S0VIVVMzRURud3A2UHdpNDVtMlNGOVVEdEV5RWc0NVBZb1NrMjFwZTVOZzVacU9IQ2xZNmN3NTcvbHVZUjUiLCJtYWMiOiJjYmRhYzFmMTNhNzYzZmVhMTkzZGRiYjczMzMzZDhiNjE2YTViN2E2YTVlZTlmYjYxOTZmOTYwMWJiMDFlZTIxIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImoxaWNiVXFWMjBEcGRLUXBxWlI2cWc9PSIsInZhbHVlIjoialUyMCtwWFp1bmtkK1hmTUxrQjc4dzJNeEErNkNaeDlsOUdhLyszOXVlNzl2eFkvekdmT2UvWjJHYXNqNzRkOVBxVE9MS3M0SWR0QkNjSGx0RmttMGtndms4UENXYkdWd1JxRklJTWRpRXVvekRRdFlITlB1c0hINzF2aWNqSUgiLCJtYWMiOiI1ODVkMmVmODk5MGZiNWZhMWNmYzUxNDBmNDk0YzY3YjI1ZWRhMzVhZTRmYzE2MGU4ZmY1YzNmNzE4YmFjZWZiIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InQvTVVwRGJsNjlNN1k3S1FwMzJVNkE9PSIsInZhbHVlIjoiQ2dXOW1MY3ArYmFXWjB6cmRqaXBnUW1tVE9qQXFtY3FXS3U0MVZjU25WOW5XUmV0SDdpNGNlWWMyT3RlWklNUER1S0VIVVMzRURud3A2UHdpNDVtMlNGOVVEdEV5RWc0NVBZb1NrMjFwZTVOZzVacU9IQ2xZNmN3NTcvbHVZUjUiLCJtYWMiOiJjYmRhYzFmMTNhNzYzZmVhMTkzZGRiYjczMzMzZDhiNjE2YTViN2E2YTVlZTlmYjYxOTZmOTYwMWJiMDFlZTIxIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1830595783\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1099521630 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"68 characters\">http://127.0.0.1:8000/public/assets/installation/assets/img/logo.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099521630\", {\"maxDepth\":0})</script>\n"}}