# Test Customer Login and API Functions
$headers = @{
    'Content-Type' = 'application/json'
    'User-Agent' = 'Dart/2.17 (dart:io)'
    'device-id' = 'test-device-123'
    'browser' = 'Mobile App'
    'os' = 'Android'
    'device-model' = 'Test Device'
}

# Test Customer Login
Write-Host "=== Customer Login Test ==="
$loginBody = @{
    'phone' = '1234567890'
    'dial_country_code' = '+880'
    'password' = '1234'
} | ConvertTo-Json

Write-Host "Login Request Body: $loginBody"

try {
    $loginResponse = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/customer/auth/login' -Method POST -Body $loginBody -Headers $headers
    Write-Host "✅ Login Success:"
    $loginResponse | ConvertTo-Json -Depth 10
    
    # Store the token for further API calls
    $customerToken = $loginResponse.content
    Write-Host "`nCustomer Token: $customerToken"
    
    # Test Authenticated Endpoints
    Write-Host "`n=== Testing Authenticated Endpoints ==="
    
    # Add Authorization header
    $authHeaders = @{
        'Content-Type' = 'application/json'
        'User-Agent' = 'Dart/2.17 (dart:io)'
        'Authorization' = "Bearer $customerToken"
        'device-id' = 'test-device-123'
        'browser' = 'Mobile App'
        'os' = 'Android'
        'device-model' = 'Test Device'
    }
    
    # Test Get Customer Profile
    Write-Host "`n--- Get Customer Profile ---"
    try {
        $profileResponse = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/customer/get-customer' -Method GET -Headers $authHeaders
        Write-Host "✅ Profile Success:"
        $profileResponse | ConvertTo-Json -Depth 10
    } catch {
        Write-Host "❌ Profile Error:"
        Write-Host "Status Code:" $_.Exception.Response.StatusCode
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body:" $responseBody
        }
    }
    
    # Test Get Transaction History
    Write-Host "`n--- Get Transaction History ---"
    try {
        $transactionResponse = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/customer/transaction-history' -Method GET -Headers $authHeaders
        Write-Host "✅ Transaction History Success:"
        $transactionResponse | ConvertTo-Json -Depth 10
    } catch {
        Write-Host "❌ Transaction History Error:"
        Write-Host "Status Code:" $_.Exception.Response.StatusCode
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body:" $responseBody
        }
    }
    
    # Test Get Banners
    Write-Host "`n--- Get Customer Banners ---"
    try {
        $bannerResponse = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/customer/get-banner' -Method GET -Headers $authHeaders
        Write-Host "✅ Banner Success:"
        $bannerResponse | ConvertTo-Json -Depth 10
    } catch {
        Write-Host "❌ Banner Error:"
        Write-Host "Status Code:" $_.Exception.Response.StatusCode
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body:" $responseBody
        }
    }
    
    # Test Get Notifications
    Write-Host "`n--- Get Customer Notifications ---"
    try {
        $notificationResponse = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/customer/get-notification' -Method GET -Headers $authHeaders
        Write-Host "✅ Notification Success:"
        $notificationResponse | ConvertTo-Json -Depth 10
    } catch {
        Write-Host "❌ Notification Error:"
        Write-Host "Status Code:" $_.Exception.Response.StatusCode
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body:" $responseBody
        }
    }
    
} catch {
    Write-Host "❌ Login Error:"
    Write-Host "Status Code:" $_.Exception.Response.StatusCode
    Write-Host "Status Description:" $_.Exception.Response.StatusDescription
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body:" $responseBody
    }
}

Write-Host "`n=== Test Complete ==="
