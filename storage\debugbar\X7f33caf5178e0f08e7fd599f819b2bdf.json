{"__meta": {"id": "X7f33caf5178e0f08e7fd599f819b2bdf", "datetime": "2025-08-12 16:41:10", "utime": 1754995270.096127, "method": "GET", "uri": "/admin/auth/code/captcha/1", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754995269.689991, "end": 1754995270.096146, "duration": 0.4061551094055176, "duration_str": "406ms", "measures": [{"label": "Booting", "start": 1754995269.689991, "relative_start": 0, "end": 1754995270.038136, "relative_end": 1754995270.038136, "duration": 0.3481450080871582, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754995270.038153, "relative_start": 0.3481619358062744, "end": 1754995270.096148, "relative_end": 1.9073486328125e-06, "duration": 0.05799508094787598, "duration_str": "58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23595032, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/auth/code/captcha/{tmp}", "middleware": "web, guest:user", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@captcha", "as": "admin.auth.default-captcha", "namespace": "App\\Http\\Controllers\\Admin\\Auth", "prefix": "admin/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=28\" onclick=\"\">app/Http/Controllers/Admin/Auth/LoginController.php:28-48</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/auth/code/captcha/1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "default_captcha_code": "vkPu", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/auth/code/captcha/1", "status_code": "<pre class=sf-dump id=sf-dump-1253257678 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1253257678\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1149484711 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1149484711\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-878315352 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-878315352\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-556674339 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InB0VWFHalZ3alN6TytIb3lycW1aTEE9PSIsInZhbHVlIjoic05XTUVaRWRhcFZ5MWMwbFkrVXN0OTRuTmk3NjJpeE5lcEZYQ2d0NlRuMUFCNXExVUhSMEZZczhJR0ZpQThsd0lKaWwvd2FVaW45VmxLSU9RdHpJUW12U1F3KzlNN2xqMHNNZDVHaE4wOFZ0VFd4aTNMaWJXMldPYS9lR2NsRjgiLCJtYWMiOiJmNWJiYTMzOWRlOTEwMWFiNGQwM2YyNmQ2YWZiYzllM2NiOTY4YmUzYTk0ZTM1YTk3ZmJlMTkyMWJmMzc1Mzk3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVwOFBZbkVUZS9YbTdrdGlsMmdBMVE9PSIsInZhbHVlIjoidzVWM0gzZk1lazdYVm0rMC9RMGE4SnZUcFF3RVNPemhBSjRsbm8wQ0hVVlA5NXhxT2orTlIzcjlGNVpKRnJ5ZkZYYllIZytOdUxMajI1ZnNYUVdVTm5LVW1zNWljMndHK1FjVy9ad3ZwZ09qTGllOXlNUEJDeUVqYmRldEUyUy8iLCJtYWMiOiIzZTU5Y2VmNjliOWU1Zjg4NzE1YjRhNzQ5MzJiYWEwYTljYWU3ZDVmYTczOTYzZjE3Y2E4MWYyMjVkNzk1ZTA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556674339\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-776590355 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xXBna3nsIuOcOZy2mqUA7gFRJDRGHJs4xurDvGRe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776590355\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-481160611 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 10:41:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkYzd3ByT0d6SGIwa240eXhvaStNWWc9PSIsInZhbHVlIjoiYkhSTFJnczJNZnNPQWN5UGNRRHBWRnZoV21DZXVid0FHTkdXZm1sTHFlcENtQWdNUDJzZ3djM2ZQQmtRYUdPcVJDdlZ1NXlpMHpaYjVXaTYyNkJrVjVacUszUUs0WittWkMzME4yaTNQY0NBYlFCYTJVM0FmR1ZhTjliSFRqMzIiLCJtYWMiOiIwMTQ4NzUyOTY2OWQzNjEzYjllYzJhMGIyMmZhNGUxN2IwMzA5MGQ5ZDVmYzk0MjRmYmI4ZTE4ZDg0NWM0MDg5IiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:41:10 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkFYSEpUOUcvMnJmcXJINVQrbDN0M0E9PSIsInZhbHVlIjoiWFp1OVY4am1aSENLS051K29ldGE5THg2OXM0UkN6bVdIdzBHK1BHbGVrTmFZalZ5YnZSczFNdzgrTDdJbkN3bnl1TnFuMUxERXVXckttWERRbnFCSnRWMlMxVitLQmUrY0xEY09MSUJ1TVdCdElwVGdLZjNkZURMRDVhVXo2R3oiLCJtYWMiOiI2YTliNDJmNzkwMjhjMmFhZjUzYjU2ZmQ4MjU5YWIyNDI1Yjg3M2U3OTRlNzFkMDM5MGM1MzkyOWVkOGZlMzI2IiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:41:10 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkYzd3ByT0d6SGIwa240eXhvaStNWWc9PSIsInZhbHVlIjoiYkhSTFJnczJNZnNPQWN5UGNRRHBWRnZoV21DZXVid0FHTkdXZm1sTHFlcENtQWdNUDJzZ3djM2ZQQmtRYUdPcVJDdlZ1NXlpMHpaYjVXaTYyNkJrVjVacUszUUs0WittWkMzME4yaTNQY0NBYlFCYTJVM0FmR1ZhTjliSFRqMzIiLCJtYWMiOiIwMTQ4NzUyOTY2OWQzNjEzYjllYzJhMGIyMmZhNGUxN2IwMzA5MGQ5ZDVmYzk0MjRmYmI4ZTE4ZDg0NWM0MDg5IiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:41:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkFYSEpUOUcvMnJmcXJINVQrbDN0M0E9PSIsInZhbHVlIjoiWFp1OVY4am1aSENLS051K29ldGE5THg2OXM0UkN6bVdIdzBHK1BHbGVrTmFZalZ5YnZSczFNdzgrTDdJbkN3bnl1TnFuMUxERXVXckttWERRbnFCSnRWMlMxVitLQmUrY0xEY09MSUJ1TVdCdElwVGdLZjNkZURMRDVhVXo2R3oiLCJtYWMiOiI2YTliNDJmNzkwMjhjMmFhZjUzYjU2ZmQ4MjU5YWIyNDI1Yjg3M2U3OTRlNzFkMDM5MGM1MzkyOWVkOGZlMzI2IiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:41:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-481160611\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2114763566 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/admin/auth/code/captcha/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>default_captcha_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">vkPu</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2114763566\", {\"maxDepth\":0})</script>\n"}}