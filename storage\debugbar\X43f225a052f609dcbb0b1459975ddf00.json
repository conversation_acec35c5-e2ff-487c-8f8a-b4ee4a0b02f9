{"__meta": {"id": "X43f225a052f609dcbb0b1459975ddf00", "datetime": "2025-07-07 14:42:34", "utime": 1751877754.150243, "method": "GET", "uri": "/admin/auth/code/captcha/1", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751877753.850463, "end": 1751877754.150267, "duration": 0.2998039722442627, "duration_str": "300ms", "measures": [{"label": "Booting", "start": 1751877753.850463, "relative_start": 0, "end": 1751877754.092931, "relative_end": 1751877754.092931, "duration": 0.24246811866760254, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751877754.092953, "relative_start": 0.24249005317687988, "end": 1751877754.15027, "relative_end": 3.0994415283203125e-06, "duration": 0.05731701850891113, "duration_str": "57.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23734496, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/auth/code/captcha/{tmp}", "middleware": "web, guest:user", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@captcha", "as": "admin.auth.default-captcha", "namespace": "App\\Http\\Controllers\\Admin\\Auth", "prefix": "admin/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=28\" onclick=\"\">app/Http/Controllers/Admin/Auth/LoginController.php:28-48</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/auth/code/captcha/1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "default_captcha_code": "4MR4", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/auth/code/captcha/1", "status_code": "<pre class=sf-dump id=sf-dump-1823666114 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1823666114\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1394016862 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1394016862\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-774349439 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-774349439\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1417194266 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlpVb091cExyUzJZRHk2V0wwRjJJcnc9PSIsInZhbHVlIjoiMk05a0d1N3d4T0pZcDJ2TGhUeU9EbmxRcUdnYnF4U0tGK09RbWNDWFRWRlBEQXNhZGZDL0JTM3kxUzFhSGNPSlF4dndhVzROTE9UU0xCeW43UmlNZE4ybnRQTFV2bDZpQU81YkgwTlBDb05qVWQvWkJ3VVhYOGRteU5lbkdjSXAiLCJtYWMiOiIyYTA2NDdkMzg1YzY4NjM4ODUwMzE4OGQxYjE3NDBjYjQ1ZTYzYmRiNGRiZGU4MjI2OTg1M2ZhNTJmM2QzY2Y2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjBtQUdueXZEcitlaStSYk9OaFF0WkE9PSIsInZhbHVlIjoiVGN4MUVKRm5GOGtsUHVIYlVWdStYbWZRY2t2aHU0Ny9OQXBhRDFUd2ttb2JmNGdkUmE3elJCaTRkMHQzcVdSTnppdkpzMkJvcUtTY2Y2NUZBdUJTTmZuM1FrZUZ4dHR2a3ZWZHpON05mQnZROG9jSmFSTW5MRlptQUxKK1MyWjEiLCJtYWMiOiIwYWVjZWE4NDk1ZDgxNDFhZTQ1MmJjODcxNTA5OTFjZDZjZDdkN2JhOTQwMGNkYjM3MWU4ZGU2YTE4ZWQ3MmZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1417194266\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1442126774 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442126774\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-891939222 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:42:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik0xQm85RmIvZit4Y1ZmQWRxUkVHWUE9PSIsInZhbHVlIjoiMjNISE5PM2F5SDRnL3hHR2tTcVRSdG1rOUJkOGZIWVBubVVWc0VpcDVhZFAxSmpGMWFwWDR6TENRTUhIVlQrNCthQTIzU0liODlLaG4rSXlNZlZsVzd4ZjMxTi9VdjlaaVo4MTR2aDdoK21qZzUzcVdZZEM5U28xMU8yM0lzQWQiLCJtYWMiOiJkMjQxYWVmZmU1OWUwZjVhYmFlNDg5ODY0NjMwMWMyZjQyMDI3Mjg3MmY0MThhOWM2NmQ5YjMwZjZjODNiY2VlIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:42:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkRPVUlhaE9NUnJldFB3Yklqa24yR0E9PSIsInZhbHVlIjoiRUZSTUVzNlBvZGxUOXFBcFNPRUFKbHRScW14Sk5MR3lreVZoRlp1R1ZLcm42cG5lcUVGTktZNU5iNTNDWElGclBpQjNDZkxHSWlhMGJTaFpQQkQ3S3p4eW82UVh1TDhlRDcxdzRzb0V1WjBLOG5PbUIvV2tyQmNiZHQ4SnRHSngiLCJtYWMiOiJhMGEzN2RiZGU2MDBmODExN2ViNzlkOTA3MjhlOWIzZDZmN2E1ZTIwMDU0ZDc2ZDU2YTNiNzBmYjUxNzkzNzA2IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:42:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik0xQm85RmIvZit4Y1ZmQWRxUkVHWUE9PSIsInZhbHVlIjoiMjNISE5PM2F5SDRnL3hHR2tTcVRSdG1rOUJkOGZIWVBubVVWc0VpcDVhZFAxSmpGMWFwWDR6TENRTUhIVlQrNCthQTIzU0liODlLaG4rSXlNZlZsVzd4ZjMxTi9VdjlaaVo4MTR2aDdoK21qZzUzcVdZZEM5U28xMU8yM0lzQWQiLCJtYWMiOiJkMjQxYWVmZmU1OWUwZjVhYmFlNDg5ODY0NjMwMWMyZjQyMDI3Mjg3MmY0MThhOWM2NmQ5YjMwZjZjODNiY2VlIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:42:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkRPVUlhaE9NUnJldFB3Yklqa24yR0E9PSIsInZhbHVlIjoiRUZSTUVzNlBvZGxUOXFBcFNPRUFKbHRScW14Sk5MR3lreVZoRlp1R1ZLcm42cG5lcUVGTktZNU5iNTNDWElGclBpQjNDZkxHSWlhMGJTaFpQQkQ3S3p4eW82UVh1TDhlRDcxdzRzb0V1WjBLOG5PbUIvV2tyQmNiZHQ4SnRHSngiLCJtYWMiOiJhMGEzN2RiZGU2MDBmODExN2ViNzlkOTA3MjhlOWIzZDZmN2E1ZTIwMDU0ZDc2ZDU2YTNiNzBmYjUxNzkzNzA2IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:42:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891939222\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1902944695 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/admin/auth/code/captcha/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>default_captcha_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4MR4</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902944695\", {\"maxDepth\":0})</script>\n"}}