{"__meta": {"id": "X43a37596a5fee62e6a4736a6e02ae9d9", "datetime": "2025-07-07 14:27:26", "utime": 1751876846.494881, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876846.136049, "end": 1751876846.494903, "duration": 0.3588540554046631, "duration_str": "359ms", "measures": [{"label": "Booting", "start": 1751876846.136049, "relative_start": 0, "end": 1751876846.358408, "relative_end": 1751876846.358408, "duration": 0.22235894203186035, "duration_str": "222ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876846.358419, "relative_start": 0.22236990928649902, "end": 1751876846.494906, "relative_end": 2.86102294921875e-06, "duration": 0.13648700714111328, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002040, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876846.390571, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876846.482317, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-860701548 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-860701548\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-370835729 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-370835729\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-460927092 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-460927092\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1474558942 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InpYUlNTems2MDl2bFM0d0RyZml1TXc9PSIsInZhbHVlIjoiMDlENjl1Q1dqaTZ2T1pOZSs1N1BHM1JrdGd5Wi85dEhRa2RXZ3RTc0N6WkRaTzF0eFo2RXNQSk16b2g0RE1YYUhId3BzWk9haTd6c3k0cE42WCtMQnFXQzBnOUtmcXF2TkZKem1qbk9DK3J2ZEdFQTkzUzRUK0dHYkh2NXoyWEMiLCJtYWMiOiI1NmFjNjM0MDBmZDcyMDZkNjNhZjk5MDQ4MjY4ODVlYjVkMTI1MTE1ZGNmMTFmZjQ5ZWQ0ZTg5NTAxNDIzMjQzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkpkaUdtVUduNFhhQkZVcEVKc0RYVUE9PSIsInZhbHVlIjoiZXBuUFNMMmM3bHprdUE1Y3IrbkxwaXNUTTBUb09XV2JFdThtQnc1cGtQVUVrOGhud3dZV2lObTl1bXBwbzR0YkVkYTB1QWV0VnVsOXMyZ3AzeDJZSmZQKzFUOFF2Y3RBVGpYUU95VGxXYjF2T016Ynd2Q1RxcGhKWVdpSnRTenoiLCJtYWMiOiJlNTBmZGYyMjk5Y2YxODRlZTcxYzkwYjFkMzNmYzRhZDZhNmI3N2I4NjljMGEwNGI0NmJhYWNjOWUwZTI5Y2NhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474558942\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2145990373 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2145990373\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1612296009 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InBLT052NDFLMGhRTFIzRzN5cTQ3RXc9PSIsInZhbHVlIjoiMmh0TDQrR2ZxSVhWVU1WTjE2ajh1UCtUOURkZStaSlkzdE5IOGp1ZUVBQ1lPck1WLzI0bFo3aGxXRVVTZFE4RDVyQyszd2FjejFPazBoZzl5bGJYT0tWUEQva2xDbExCNS91RERxa2N3ZzdUWVdmQUZITUROL3NrYWRIaTV5UGwiLCJtYWMiOiJiMmU1MjBjOTNiZmE2YTY4MjdiMmUxODBiYzUzYzZjMjQ0NjgyMmFkMmE0MzNmYzI0MzZmODU4NTEyNTY0ZjkyIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjdSWjVsMlBIVjdoSzAyMXY4Yk5Kb1E9PSIsInZhbHVlIjoiT1Q0T01DTUozNE1LUWl1emZQakx2NWJNcFdyR1hCRWVKOU5uUG96YmJMaGxybnF1MDU4UXhMYmNZVHlsY3hQc3kvREhGY3lIVXB0STBLYlM3VjBzRk1ZUU5hOUpQQWtvUnVSQkc5RDNyVW5IS1Erd0Foc2xIMWhxWlFJNEgvNCsiLCJtYWMiOiJiODllZjg4MDU4ZWJhYjUxMjkwNWQxNmEyMmNlYjVhYzhlMzAzMjIzMzFjN2NjZjRlYmZmODI0NDk0YjcyNTBjIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InBLT052NDFLMGhRTFIzRzN5cTQ3RXc9PSIsInZhbHVlIjoiMmh0TDQrR2ZxSVhWVU1WTjE2ajh1UCtUOURkZStaSlkzdE5IOGp1ZUVBQ1lPck1WLzI0bFo3aGxXRVVTZFE4RDVyQyszd2FjejFPazBoZzl5bGJYT0tWUEQva2xDbExCNS91RERxa2N3ZzdUWVdmQUZITUROL3NrYWRIaTV5UGwiLCJtYWMiOiJiMmU1MjBjOTNiZmE2YTY4MjdiMmUxODBiYzUzYzZjMjQ0NjgyMmFkMmE0MzNmYzI0MzZmODU4NTEyNTY0ZjkyIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjdSWjVsMlBIVjdoSzAyMXY4Yk5Kb1E9PSIsInZhbHVlIjoiT1Q0T01DTUozNE1LUWl1emZQakx2NWJNcFdyR1hCRWVKOU5uUG96YmJMaGxybnF1MDU4UXhMYmNZVHlsY3hQc3kvREhGY3lIVXB0STBLYlM3VjBzRk1ZUU5hOUpQQWtvUnVSQkc5RDNyVW5IS1Erd0Foc2xIMWhxWlFJNEgvNCsiLCJtYWMiOiJiODllZjg4MDU4ZWJhYjUxMjkwNWQxNmEyMmNlYjVhYzhlMzAzMjIzMzFjN2NjZjRlYmZmODI0NDk0YjcyNTBjIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612296009\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1044131017 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044131017\", {\"maxDepth\":0})</script>\n"}}