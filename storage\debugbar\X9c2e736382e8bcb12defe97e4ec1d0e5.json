{"__meta": {"id": "X9c2e736382e8bcb12defe97e4ec1d0e5", "datetime": "2025-07-07 14:27:23", "utime": 1751876843.33136, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876842.759721, "end": 1751876843.331387, "duration": 0.5716660022735596, "duration_str": "572ms", "measures": [{"label": "Booting", "start": 1751876842.759721, "relative_start": 0, "end": 1751876843.156573, "relative_end": 1751876843.156573, "duration": 0.3968520164489746, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876843.156592, "relative_start": 0.39687085151672363, "end": 1751876843.33139, "relative_end": 2.86102294921875e-06, "duration": 0.17479801177978516, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24001896, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876843.210519, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876843.314082, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-335125255 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-335125255\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-469171867 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-469171867\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-852929559 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-852929559\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-387062282 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkdQZS96OGtib1Z0SUxxMGE3a25SQmc9PSIsInZhbHVlIjoibHJFSC9ZV2YyWjJqZ0UvQWkrTndTeHpjdnVadEREeXBvNjM0MmZmbmd1T3FDV2Y1bElLbnArNUNvemROUm9zOVZhSDFWNUYrc3p3R2RDSU1qZFp6dUhmWWVoQUM0bHY3R1c2amdSaHg4Qk1xRU9kemVyL1I3Ny9vaGE1bU1DL1oiLCJtYWMiOiJhZmU2YmU3N2Y4NzNmZjczZThiY2RhZjcxOGM0ODgzYTRiNmUwZTNiMGEwMmE2OTRmYTM0MjU0ZjVjMGY1MjM0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkpobE5ESXhEek5za2d0T3cyb2xOU2c9PSIsInZhbHVlIjoic001dG52UEwxeld0YXFGMUlvQjFTNXlkY29obnFLQnJRbU4xVTNhcHFWaVpZTEhYNzhFaFp1dEMrQ1hrVk43cnd1RkZRaFFjVGZTQy82ekpocHd4K2QranB0RkZ1bkM4SVVGT3dLNVplc0ppN09EZnNEK2g4ZCt5Z1VYYUI5Ui8iLCJtYWMiOiJlN2FmMDU4YTE3MmE5Yjc1ZGJmYjkxN2UxZmRjNzI0ZjQ2NTQ1M2UxMWJkY2Q3Yjc3NmY3ZTU4OWZkMWUyMTliIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387062282\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1376562045 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376562045\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1532126270 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InVNakQ2NEd3RTExNkJ4M25nanpaWVE9PSIsInZhbHVlIjoiL1Y1eUovczRNSG1nMnN3aDFjb3JlQzFVV2xWSmFJL1RjTDBqRVM2Y3NnWHhuTkZTNk9ya3cxM3JTdS9HSXlTMktTRDNodFFuWUk1cjN5WmxMaTNCQWhQU3N6VlExTHdEZlczQkRPVmdNN3ppYjhtTTRYenVOaEpvVTNlT0NUVXgiLCJtYWMiOiIyNmVmMzI0MTkwMThlNTI1ZTZmYWE0Y2Y4NjFiYTc4NDRhNzVmMDEyYWE4ZTk2Njk3MDg3NDI1NzU5ZTFkY2NiIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlluTklEaVViTWladEVhU2hRY0U4SUE9PSIsInZhbHVlIjoiZlJ0c3lDVTZZR2E2NExsR0c0K3N0bUExUWo4OUFsY2JXVWJTMGIwcWRuVUdrbnNYS1poc0hYQzVpZ1FjY2RNSVRxSnRRL3BsUmhucnBpaC9CRzJQTjc0QlVSQ3oxUjhqKy9RMXhZdXRCR25hL1NtQ1FNNHBqUzJ0ZTRibGx4ZXAiLCJtYWMiOiI5ZDNmMzJiNTg1M2YwMGI4YzQ3ZWRlZTc3OTRmNjFhZTcyNjAwMGVlYjQ5Y2MzYjM2ZGVmZmFmYTViMzFjZWMzIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InVNakQ2NEd3RTExNkJ4M25nanpaWVE9PSIsInZhbHVlIjoiL1Y1eUovczRNSG1nMnN3aDFjb3JlQzFVV2xWSmFJL1RjTDBqRVM2Y3NnWHhuTkZTNk9ya3cxM3JTdS9HSXlTMktTRDNodFFuWUk1cjN5WmxMaTNCQWhQU3N6VlExTHdEZlczQkRPVmdNN3ppYjhtTTRYenVOaEpvVTNlT0NUVXgiLCJtYWMiOiIyNmVmMzI0MTkwMThlNTI1ZTZmYWE0Y2Y4NjFiYTc4NDRhNzVmMDEyYWE4ZTk2Njk3MDg3NDI1NzU5ZTFkY2NiIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlluTklEaVViTWladEVhU2hRY0U4SUE9PSIsInZhbHVlIjoiZlJ0c3lDVTZZR2E2NExsR0c0K3N0bUExUWo4OUFsY2JXVWJTMGIwcWRuVUdrbnNYS1poc0hYQzVpZ1FjY2RNSVRxSnRRL3BsUmhucnBpaC9CRzJQTjc0QlVSQ3oxUjhqKy9RMXhZdXRCR25hL1NtQ1FNNHBqUzJ0ZTRibGx4ZXAiLCJtYWMiOiI5ZDNmMzJiNTg1M2YwMGI4YzQ3ZWRlZTc3OTRmNjFhZTcyNjAwMGVlYjQ5Y2MzYjM2ZGVmZmFmYTViMzFjZWMzIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532126270\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2089011922 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2089011922\", {\"maxDepth\":0})</script>\n"}}