{"title": "SB Admin 2", "name": "startbootstrap-sb-admin-2", "version": "4.1.1", "scripts": {"start": "node_modules/.bin/gulp watch"}, "description": "An open source Bootstrap 4 admin theme.", "keywords": ["css", "sass", "html", "responsive", "theme", "template", "admin", "app"], "homepage": "https://startbootstrap.com/themes/sb-admin-2", "bugs": {"url": "https://github.com/StartBootstrap/startbootstrap-sb-admin-2/issues", "email": "<EMAIL>"}, "license": "MIT", "author": "Start Bootstrap", "contributors": ["<PERSON> (http://davidmiller.io/)"], "repository": {"type": "git", "url": "https://github.com/StartBootstrap/startbootstrap-sb-admin-2.git"}, "dependencies": {"@fortawesome/fontawesome-free": "5.13.1", "bootstrap": "4.5.0", "chart.js": "2.9.3", "datatables.net-bs4": "1.10.21", "jquery": "3.5.1", "jquery.easing": "^1.4.1"}, "devDependencies": {"browser-sync": "2.26.7", "del": "5.1.0", "gulp": "4.0.2", "gulp-autoprefixer": "7.0.1", "gulp-clean-css": "4.3.0", "gulp-header": "2.0.9", "gulp-plumber": "^1.2.1", "gulp-rename": "2.0.0", "gulp-sass": "4.1.0", "gulp-uglify": "3.0.2", "merge-stream": "2.0.0"}}