<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>AREFAN WALLET - Your All-in-One Neo Banking Experience</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>
  <script src="https://unpkg.com/@heroicons/react@2.0.16/dist/outline/index.esm.js" type="module"></script>
  <style>
    body {
      font-family: 'Inter', sans-serif;
    }
    .fade-in {
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.8s ease, transform 0.8s ease;
    }
    .fade-in.visible {
      opacity: 1;
      transform: translateY(0);
    }
    .hover-scale:hover {
      transform: scale(1.05);
      transition: transform 0.3s ease;
    }
    .btn-primary {
      background-color: #A3E635;
      color: #064E3B;
      font-weight: 600;
      border: none;
      border-radius: 0.5rem;
      padding: 0.75rem 1.5rem;
      transition: all 0.3s ease;
    }
    .btn-primary:hover {
      background-color: #85c72a;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(163, 230, 53, 0.3);
    }
    .btn-secondary {
      background-color: transparent;
      color: #A3E635;
      font-weight: 600;
      border: 2px solid #A3E635;
      border-radius: 0.5rem;
      padding: 0.75rem 1.5rem;
      transition: all 0.3s ease;
    }
    .btn-secondary:hover {
      background-color: rgba(163, 230, 53, 0.1);
      transform: translateY(-2px);
    }
    /* Header Styles */
    .navbar {
      transition: all 0.3s ease;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    .navbar.scrolled {
      background-color: rgba(6, 78, 59, 0.95);
      backdrop-filter: blur(10px);
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    }
    .navbar a {
      position: relative;
      transition: color 0.3s ease;
    }
    .navbar a:hover {
      color: #A3E635;
    }
    .navbar a::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 0;
      height: 2px;
      background-color: #A3E635;
      transition: width 0.3s ease;
    }
    .navbar a:hover::after {
      width: 100%;
    }
    .mobile-menu {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }
    .mobile-menu.open {
      max-height: 500px;
    }
    /* Custom Scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #A3E635;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #85c72a;
    }
    /* Animation */
    @keyframes blob {
      0% {
        transform: translate(0px, 0px) scale(1);
      }
      33% {
        transform: translate(30px, -50px) scale(1.1);
      }
      66% {
        transform: translate(-20px, 20px) scale(0.9);
      }
      100% {
        transform: translate(0px, 0px) scale(1);
      }
    }
    .animate-blob {
      animation: blob 7s infinite;
    }
    .animation-delay-2000 {
      animation-delay: 2s;
    }
    .animation-delay-4000 {
      animation-delay: 4s;
    }
  </style>
</head>
<body class="bg-gray-50 text-gray-800">
  <!-- Header -->
  <header class="navbar fixed w-full z-50 py-4 px-6 md:px-12 bg-transparent sticky top-0">
    <div class="max-w-6xl mx-auto flex justify-between items-center">
      <!-- Logo -->
      <div class="flex items-center">
        <img src="img/logo.png" alt="AREFAN WALLET Logo" class="h-10"/>
        <!-- <span class="ml-3 text-xl font-bold text-white hidden sm:block">AREFAN WALLET</span> -->
      </div>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex space-x-10 text-white">
        <a href="#features" class="font-medium hover:text-[#A3E635] transition">Features</a>
        <a href="#how-it-works" class="font-medium hover:text-[#A3E635] transition">How It Works</a>
        <a href="#testimonials" class="font-medium hover:text-[#A3E635] transition">Testimonials</a>
        <a href="#pricing" class="font-medium hover:text-[#A3E635] transition">Pricing</a>
      </nav>

      <!-- CTA Buttons -->
      <div class="hidden md:flex items-center space-x-4">
        <!-- <button class="btn-secondary text-white border-white hover:bg-primary hover:bg-opacity-10">Log In</button> -->
        <button class="btn-primary">Downlaod Our App</button>
      </div>

      <!-- Mobile Menu Button -->
      <button id="mobile-menu-button" class="md:hidden text-white focus:outline-none">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="mobile-menu md:hidden mt-4 bg-[#064E3B] rounded-lg shadow-lg">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
        <a href="#features" class="block px-3 py-2 rounded-md text-base font-medium text-white hover:bg-[#095F49]">Features</a>
        <a href="#how-it-works" class="block px-3 py-2 rounded-md text-base font-medium text-white hover:bg-[#095F49]">How It Works</a>
        <a href="#testimonials" class="block px-3 py-2 rounded-md text-base font-medium text-white hover:bg-[#095F49]">Testimonials</a>
        <a href="#pricing" class="block px-3 py-2 rounded-md text-base font-medium text-white hover:bg-[#095F49]">Pricing</a>
        <div class="pt-4 pb-2 border-t border-gray-700 flex flex-col space-y-3">
          <button class="btn-secondary text-white border-white hover:bg-white hover:bg-opacity-10 w-full">Log In</button>
          <button class="btn-primary w-full">Sign Up</button>
        </div>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section id="hero" class="relative min-h-screen flex items-center justify-center px-6 md:px-12 pt-20 pb-16 bg-gradient-to-br from-[#064E3B] via-[#095F49] to-[#0D7B5A] text-white overflow-hidden">
    <!-- Background Illustration -->
    <div class="absolute inset-0 z-0">
      <img src="img/9302740.jpg" alt="Fintech Background" class="w-full h-full object-cover opacity-30"/>
      <div class="absolute inset-0 bg-gradient-to-r from-[#064E3B] to-transparent"></div>
    </div>

    <!-- Content -->
    <div class="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
      <div class="text-center lg:text-left">
        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
          Your All-in-One Neo Banking Experience
        </h1>
        <p class="text-lg md:text-xl mb-10 opacity-90 max-w-2xl">
          Send money, pay bills, save, and grow your wealth with AREFAN WALLET — built for Nigerians, by Nigerians.
        </p>
        <div class="flex flex-col sm:flex-row justify-start gap-5">
          <button class="btn-primary rounded-lg shadow-lg hover-scale">Get Started</button>
          <button class="btn-secondary rounded-lg hover-scale">Learn More</button>
        </div>
      </div>
      
      <!-- Hero Image -->
      <div class="flex justify-center lg:justify-end">
        <div class="relative">
          <div class="absolute -top-6 -left-6 w-32 h-32 bg-[#9e8ff9] rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
          <div class="absolute -bottom-6 -right-6 w-32 h-32 bg-[#A3E635] rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
          <div class="relative rounded-2xl bg-white bg-opacity-10 backdrop-blur-lg border border-white border-opacity-20 p-1">
            <img src="img/HERO.webp" alt="AREFAN WALLET App" class="rounded-xl w-full max-w-md"/>
          </div>
        </div>
      </div>
    </div>

    <!-- Floating Elements -->
    <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-[#A3E635] opacity-70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
      </svg>
    </div>
  </section>

  <!-- Download Section -->
  <section class="py-12 px-6 bg-gradient-to-r from-[#064E3B] to-[#0D7B5A]">
    <div class="max-w-6xl mx-auto">
      <div class="flex flex-col md:flex-row items-center justify-between gap-8">
        <div class="text-center md:text-left text-white flex-1">
          <h2 class="text-3xl md:text-4xl font-bold mb-4">Download AREFAN WALLET Today</h2>
          <p class="text-lg opacity-90 max-w-2xl">Join thousands of satisfied users who have transformed their banking experience</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-4 flex-1 justify-end">
          <button class="flex items-center bg-black bg-opacity-30 backdrop-blur-sm px-6 py-3 rounded-xl hover:bg-opacity-40 transition w-full sm:w-auto justify-center">
            <div class="mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="white">
                <path d="M17.062 4c.693 0 1.25.557 1.25 1.25v13.5c0 .693-.557 1.25-1.25 1.25H6.938c-.693 0-1.25-.557-1.25-1.25V5.25c0-.693.557-1.25 1.25-1.25h10.124zm-2.56 14.5H9.438v-1.25h5.064v1.25zm1.25-2.5H8.188v-7.5h7.574v7.5z"/>
              </svg>
            </div>
            <div class="text-left">
              <div class="text-xs opacity-80">Download on the</div>
              <div class="text-xl font-semibold">App Store</div>
            </div>
          </button>
          
          <button class="flex items-center bg-black bg-opacity-30 backdrop-blur-sm px-6 py-3 rounded-xl hover:bg-opacity-40 transition w-full sm:w-auto justify-center">
            <div class="mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="white">
                <path d="M3 21h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18V7H3v2zm0-6v2h18V3H3z"/>
              </svg>
            </div>
            <div class="text-left">
              <div class="text-xs opacity-80">GET IT ON</div>
              <div class="text-xl font-semibold">Google Play</div>
            </div>
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="py-20 px-6 md:px-12 bg-white">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16 fade-in">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Powerful Features</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">Everything you need to manage your money, all in one secure app.</p>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
        <!-- Feature 1 -->
        <div class="fade-in bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover-scale">
          <div class="w-14 h-14 bg-[#A3E635] bg-opacity-20 rounded-xl flex items-center justify-center mb-5">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-[#064E3B]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold mb-3 text-gray-800">Instant Transfers</h3>
          <p class="text-gray-600">Send money to any Nigerian bank in seconds, 24/7, with zero delays.</p>
        </div>

        <!-- Feature 2 -->
        <div class="fade-in bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover-scale">
          <div class="w-14 h-14 bg-[#A3E635] bg-opacity-20 rounded-xl flex items-center justify-center mb-5">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-[#064E3B]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold mb-3 text-gray-800">Bill Payments</h3>
          <p class="text-gray-600">Pay for airtime, data, electricity, cable TV, and more with one tap.</p>
        </div>

        <!-- Feature 3 -->
        <div class="fade-in bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover-scale">
          <div class="w-14 h-14 bg-[#A3E635] bg-opacity-20 rounded-xl flex items-center justify-center mb-5">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-[#064E3B]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold mb-3 text-gray-800">Virtual Cards</h3>
          <p class="text-gray-600">Create secure virtual cards for online shopping and subscriptions.</p>
        </div>

        <!-- Feature 4 -->
        <div class="fade-in bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover-scale">
          <div class="w-14 h-14 bg-[#A3E635] bg-opacity-20 rounded-xl flex items-center justify-center mb-5">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-[#064E3B]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold mb-3 text-gray-800">Savings Goals</h3>
          <p class="text-gray-600">Set financial goals and watch your savings grow automatically.</p>
        </div>
      </div>
      
      <!-- Additional Features Preview -->
      <div class="mt-20 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
        <div class="fade-in bg-gradient-to-br from-[#064E3B] to-[#095F49] p-8 rounded-2xl text-white">
          <div class="flex items-start">
            <img src="img/features.png" alt="Security Feature" class="w-16 h-16 rounded-lg object-cover mr-6"/>
            <div>
              <h3 class="text-xl font-semibold mb-2">Bank-Grade Security</h3>
              <p class="opacity-90">Military-grade encryption and multi-factor authentication to keep your funds safe.</p>
            </div>
          </div>
        </div>
        
        <div class="fade-in bg-gradient-to-br from-[#095F49] to-[#0D7B5A] p-8 rounded-2xl text-white">
          <div class="flex items-start">
            <img src="img/features-2.png" alt="Investment Feature" class="w-16 h-16 rounded-lg object-cover mr-6"/>
            <div>
              <h3 class="text-xl font-semibold mb-2">Smart Investments</h3>
              <p class="opacity-90">Grow your wealth with our carefully curated investment opportunities.</p>
            </div>
          </div>
        </div>
        
        <div class="fade-in bg-gradient-to-br from-[#0D7B5A] to-[#10926B] p-8 rounded-2xl text-white">
          <div class="flex items-start">
            <img src="img/features-3.png" alt="Rewards Feature" class="w-16 h-16 rounded-lg object-cover mr-6"/>
            <div>
              <h3 class="text-xl font-semibold mb-2">Rewarding Program</h3>
              <p class="opacity-90">Earn cashback and rewards for every transaction and milestone reached.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- How It Works Section -->
  <section id="how-it-works" class="py-20 px-6 md:px-12 relative">
    <div class="absolute inset-0 z-0">
      <img src="img/90733.jpg" alt="How It Works Background" class="w-full h-full object-cover"/>
      <div class="absolute inset-0 bg-gradient-to-b from-transparent to-[#064E3B] opacity-90"></div>
    </div>
    <div class="relative z-10 max-w-6xl mx-auto text-center text-white">
      <h2 class="text-3xl md:text-4xl font-bold mb-6 fade-in">How It Works</h2>
      <p class="text-lg opacity-90 max-w-2xl mx-auto mb-16 fade-in">Get started with AREFAN WALLET in just a few simple steps</p>
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-8">
        <!-- Step 1 -->
        <div class="fade-in bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-2xl border border-white border-opacity-20">
          <div class="w-16 h-16 bg-[#A3E635] text-[#064E3B] rounded-full flex items-center justify-center text-2xl font-bold mb-6 mx-auto">1</div>
          <h3 class="text-xl font-semibold mb-4 text-center">Download the App</h3>
          <p class="text-base opacity-90 text-center">Get AREFAN WALLET from the Google Play Store or Apple App Store.</p>
        </div>

        <!-- Step 2 -->
        <div class="fade-in bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-2xl border border-white border-opacity-20">
          <div class="w-16 h-16 bg-[#A3E635] text-[#064E3B] rounded-full flex items-center justify-center text-2xl font-bold mb-6 mx-auto">2</div>
          <h3 class="text-xl font-semibold mb-4 text-center">Create Your Account</h3>
          <p class="text-base opacity-90 text-center">Sign up in minutes with your phone number and valid ID.</p>
        </div>

        <!-- Step 3 -->
        <div class="fade-in bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-2xl border border-white border-opacity-20">
          <div class="w-16 h-16 bg-[#A3E635] text-[#064E3B] rounded-full flex items-center justify-center text-2xl font-bold mb-6 mx-auto">3</div>
          <h3 class="text-xl font-semibold mb-4 text-center">Start Banking Smarter</h3>
          <p class="text-base opacity-90 text-center">Fund your wallet and begin enjoying seamless banking.</p>
        </div>
      </div>
      
      <!-- App Download Badges -->
      <div class="mt-16 fade-in flex flex-col sm:flex-row justify-center items-center gap-6">
        <button class="flex items-center bg-black bg-opacity-30 backdrop-blur-sm px-6 py-3 rounded-xl hover:bg-opacity-40 transition">
          <div class="mr-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="white">
              <path d="M17.062 4c.693 0 1.25.557 1.25 1.25v13.5c0 .693-.557 1.25-1.25 1.25H6.938c-.693 0-1.25-.557-1.25-1.25V5.25c0-.693.557-1.25 1.25-1.25h10.124zm-2.56 14.5H9.438v-1.25h5.064v1.25zm1.25-2.5H8.188v-7.5h7.574v7.5z"/>
            </svg>
          </div>
          <div class="text-left">
            <div class="text-xs opacity-80">Download on the</div>
            <div class="text-xl font-semibold">App Store</div>
          </div>
        </button>
        
        <button class="flex items-center bg-black bg-opacity-30 backdrop-blur-sm px-6 py-3 rounded-xl hover:bg-opacity-40 transition">
          <div class="mr-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="white">
              <path d="M3 21h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18V7H3v2zm0-6v2h18V3H3z"/>
            </svg>
          </div>
          <div class="text-left">
            <div class="text-xs opacity-80">GET IT ON</div>
            <div class="text-xl font-semibold">Google Play</div>
          </div>
        </button>
      </div>
    </div>
  </section>

  <!-- App Showcase Section -->
  <section id="app-showcase" class="py-20 px-6 md:px-12 bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="max-w-6xl mx-auto">
      <div class="flex flex-col lg:flex-row items-center gap-12">
        <!-- App Mockup -->
        <div class="flex-1 flex justify-center fade-in relative">
          <div class="absolute -top-6 -left-6 w-32 h-32 bg-[#9e8ff9] rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
          <div class="absolute -bottom-6 -right-6 w-32 h-32 bg-[#A3E635] rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
          <div class="relative">
            <img src="img/Jla3P43tx11.png" alt="AREFAN WALLET App Mockup" class="max-w-full h-auto shadow-2xl rounded-3xl border-8 border-white"/>
          </div>
        </div>

        <!-- Benefits List -->
        <div class="flex-1">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-8 fade-in">Banking Made Simple</h2>
          <ul class="space-y-6">
            <li class="flex items-start fade-in bg-white p-5 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition">
              <div class="w-6 h-6 bg-[#A3E635] rounded-full mt-1 mr-4 flex-shrink-0 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#064E3B]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Zero Hidden Fees</h3>
                <p class="text-gray-600">Transparent pricing with no surprise charges.</p>
              </div>
            </li>
            <li class="flex items-start fade-in bg-white p-5 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition">
              <div class="w-6 h-6 bg-[#A3E635] rounded-full mt-1 mr-4 flex-shrink-0 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#064E3B]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">24/7 Customer Support</h3>
                <p class="text-gray-600">Our team is always ready to assist you.</p>
              </div>
            </li>
            <li class="flex items-start fade-in bg-white p-5 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition">
              <div class="w-6 h-6 bg-[#A3E635] rounded-full mt-1 mr-4 flex-shrink-0 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#064E3B]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Bank-Level Security</h3>
                <p class="text-gray-600">Your money and data are protected with military-grade encryption.</p>
              </div>
            </li>
            <li class="flex items-start fade-in bg-white p-5 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition">
              <div class="w-6 h-6 bg-[#A3E635] rounded-full mt-1 mr-4 flex-shrink-0 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#064E3B]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Instant Notifications</h3>
                <p class="text-gray-600">Stay informed with real-time transaction alerts.</p>
              </div>
            </li>
          </ul>
          
          <!-- Stats -->
          <div class="mt-10 grid grid-cols-3 sm:grid-cols-3 gap-6 fade-in">
            <div class="text-center">
              <div class="text-3xl font-bold text-[#064E3B]">10K+</div>
              <div class="text-gray-600">Active Users</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-[#064E3B]">₦2B+</div>
              <div class="text-gray-600">Transacted</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-[#064E3B]">4.9</div>
              <div class="text-gray-600">App Rating</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section id="testimonials" class="py-20 px-6 md:px-12 bg-gradient-to-br from-white to-gray-100 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute top-0 left-0 w-full h-full opacity-5">
      <img src="img/icon_pattern.svg" class="w-full h-full object-cover" alt="Pattern"/>
    </div>
    
    <div class="max-w-6xl mx-auto relative z-10">
      <h2 class="text-3xl md:text-4xl font-bold text-center text-gray-800 mb-6 fade-in">What Our Users Say</h2>
      <p class="text-lg text-gray-600 text-center max-w-2xl mx-auto mb-16 fade-in">Join thousands of satisfied users who have transformed their banking experience</p>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Testimonial 1 -->
        <div class="fade-in bg-white p-8 rounded-2xl shadow-lg border border-gray-100 transform transition duration-500 hover:scale-105">
          <div class="flex items-center mb-6">
            <img src="img/user1.png" alt="User 1" class="w-14 h-14 rounded-full mr-4 object-cover border-2 border-[#A3E635]"/>
            <div>
              <h3 class="font-semibold text-gray-800">Chinedu Okonkwo</h3>
              <p class="text-sm text-gray-500">Lagos, Nigeria</p>
            </div>
          </div>
          <p class="text-gray-600 italic mb-4">"AREFAN WALLET has transformed how I manage my money. Sending money to family across states is now instant and free!"</p>
          <div class="mt-4 flex text-[#A3E635]">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          </div>
        </div>

        <!-- Testimonial 2 -->
        <div class="fade-in bg-white p-8 rounded-2xl shadow-lg border border-gray-100 transform transition duration-500 hover:scale-105">
          <div class="flex items-center mb-6">
            <img src="img/user2.png" alt="User 2" class="w-14 h-14 rounded-full mr-4 object-cover border-2 border-[#A3E635]"/>
            <div>
              <h3 class="font-semibold text-gray-800">Amina Bello</h3>
              <p class="text-sm text-gray-500">Abuja, Nigeria</p>
            </div>
          </div>
          <p class="text-gray-600 italic mb-4">"I love the savings goals feature! I've saved over ₦200,000 for my business startup in just 6 months."</p>
          <div class="mt-4 flex text-[#A3E635]">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          </div>
        </div>

        <!-- Testimonial 3 -->
        <div class="fade-in bg-white p-8 rounded-2xl shadow-lg border border-gray-100 transform transition duration-500 hover:scale-105">
          <div class="flex items-center mb-6">
            <img src="img/user3.png" alt="User 3" class="w-14 h-14 rounded-full mr-4 object-cover border-2 border-[#A3E635]"/>
            <div>
              <h3 class="font-semibold text-gray-800">Tunde Adeyemi</h3>
              <p class="text-sm text-gray-500">Ibadan, Nigeria</p>
            </div>
          </div>
          <p class="text-gray-600 italic mb-4">"The virtual card feature saved me when my physical card was stolen. I created a new virtual card in seconds!"</p>
          <div class="mt-4 flex text-[#A3E635]">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          </div>
        </div>
      </div>
      
      <!-- Trust Badges -->
      <div class="mt-16 flex flex-wrap justify-center gap-8 fade-in">
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#A3E635] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          <span class="font-semibold">Licensed by CBN</span>
        </div>
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#A3E635] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <span class="font-semibold">256-bit Encryption</span>
        </div>
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#A3E635] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span class="font-semibold">PCI DSS Compliant</span>
        </div>
      </div>
    </div>
  </section>

  <!-- Pricing Section -->
  <section id="pricing" class="py-20 px-6 md:px-12 bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16 fade-in">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Simple, Transparent Pricing</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">Choose the plan that works best for you. All plans include our core banking features.</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Free Plan -->
        <div class="fade-in bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover-scale">
          <div class="flex justify-center mb-4">
            <img src="img/pricing-free.png" alt="Free Plan" class="h-16 w-16 object-contain">
          </div>
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-semibold text-gray-800">Free</h3>
            <div class="bg-[#064E3B] text-white text-xs font-bold px-3 py-1 rounded-full">Most Popular</div>
          </div>
          <div class="mb-6">
            <span class="text-4xl font-bold text-gray-800">₦0</span>
            <span class="text-gray-600">/month</span>
          </div>
          <ul class="space-y-4 mb-8">
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>Up to 5 transfers/month</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>Basic bill payments</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>₦50,000 monthly limit</span>
            </li>
            <li class="flex items-center opacity-50">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
              <span>Virtual cards</span>
            </li>
          </ul>
          <button class="btn-secondary w-full">Get Started</button>
        </div>

        <!-- Starter Plan -->
        <div class="fade-in bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover-scale">
          <div class="flex justify-center mb-4">
            <img src="img/pricing-starter.png" alt="Starter Plan" class="h-16 w-16 object-contain">
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-6">Starter</h3>
          <div class="mb-6">
            <span class="text-4xl font-bold text-gray-800">₦500</span>
            <span class="text-gray-600">/month</span>
          </div>
          <ul class="space-y-4 mb-8">
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>Unlimited transfers</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>All bill payments</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>₦500,000 monthly limit</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>1 virtual card</span>
            </li>
          </ul>
          <button class="btn-primary w-full">Get Started</button>
        </div>

        <!-- Business Plan -->
        <div class="fade-in bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover-scale">
          <div class="flex justify-center mb-4">
            <img src="img/pricing-business.png" alt="Business Plan" class="h-16 w-16 object-contain">
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-6">Business</h3>
          <div class="mb-6">
            <span class="text-4xl font-bold text-gray-800">₦2,500</span>
            <span class="text-gray-600">/month</span>
          </div>
          <ul class="space-y-4 mb-8">
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>Unlimited transfers</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>All bill payments</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>₦2,000,000 monthly limit</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>5 virtual cards</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>Business analytics</span>
            </li>
          </ul>
          <button class="btn-primary w-full">Get Started</button>
        </div>

        <!-- Ultimate Plan -->
        <div class="fade-in bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover-scale">
          <div class="flex justify-center mb-4">
            <img src="img/pricing-ultimate.png" alt="Ultimate Plan" class="h-16 w-16 object-contain">
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-6">Ultimate</h3>
          <div class="mb-6">
            <span class="text-4xl font-bold text-gray-800">₦5,000</span>
            <span class="text-gray-600">/month</span>
          </div>
          <ul class="space-y-4 mb-8">
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>Unlimited transfers</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>All bill payments</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>Unlimited monthly limit</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>Unlimited virtual cards</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>Advanced analytics</span>
            </li>
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#A3E635] mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span>Priority support</span>
            </li>
          </ul>
          <button class="btn-primary w-full">Get Started</button>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer Section -->
  <footer id="footer" class="py-12 px-6 md:px-12 bg-[#064E3B] text-white">
    <div class="max-w-6xl mx-auto">
      <div class="flex flex-col md:flex-row justify-between items-center md:items-start gap-8">
        <!-- Logo and Description -->
        <div class="text-center md:text-left">
          <img src="img/logo.png" alt="AREFAN WALLET Logo" class="h-12 mb-4 mx-auto md:mx-0"/>
          <p class="text-gray-300 max-w-xs">Your trusted financial partner for seamless banking in Nigeria.</p>
        </div>

        <!-- Quick Links -->
        <div class="grid grid-cols-2 sm:grid-cols-4 gap-8 text-center md:text-left">
          <div>
            <h4 class="font-semibold text-lg mb-4">Company</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">About</a></li>
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">Careers</a></li>
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">Blog</a></li>
            </ul>
          </div>
          <div>
            <h4 class="font-semibold text-lg mb-4">Product</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">Features</a></li>
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">Pricing</a></li>
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">Security</a></li>
            </ul>
          </div>
          <div>
            <h4 class="font-semibold text-lg mb-4">Support</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">Help Center</a></li>
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">Contact</a></li>
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">Status</a></li>
            </ul>
          </div>
          <div>
            <h4 class="font-semibold text-lg mb-4">Legal</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">Privacy Policy</a></li>
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">Terms of Service</a></li>
              <li><a href="#" class="text-gray-300 hover:text-[#A3E635] transition">Cookies</a></li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="border-t border-gray-700 mt-10 pt-8 flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-400 text-sm">© 2023 AREFAN WALLET. All rights reserved.</p>
        <div class="flex space-x-6 mt-4 md:mt-0">
          <a href="#" class="text-gray-400 hover:text-[#A3E635] transition">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
            </svg>
          </a>
          <a href="#" class="text-gray-400 hover:text-[#A3E635] transition">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
            </svg>
          </a>
          <a href="#" class="text-gray-400 hover:text-[#A3E635] transition">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm3 8h-1.35c-.538 0-.65.221-.65.778v1.222h2l-.209 2h-1.791v7h-3v-7h-2v-2h2v-2.308c0-1.769.931-2.692 3.029-2.692h1.971v3z"/>
            </svg>
          </a>
          <a href="#" class="text-gray-400 hover:text-[#A3E635] transition">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.146-3.233 1.658-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript for Scroll Animation and Mobile Menu -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const fadeElements = document.querySelectorAll('.fade-in');
      
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
          }
        });
      }, {
        threshold: 0.1
      });

      fadeElements.forEach(el => {
        observer.observe(el);
      });

      // Add visible class to elements already in view on load
      setTimeout(() => {
        fadeElements.forEach(el => {
          if (isElementInViewport(el)) {
            el.classList.add('visible');
          }
        });
      }, 100);

      function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
          rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
          rect.bottom >= 0
        );
      }
      
      // Mobile menu toggle
      const mobileMenuButton = document.getElementById('mobile-menu-button');
      const mobileMenu = document.getElementById('mobile-menu');
      
      mobileMenuButton.addEventListener('click', function() {
        mobileMenu.classList.toggle('open');
      });
      
      // Close mobile menu when clicking on a link
      const mobileLinks = mobileMenu.querySelectorAll('a');
      mobileLinks.forEach(link => {
        link.addEventListener('click', function() {
          mobileMenu.classList.remove('open');
        });
      });
      
      // Navbar scroll effect
      const navbar = document.querySelector('.navbar');
      const navLinks = document.querySelectorAll('.navbar a');
      const logoText = document.querySelector('.navbar .text-xl.font-bold');
      
      window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
          navbar.classList.add('scrolled');
          // Change text color to white when scrolled
          navLinks.forEach(link => {
            link.classList.remove('text-gray-800');
            link.classList.add('text-white');
          });
          if (logoText) {
            logoText.classList.remove('text-gray-800');
            logoText.classList.add('text-white');
          }
        } else {
          navbar.classList.remove('scrolled');
          // Reset text color when at top
          navLinks.forEach(link => {
            link.classList.remove('text-white');
            link.classList.add('text-gray-800');
          });
          if (logoText) {
            logoText.classList.remove('text-white');
            logoText.classList.add('text-gray-800');
          }
        }
      });
    });
  </script>
</body>
</html>