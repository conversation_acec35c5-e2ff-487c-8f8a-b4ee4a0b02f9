{"__meta": {"id": "X7a0f581c0db1543259e1b3743cfdb59b", "datetime": "2025-08-12 22:13:45", "utime": 1755015225.63206, "method": "GET", "uri": "/admin/auth/code/captcha/1", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755015225.123856, "end": 1755015225.632078, "duration": 0.5082218647003174, "duration_str": "508ms", "measures": [{"label": "Booting", "start": 1755015225.123856, "relative_start": 0, "end": 1755015225.447554, "relative_end": 1755015225.447554, "duration": 0.3236980438232422, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1755015225.447577, "relative_start": 0.32372093200683594, "end": 1755015225.632081, "relative_end": 3.0994415283203125e-06, "duration": 0.18450403213500977, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23595024, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/auth/code/captcha/{tmp}", "middleware": "web, guest:user", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@captcha", "as": "admin.auth.default-captcha", "namespace": "App\\Http\\Controllers\\Admin\\Auth", "prefix": "admin/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=28\" onclick=\"\">app/Http/Controllers/Admin/Auth/LoginController.php:28-48</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RUXMC3HJeJD4B2tk4KRSmLGszkvjIPFeK6Yu43wH", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/auth/code/captcha/1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "default_captcha_code": "FJLk", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/auth/code/captcha/1", "status_code": "<pre class=sf-dump id=sf-dump-408811003 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-408811003\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1028887405 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1028887405\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-353963624 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-353963624\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-930189298 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlFkUWF6dHNuZ0ZlYmg0em9INXpSeEE9PSIsInZhbHVlIjoiYWhwWS95Z01xSE1FMDkxTmJTUXRsUkhPK0FtaVYrbXhrNXF2SFdUSzE4LzBlNUNtNWFRcGZKdlNlSFpBcWkwcGk2ZmFuSWhUTmRIZjI0Znd1d3UwckZScGtlcGFET1REOHlZZ3VOUHJMRHM0Wmd0WmxibFZLdURROG5GbWphcVIiLCJtYWMiOiIxZWE1MDYzYWE0MzE4MTkxOGE0NWNjYjI4YjViODgyOGZmMGUwN2RlNGJmMGNiMWQzYjVkZDNkZDE3NTU0N2ZkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjFzRWJZZmdRL1lFZk11WjRISElqaVE9PSIsInZhbHVlIjoiOE5rZ2FnUmIrMitLM0FzL3Z2N2JJbmRQZFpRVTF4N3V5c2tqeWY5UjI0ekthV0RaQU1FS2t0TG9tTGZuWWo4THIrdEFyRnpLM09sMFFyU3cwN0luVmVvUng5bDBqdkdmMmpub0dEUEROS3hZVjdQWDI5cGZGNnIrV0tqYVpHZTciLCJtYWMiOiI2Zjc4MmMzM2QxYjIwZjFlN2M5MjkxMzdjNTNmYzJhZmYxMDQ0ZmQ2YmJiZTUzNWEzNGE5OGEzMzViYjI2NjZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930189298\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1245952431 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RUXMC3HJeJD4B2tk4KRSmLGszkvjIPFeK6Yu43wH</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HqiWVjrBz304HgMjNZDWHCTOz0FTrCO0fCXdPmIA</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245952431\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1729099351 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 16:13:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InNUREp5M00zVjg2NjFXMWMySGdlcmc9PSIsInZhbHVlIjoiVEVON2VtM0NTMUtjMlh3bFgxdFI3c2RXWlR5Y3lBL3crcEw4VEVCU2dwSzBaL1ZtT1JqaHNVTklaeHNXYTNZTnJvdWNLc3ZQaysxQnIrQmdLcnN2alVwWWp1VUJxc2hRclJRLy9wRlZ4VytUZHcvNlpnTFZvR1N2aUNIQ21uV2UiLCJtYWMiOiIwYzEzNzQyZmJmMDNmM2IwMjFiYTYyNDI1Nzc2MzM4YzRiNTNhN2M3MTYyNDA2N2VlNDZkYjIzMWI5MDliYWU5IiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 18:13:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InZFaXJNWGJ4TmR5RnNqSFBVd0RNdXc9PSIsInZhbHVlIjoiYnBzS0EyMkRYcXZDQWprQmlQR3R5YUhCbzVoUGM4c2IzbG45MnkxWlY5SWp0WEVSOEUyOGlKeEQvNE9RbEhBdlVZMlBlVS9NZGtYaElEUjgzSzlha1JNcXNpTjVQcG1vSVRuUEZOTnZ4aVdWZkhiSnhya1FTa29xRFhYZG1lcnkiLCJtYWMiOiI1OWFkODdjZWJkOTdlN2M1ZWM1ZTkzMzZjZjE1MjI4YTY2ZTVlZTNlMmQyMGU1OGEyM2ZjYzY0MTg3ODg4OTk0IiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 18:13:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InNUREp5M00zVjg2NjFXMWMySGdlcmc9PSIsInZhbHVlIjoiVEVON2VtM0NTMUtjMlh3bFgxdFI3c2RXWlR5Y3lBL3crcEw4VEVCU2dwSzBaL1ZtT1JqaHNVTklaeHNXYTNZTnJvdWNLc3ZQaysxQnIrQmdLcnN2alVwWWp1VUJxc2hRclJRLy9wRlZ4VytUZHcvNlpnTFZvR1N2aUNIQ21uV2UiLCJtYWMiOiIwYzEzNzQyZmJmMDNmM2IwMjFiYTYyNDI1Nzc2MzM4YzRiNTNhN2M3MTYyNDA2N2VlNDZkYjIzMWI5MDliYWU5IiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 18:13:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InZFaXJNWGJ4TmR5RnNqSFBVd0RNdXc9PSIsInZhbHVlIjoiYnBzS0EyMkRYcXZDQWprQmlQR3R5YUhCbzVoUGM4c2IzbG45MnkxWlY5SWp0WEVSOEUyOGlKeEQvNE9RbEhBdlVZMlBlVS9NZGtYaElEUjgzSzlha1JNcXNpTjVQcG1vSVRuUEZOTnZ4aVdWZkhiSnhya1FTa29xRFhYZG1lcnkiLCJtYWMiOiI1OWFkODdjZWJkOTdlN2M1ZWM1ZTkzMzZjZjE1MjI4YTY2ZTVlZTNlMmQyMGU1OGEyM2ZjYzY0MTg3ODg4OTk0IiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 18:13:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729099351\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-275786655 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RUXMC3HJeJD4B2tk4KRSmLGszkvjIPFeK6Yu43wH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/admin/auth/code/captcha/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>default_captcha_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FJLk</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275786655\", {\"maxDepth\":0})</script>\n"}}