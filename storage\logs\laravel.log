[2025-07-07 08:25:10] local.ERROR: scandir(Modules): The system cannot find the file specifi (code: 2) {"exception":"[object] (ErrorException(code: 0): scandir(Modules): The system cannot find the file specifi (code: 2) at C:\\laragon\\www\\arefan_wallet_admin\\app\\Traits\\AddonHelper.php:85)
[stacktrace]
#0 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'scandir(Modules...', 'C:\\\\laragon\\\\www\\\\...', 85)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'scandir(Modules...', 'C:\\\\laragon\\\\www\\\\...', 85)
#2 C:\\laragon\\www\\arefan_wallet_admin\\app\\Traits\\AddonHelper.php(85): scandir('Modules')
#3 C:\\laragon\\www\\arefan_wallet_admin\\app\\Traits\\AddonHelper.php(35): App\\Providers\\AppServiceProvider->getDirectories('Modules')
#4 C:\\laragon\\www\\arefan_wallet_admin\\app\\Providers\\AppServiceProvider.php(34): App\\Providers\\AppServiceProvider->get_addon_admin_routes()
#5 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#6 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#11 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#12 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 34)
#13 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(909): array_walk(Array, Object(Closure))
#14 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#15 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#16 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#17 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#18 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\arefan_wallet_admin\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\arefan_wallet_admin\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#21 {main}
"} 
[2025-07-07 08:25:31] local.ERROR: scandir(Modules): The system cannot find the file specifi (code: 2) {"exception":"[object] (ErrorException(code: 0): scandir(Modules): The system cannot find the file specifi (code: 2) at C:\\laragon\\www\\arefan_wallet_admin\\app\\Traits\\AddonHelper.php:85)
[stacktrace]
#0 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'scandir(Modules...', 'C:\\\\laragon\\\\www\\\\...', 85)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'scandir(Modules...', 'C:\\\\laragon\\\\www\\\\...', 85)
#2 C:\\laragon\\www\\arefan_wallet_admin\\app\\Traits\\AddonHelper.php(85): scandir('Modules')
#3 C:\\laragon\\www\\arefan_wallet_admin\\app\\Traits\\AddonHelper.php(35): App\\Providers\\AppServiceProvider->getDirectories('Modules')
#4 C:\\laragon\\www\\arefan_wallet_admin\\app\\Providers\\AppServiceProvider.php(34): App\\Providers\\AppServiceProvider->get_addon_admin_routes()
#5 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#6 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(929): Illuminate\\Container\\Container->call(Array)
#11 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(910): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#12 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 34)
#13 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(909): array_walk(Array, Object(Closure))
#14 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#15 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#16 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#17 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#18 C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\arefan_wallet_admin\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\arefan_wallet_admin\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#21 {main}
"} 
