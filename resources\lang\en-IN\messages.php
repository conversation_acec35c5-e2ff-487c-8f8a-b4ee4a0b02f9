<?php return array (
  'software_version' => 'Software version',
  'signin' => 'Signin',
  'admin_or_employee_signin' => 'Admin or employee signin',
  'want' => 'Want',
  'to' => 'To',
  'login' => 'Login',
  'your' => 'Your',
  'vendors' => 'Vendors',
  'vendor' => 'Vendor',
  'email' => 'Email',
  'password' => 'Password',
  'remember' => 'Remember',
  'me' => 'Me',
  'sign_in' => 'Sign in',
  'remember me' => 'Remember me',
  'your phone' => 'Your phone',
  'settings' => 'Settings',
  'sign_out' => 'Sign out',
  'dashboard' => 'Dashboard',
  'restaurant' => 'Restaurant',
  'section' => 'Section',
  'management' => 'Management',
  'business' => 'Business',
  'setup' => 'Setup',
  'profile' => 'Profile',
  'card_holder_name' => 'Card holder name',
  'card_holder_email' => 'Card holder email',
  'card_number' => 'Card number',
  'card_expire_month' => 'Card expire month',
  'card_expire_year' => 'Card expire year',
  'security_code' => 'Security code',
  'dues' => 'Dues',
  'document_type' => 'Document type',
  'document_number' => 'Document number',
  'issuing_bank' => 'Issuing bank',
  'amount_to_be_paid' => 'Amount to be paid',
  'country_permission_denied_or_misconfiguration' => 'Country permission denied or misconfiguration',
  'sms' => 'Sms',
  'module' => 'Module',
  'payment' => 'Payment',
  'methods' => 'Methods',
  'gateway' => 'Gateway',
  'twilio_sms' => 'Twilio sms',
  'active' => 'Active',
  'inactive' => 'Inactive',
  'sid' => 'Sid',
  'messaging_service_sid' => 'Messaging service sid',
  'token' => 'Token',
  'from' => 'From',
  'otp_template' => 'Otp template',
  'save' => 'Save',
  'nexmo_sms' => 'Nexmo sms',
  'api_key' => 'Api key',
  'api_secret' => 'Api secret',
  '2factor_sms' => '2factor sms',
  'msg91_sms' => 'Msg91 sms',
  'template_id' => 'Template id',
  'authkey' => 'Authkey',
  'method' => 'Method',
  'configure' => 'Configure',
  'sslcommerz' => 'Sslcommerz',
  'store' => 'Store',
  'id' => 'Id',
  'razorpay' => 'Razorpay',
  'razorkey' => 'Razorkey',
  'razorsecret' => 'Razorsecret',
  'paypal' => 'Paypal',
  'client' => 'Client',
  'secret' => 'Secret',
  'stripe' => 'Stripe',
  'published' => 'Published',
  'key' => 'Key',
  'api' => 'Api',
  'paystack' => 'Paystack',
  'senang' => 'Senang',
  'pay' => 'Pay',
  'wallet' => 'Wallet',
  'point' => 'Point',
  'bkash' => 'Bkash',
  'username' => 'Username',
  'paymob' => 'Paymob',
  'iframe_id' => 'Iframe id',
  'integration_id' => 'Integration id',
  'hmac' => 'Hmac',
  'flutterwave' => 'Flutterwave',
  'public_key' => 'Public key',
  'secret_key' => 'Secret key',
  'hash' => 'Hash',
  'mercadopago' => 'Mercadopago',
  'access_token' => 'Access token',
  'merchant' => 'Merchant',
  'publicKey' => 'PublicKey',
  'secretKey' => 'SecretKey',
  'paymentUrl' => 'PaymentUrl',
  'merchantEmail' => 'MerchantEmail',
  'Business' => 'Business',
  'maintenance_mode' => 'Maintenance mode',
  'name' => 'Name',
  'currency' => 'Currency',
  'currency_symbol_positon' => 'Currency symbol positon',
  'country' => 'Country',
  'time' => 'Time',
  'zone' => 'Zone',
  'scheduled' => 'Scheduled',
  'orders' => 'Orders',
  'customer_varification_toggle' => 'Customer varification toggle',
  'on' => 'On',
  'off' => 'Off',
  'order_confirmation_model' => 'Order confirmation model',
  'order_confirmation_model_hint' => 'Order confirmation model hint',
  'deliveryman' => 'Deliveryman',
  'admin' => 'Admin',
  'order' => 'Order',
  'notification' => 'Notification',
  'customer' => 'Customer',
  'verification' => 'Verification',
  'delivery' => 'Delivery',
  'order_varification_toggle' => 'Order varification toggle',
  'default_admin_commission' => 'Default admin commission',
  'free_delivery_over' => 'Free delivery over',
  'minimum_shipping_charge' => 'Minimum shipping charge',
  'per_km_shipping_charge' => 'Per km shipping charge',
  'dm_maximum_order' => 'Dm maximum order',
  'dm_maximum_order_hint' => 'Dm maximum order hint',
  'phone' => 'Phone',
  'footer' => 'Footer',
  'text' => 'Text',
  'logo' => 'Logo',
  'ratio' => 'Ratio',
  'choose' => 'Choose',
  'file' => 'File',
  'address' => 'Address',
  'latitude' => 'Latitude',
  'click_on_the_map_select_your_defaul_location' => 'Click on the map select your defaul location',
  'longitude' => 'Longitude',
  'successfully_updated_to_changes_restart_user_app' => 'Successfully updated to changes restart user app',
  'successfully_updated' => 'Successfully updated',
  'to_changes_restart_user_app' => 'To changes restart user app',
  'successfully_updated_to_changes_restart_app' => 'Successfully updated to changes restart app',
  'successfully_updated_to_changes_restart_the_app' => 'Successfully updated to changes restart the app',
  'push' => 'Push',
  'firebase' => 'Firebase',
  'server' => 'Server',
  'submit' => 'Submit',
  'messages' => 'Messages',
  'message' => 'Message',
  'settings_updated' => 'Settings updated',
  'welcome' => 'Welcome',
  'message_updated' => 'Message updated',
  'terms_and_condition' => 'Terms and condition',
  'privacy_policy' => 'Privacy policy',
  'about_us' => 'About us',
  'About us updated!' => 'About us updated!',
  'Privacy policy updated!' => 'Privacy policy updated!',
  'Terms and Conditions updated!' => 'Terms and Conditions updated!',
  'Linked Website' => 'Linked Website',
  'add' => 'Add',
  'new' => 'New',
  'branch' => 'Branch',
  'coverage' => 'Coverage',
  'km' => 'Km',
  '#' => '#',
  'action' => 'Action',
  'Add New Website' => 'Add New Website',
  'add new website' => 'Add new website',
  'link' => 'Link',
  'example' => 'Example',
  'www.example.com' => 'Www.example.com',
  '""_www.example.com' => 'www.example.com',
  'url' => 'Url',
  'Linked Website Table' => 'Linked Website Table',
  'image' => 'Image',
  'URL' => 'URL',
  'Status' => 'Status',
  'edit' => 'Edit',
  'Added Successfully!' => 'Added Successfully!',
  'delete' => 'Delete',
  'Updated Successfully!' => 'Updated Successfully!',
  'Website removed!' => 'Website removed!',
  'send' => 'Send',
  'title' => 'Title',
  'description' => 'Description',
  'status' => 'Status',
  'disabled' => 'Disabled',
  'update' => 'Update',
  'No Image' => 'No Image',
  'list' => 'List',
  'customers' => 'Customers',
  'export' => 'Export',
  'options' => 'Options',
  'copy' => 'Copy',
  'print' => 'Print',
  'download' => 'Download',
  'excel' => 'Excel',
  'csv' => 'Csv',
  'pdf' => 'Pdf',
  'columns' => 'Columns',
  'total' => 'Total',
  'actions' => 'Actions',
  'available' => 'Available',
  'points' => 'Points',
  'view' => 'View',
  'QR' => 'QR',
  'details' => 'Details',
  'joined_at' => 'Joined at',
  'current' => 'Current',
  'contact' => 'Contact',
  'info' => 'Info',
  'addresses' => 'Addresses',
  'Customer Details' => 'Customer Details',
  'wCustomer Details' => 'WCustomer Details',
  'Customer List' => 'Customer List',
  'search' => 'Search',
  'Admin' => 'Admin',
  'Login' => 'Login',
  'Email' => 'Email',
  '<EMAIL>' => '<EMAIL>',
  'Password' => 'Password',
  12345678 => '12345678',
  '+8801*********' => '+8801*********',
  '8+ characters required' => '8+ characters required',
  'Please enter a valid email address' => 'Please enter a valid email address',
  'Please enter a valid email address.' => 'Please enter a valid email address.',
  'Your password is invalid. Please try again.' => 'Your password is invalid. Please try again.',
  'Settings' => 'Settings',
  'Business Setup' => 'Business Setup',
  'New Business' => 'New Business',
  'Select Country' => 'Select Country',
  'Sorry! You can not enable maintainance mode in demo!' => 'Sorry! You can not enable maintainance mode in demo!',
  'Sorry! You can not enable maintenance mode in demo!' => 'Sorry! You can not enable maintenance mode in demo!',
  'Are you sure?' => 'Are you sure ',
  'Be careful before you turn on/off maintenance mode' => 'Be careful before you turn on/off maintenance mode',
  'FCM Settings' => 'FCM Settings',
  'Firebase Push Notification Setup' => 'Firebase Push Notification Setup',
  'ratio 1:1' => 'Ratio 1:1',
  'SMS Module Setup' => 'SMS Module Setup',
  '#OTP# will be replace with otp' => '#OTP# will be replace with otp',
  'NB : #OTP# will be replace with otp' => 'NB : #OTP# will be replace with otp',
  'business-settings.business-setup' => 'Business-settings.business-setup',
  'business-settings' => 'Business-settings',
  'Front Builder' => 'Front Builder',
  'Customize your overview page layout. Choose the one that best fits your needs.' => 'Customize your overview page layout. Choose the one that best fits your needs.',
  'Layout skins' => 'Layout skins',
  'Disabled' => 'Disabled',
  '3 kinds of layout skins to choose from.' => '3 kinds of layout skins to choose from.',
  'Default' => 'Default',
  'Light' => 'Light',
  'Sidebar layout options' => 'Sidebar layout options',
  'Choose between standard navigation sizing, mini or even compact with icons.' => 'Choose between standard navigation sizing  mini or even compact with icons.',
  'Compact' => 'Compact',
  'Mini' => 'Mini',
  'Header layout options' => 'Header layout options',
  'Choose the primary navigation of your header layout.' => 'Choose the primary navigation of your header layout.',
  'Default (Fluid)' => 'Default (Fluid)',
  'Default (Container)' => 'Default (Container)',
  'Double line (Fluid)' => 'Double line (Fluid)',
  'Double line (Container)' => 'Double line (Container)',
  'Reset' => 'Reset',
  'Preview' => 'Preview',
  'Do you want to logout?' => 'Do you want to logout ',
  'EX of SMS provider\'s template : your OTP is XXXX here, please check.' => 'EX of SMS provider s template : your OTP is XXXX here  please check.',
  'NB : XXXX will be replace with otp' => 'NB : XXXX will be replace with otp',
  'NB : Keep an OTP variable in your SMS providers OTP Template.' => 'NB : Keep an OTP variable in your SMS providers OTP Template.',
  'languages' => 'Languages',
  'Language' => 'Language',
  'Dashboard' => 'Dashboard',
  'language_setting' => 'Language setting',
  'changing_some_settings_will_take_time_to_show_effect_please_clear_session_or_wait_for_60_minutes_else_browse_from_incognito_mode' => 'Changing some settings will take time to show effect please clear session or wait for 60 minutes else browse from incognito mode',
  'language_table' => 'Language table',
  'add_new_language' => 'Add new language',
  'SL#' => 'SL#',
  'Id' => 'Id',
  'Code' => 'Code',
  'default' => 'Default',
  'new_language' => 'New language',
  'language' => 'Language',
  'country_code' => 'Country code',
  'direction' => 'Direction',
  'close' => 'Close',
  'Add' => 'Add',
  'Are you sure to delete this' => 'Are you sure to delete this',
  'You will not be able to revert this' => 'You will not be able to revert this',
  'Yes, delete it' => 'Yes  delete it',
  'Translate' => 'Translate',
  'Delete' => 'Delete',
  'Language Translate' => 'Language Translate',
  'language_content_table' => 'Language content table',
  'back' => 'Back',
  'value' => 'Value',
  'text_updated_successfully' => 'Text updated successfully',
  'Key removed successfully' => 'Key removed successfully',
  'Add New Notification' => 'Add New Notification',
  'New Notification' => 'New Notification',
  'Notification Table' => 'Notification Table',
  'agent section' => 'Agent section',
  'register' => 'Register',
  'first' => 'First',
  'last' => 'Last',
  'all' => 'All',
  'identity' => 'Identity',
  'type' => 'Type',
  'passport' => 'Passport',
  'driving' => 'Driving',
  'license' => 'License',
  'nid' => 'Nid',
  'number' => 'Number',
  'add new agent' => 'Add new agent',
  'Add New Agent' => 'Add New Agent',
  'First Name' => 'First Name',
  'Last Name' => 'Last Name',
  'optional' => 'Optional',
  'Gender' => 'Gender',
  'Select Gender' => 'Select Gender',
  'Male' => 'Male',
  'Female' => 'Female',
  'Other' => 'Other',
  'Occupation' => 'Occupation',
  'Agent Image' => 'Agent Image',
  'Agent Added Successfully!' => 'Agent Added Successfully!',
  'Ex : <EMAIL>' => 'Ex : <EMAIL>',
  'Ex : 017********' => 'Ex : 017********',
  'Ex : Businessman' => 'Ex : Businessman',
  'Ex : password (4digit)' => 'Ex : password (4digit)',
  'Agent' => 'Agent',
  'Agent Table' => 'Agent Table',
  'agent' => 'Agent',
  'Agent List' => 'Agent List',
  'Customer Table' => 'Customer Table',
  'Add New Customer' => 'Add New Customer',
  'Customer Image' => 'Customer Image',
  'Customer Added Successfully!' => 'Customer Added Successfully!',
  'Update Agent' => 'Update Agent',
  'Customer' => 'Customer',
  'transfer' => 'Transfer',
  'E-Money Transfer' => 'E-Money Transfer',
  'Transfer' => 'Transfer Money',
  'New Transfer' => 'New Transfer',
  'Sender' => 'Sender',
  'Select Sender' => 'Select Sender',
  'Receiver' => 'Receiver',
  'Select Receiver' => 'Select Receiver',
  'Amount' => 'Amount',
  'Ex : 9999' => 'Ex : 9999',
  'Receiver Type' => 'Receiver Type',
  'Select Receiver Type' => 'Select Receiver Type',
  'Select Type' => 'Select Type',
  'Choose' => 'Choose',
  'Select Type First' => 'Select Type First',
  'Transfer Table' => 'Transfer Table',
  'Unique ID' => 'Unique ID',
  'note' => 'Note',
  'Transfer Recorded Successfully!' => 'Transfer Recorded Successfully!',
  'Walk In Customer' => 'Walk In Customer',
  'Select_valid_receiver_type_first' => 'Select valid receiver type first',
  'Choose11111111' => 'Choose11111111',
  'EMoney' => 'EMoney',
  'pending' => 'Pending',
  'confirmed' => 'Confirmed',
  'processing' => 'Processing',
  'out_for_delivery' => 'Out for delivery',
  'Total Balance' => 'Total Balance',
  'Used Balance' => 'Used Balance',
  'Unused Balance' => 'Unused Balance',
  'Total Earned' => 'Total Earned',
  'Generate EMoney' => 'Generate EMoney',
  'Generate' => 'Generate',
  'Something went wrong!' => 'Something went wrong!',
  'EMoney generated successfully!' => 'EMoney generated successfully!',
  'cashout_charge_percent' => 'Cashout charge percent',
  'addmoney_charge_percent' => 'Addmoney charge percent',
  'sentmoney_charge_flat' => 'Sentmoney charge flat',
  'cashout_charge' => 'Cashout charge',
  'percent (%)' => 'Percent (%)',
  'in percent (%)' => 'In percent (%)',
  '""_percent (%)' => '   percent (%)',
  'addmoney_charge' => 'Addmoney charge',
  'sentmoney_charge' => 'Sentmoney charge',
  'flat' => 'Flat',
  'add_money_charge' => 'Add money charge',
  'sent_money_charge' => 'Sent money charge',
  'cash_out_charge' => 'Cash out charge',
  'send_money_charge' => 'Send money charge',
  'Transferred Successfully!' => 'Transferred Successfully!',
  'No#' => 'No#',
  'Receiver QR' => 'Receiver QR',
  'amount' => 'Amount',
  'Payment_view' => 'Payment view',
  'Payment' => 'Payment',
  'About us' => 'About us',
  'Purpose' => 'Purpose',
  'add purpose' => 'Add purpose',
  'any' => 'Any',
  'Title' => 'Title',
  'Color' => 'Color',
  'New Title' => 'New Title',
  'ratio 3:1 ' => 'Ratio 3:1 ',
  'choose file' => 'Choose file',
  'ratio 1:1 ' => 'Ratio 1:1 ',
  'color' => 'Color',
  'Hexa color code' => 'Hexa color code',
  'Successfully Added!' => 'Successfully Added!',
  'Image' => 'Image',
  'choose HEXA' => 'Choose HEXA',
  'choose_in_HEXA' => 'Choose in HEXA',
  'choose_in_HEXA_formate' => 'Choose in HEXA formate',
  'choose_in_HEXA_format' => 'Choose in HEXA format',
  'Action' => 'Action',
  'Edit' => 'Edit',
  'Logo' => 'Logo',
  'Successfully Updated!' => 'Successfully Updated!',
  'Add Title' => 'Add Title',
  'Successfully Deleted!' => 'Successfully Deleted!',
  'Edit Title' => 'Edit Title',
  'paymob_supports_EGP_currency' => 'Paymob supports EGP currency',
  'Payment Setup' => 'Payment Setup',
  'Terms and Conditions' => 'Terms and Conditions',
  'Privacy Policy' => 'Privacy Policy',
  'Forgot Password' => 'Forgot Password',
  'FORGOT PASSWORD' => 'FORGOT PASSWORD',
  'We have sent you this email in response to your request to reset your password. After you reset your password, you will be able to login with your new password.' => 'We have sent you this email in response to your request to reset your password. After you reset your password  you will be able to login with your new password.',
  'To reset your password, please use the token below' => 'To reset your password  please use the token below',
  'We recommend that you keep your password secure and not share it with anyone.If you feel your password has been compromised, you can change it by going to your app, My Account Page and clicking on the "Change Email Address or Password" link.' => 'We recommend that you keep your password secure and not share it with anyone.If you feel your password has been compromised  you can change it by going to your app  My Account Page and clicking on the  Change Email Address or Password  link.',
  'If you need help, or you have any other questions, feel free to email us.' => 'If you need help  or you have any other questions  feel free to email us.',
  'From Customer Service' => 'From Customer Service',
  'Please enter a valid phone number.' => 'Please enter a valid phone number.',
  'Enter Unername' => 'Enter Unername',
  'Enter your phone' => 'Enter your phone',
  'Enter your password' => 'Enter your password',
  'sign in' => 'Sign in',
  'Enter your phone No.' => 'Enter your phone No.',
  'Enter your phone no.' => 'Enter your phone no.',
  'welcome_message' => 'Welcome message',
  'dashboard_order_statistics' => 'Dashboard order statistics',
  'earnings' => 'Earnings',
  'EMoney Statistics' => 'EMoney Statistics',
  'top_agent' => 'Top agent',
  'Credit' => 'Credit',
  'Transaction statistics for business analytics' => 'Transaction statistics for business analytics',
  'Yearly Transaction' => 'Yearly Transaction',
  'transaction' => 'Transaction',
  'Transaction' => 'Transaction',
  'New transaction' => 'New transaction',
  'transaction Table' => 'Transaction Table',
  'Debit' => 'Debit',
  'Type' => 'Type',
  'Balance' => 'Balance',
  'send_money' => 'Send money',
  'received_money' => 'Received money',
  'admin_charge' => 'Admin charge',
  'cash_out' => 'Cash out',
  'cash_in' => 'Cash in',
  'Top Transactions' => 'Top Transactions',
  'Total Business Overview' => 'Total Business Overview',
  'Purpose Table' => 'Purpose Table',
  'ID' => 'ID',
  'Seller_Details' => 'Seller Details',
  'Back_to_seller_list' => 'Back to seller list',
  'Shop' => 'Shop',
  'Order' => 'Order',
  'Product' => 'Product',
  'Setting' => 'Setting',
  'Review' => 'Review',
  'seller_wallet' => 'Seller wallet',
  'Seller' => 'Seller',
  'Account' => 'Account',
  'Back_to_customer_list' => 'Back to customer list',
  'customer_Details' => 'Customer Details',
  'Personal Info' => 'Personal Info',
  'Phone' => 'Phone',
  'balance' => 'Balance',
  'customer wallet' => 'Customer wallet',
  'cash in transaction' => 'Cash in transaction',
  'Cash In Transaction' => 'Cash In Transaction',
  'Customer Transaction' => 'Customer Transaction',
  'customer_Transactions' => 'Customer Transactions',
  'Details' => 'Details',
  'Back_to_list' => 'Back to list',
  'Transactions' => 'Transactions',
  'Agent Commission' => 'Agent Commission',
  'two_factor' => 'Two factor',
  'payment settings updated!' => 'Payment settings updated!',
  'Failed!' => 'Failed!',
  'customer_transfer_message' => 'Customer transfer message',
  'Customer Transfer Message' => 'Customer Transfer Message',
  'EMoney Transfer Message' => 'EMoney Transfer Message',
  'Push notification failed for Customer!' => 'Push notification failed for Customer!',
  'User' => 'User',
  'user' => 'User',
  'account' => 'Account',
  'receiver' => 'Receiver',
  'Select' => 'Select',
  'All' => 'All',
  'Customers' => 'Customers',
  'Agents' => 'Agents',
  'Banner' => 'Banner',
  'banner' => 'Banner',
  'Add New Banner' => 'Add New Banner',
  'Banner Table' => 'Banner Table',
  'New Banner Title' => 'New Banner Title',
  'New banner title' => 'New banner title',
  'Update Banner' => 'Update Banner',
  'Top Agent' => 'Top Agent',
  'Top Customer' => 'Top Customer',
  'Top Agents' => 'Top Agents',
  'Top Customers' => 'Top Customers',
  'agents' => 'Agents',
  'faq' => 'Faq',
  'FAQ' => 'FAQ',
  'help_topic' => 'Help topic',
  'Table' => 'Table',
  'SL' => 'SL',
  'Question' => 'Question',
  'Answer' => 'Answer',
  'Ranking' => 'Ranking',
  'Add Help Topic' => 'Add Help Topic',
  'Type Question' => 'Type Question',
  'Type Answer' => 'Type Answer',
  'Active' => 'Active',
  'Close' => 'Close',
  'Save' => 'Save',
  'Edit Modal Help Topic' => 'Edit Modal Help Topic',
  'Are you sure delete this FAQ' => 'Are you sure delete this FAQ',
  'FAQ deleted successfully' => 'FAQ deleted successfully',
  'Refer Commission' => 'Refer Commission',
  'g-recaptcha-response google reCatpcha failed' => 'G-recaptcha-response google reCatpcha failed',
  'reCatpcha' => 'ReCatpcha',
  'Please check the recaptcha' => 'Please check the recaptcha',
  'Enter recatpcha value' => 'Enter recatpcha value',
  'recatpcha' => 'Recatpcha',
  'credentials' => 'Credentials',
  'reCatpcha Setup' => 'ReCatpcha Setup',
  'Site Key' => 'Site Key',
  'Secret Key' => 'Secret Key',
  'reCaptcha' => 'ReCaptcha',
  'Enter recaptcha value' => 'Enter recaptcha value',
  'reCaptcha Setup' => 'ReCaptcha Setup',
  'Two Factor Authentication' => 'Two Factor Authentication',
  'left' => 'Left',
  'right' => 'Right',
  'welcome_to_6cash' => 'Welcome to 6cash',
  'welcome_to_6cash_admin_panel' => 'Welcome to 6cash admin panel',
  'pagination' => 'Pagination',
  'Add Banner' => 'Add Banner',
  'Add Purpose' => 'Add Purpose',
  'transaction_list' => 'Transaction list',
  'transaction List' => 'Transaction List',
  'Amount must be greater than zero!' => 'Amount must be greater than zero!',
  'theme' => 'Theme',
  'Status Updated Successfully!' => 'Status Updated Successfully!',
  'Please fill reCAPTCHA' => 'Please fill reCAPTCHA',
  'Please Fill ReCAPTCHA' => 'Please Fill ReCAPTCHA',
  'Credentials SetUp' => 'Credentials SetUp',
  'reCaptcha credential Set up Instructions' => 'ReCaptcha credential Set up Instructions',
  'Go to the Credentials page' => 'Go to the Credentials page',
  'Click' => 'Click',
  'here' => 'Here',
  'Add a ' => 'Add a ',
  'label' => 'Label',
  '(Ex: Test Label)' => '(Ex: Test Label)',
  'Select reCAPTCHA v2 as ' => 'Select reCAPTCHA v2 as ',
  'reCAPTCHA Type' => 'ReCAPTCHA Type',
  'Sub type: I\'m not a robot Checkbox' => 'Sub type: I m not a robot Checkbox',
  'domain' => 'Domain',
  '(For ex: demo.6amtech.com)' => '(For ex: demo.6amtech.com)',
  'Check in ' => 'Check in ',
  'Accept the reCAPTCHA Terms of Service' => 'Accept the reCAPTCHA Terms of Service',
  'Press' => 'Press',
  'Submit' => 'Submit',
  'Copy' => 'Copy',
  'and' => 'And',
  'paste in the input filed below and' => 'Paste in the input filed below and',
  'cashIn Message' => 'CashIn Message',
  'cash In Message' => 'Cash In Message',
  'Cash In Message' => 'Cash In Message',
  'Cash Out Message' => 'Cash Out Message',
  'Send Money Message' => 'Send Money Message',
  'Request Money Message' => 'Request Money Message',
  'Deny Money Message' => 'Deny Money Message',
  'Approved Money Message' => 'Approved Money Message',
  'Denied Money Message' => 'Denied Money Message',
  'Add Money Message' => 'Add Money Message',
  'Received Money Message' => 'Received Money Message',
  'Transaction ID' => 'Transaction ID',
  'Transfer List' => 'Transfer List',
  'Search' => 'Search',
  '#NO' => '#NO',
  'Transaction Id' => 'Transaction Id',
  'Enter captcha value' => 'Enter captcha value',
  'Captcha Failed' => 'Captcha Failed',
  'welcome_to_the_admin_panel' => 'Welcome to the admin panel',
  'Software Version' => 'Software Version',
  'EX: 100' => 'EX: 100',
  'Too many login attempts. Banned for 1minute.' => 'Too many login attempts. Banned for 1minute.',
  'Error occurred while logging in.' => 'Error occurred while logging in.',
  'add_money' => 'Add money',
  'Request Money' => 'Request Money',
  'Note' => 'Note',
  'Customer unavailable' => 'Customer unavailable',
  'Request Money by Agents' => 'Request Money by Agents',
  'Agent Request Money' => 'Add Money Requests',
  'Requested time' => 'Requested time',
  'Requested Amount' => 'Requested Amount',
  'Pending' => 'Pending',
  'Accept' => 'Accept',
  'Accepted' => 'Accepted',
  'Agent request money' => 'Add Money Requests',
  'Agent Requested Transactions' => 'Agent\'s Requests for add money',
  'Deny' => 'Deny',
  'Successfully changed the status' => 'Successfully changed the status',
  'Status change failed' => 'Status change failed',
  'Denied' => 'Denied',
  'Ex : +88017********' => 'Ex : +88017********',
  'Must use country code' => 'Must use country code',
  '* Must use country code' => '* Must use country code',
  'Choose receiver' => 'Choose receiver',
  'Update receiver' => 'Update receiver',
  'disabl  ed' => 'Disabl  ed',
  'blocked' => 'Blocked',
  'User unavailable' => 'User unavailable',
  'ratio 3:1' => 'Ratio 3:1',
  'Update Customer' => 'Update Customer',
  'Approve' => 'Approve',
  'Approved' => 'Approved',
  'Email unavailable' => 'Email unavailable',
  'Total Earn from Charges' => 'Total Earn from Charges',
  'Ex : PIN (4digit)' => 'Ex : PIN (4digit)',
  'Unused eMoney' => 'Unused eMoney',
  'In Minute' => 'In Minute',
  'Inactive authentication time' => 'Inactive authentication time',
  'Given credentials are not correct. Try again.' => 'Given credentials are not correct. Try again.',
  'eMoney Being Used' => 'EMoney Being Used',
  'Total Generated eMoney' => 'Total Generated eMoney',
  'Notification Settings' => 'Notification Settings',
  'App Settings' => 'App Settings',
  'App settings' => 'App settings',
  'Select for app theme' => 'Select for app theme',
  'Theme 1' => 'Theme 1',
  'Theme 3' => 'Theme 3',
  'Theme 2' => 'Theme 2',
  'Select for user app theme' => 'Select for user app theme',
  'Select for User App Theme' => 'Select for User App Theme',
  'Select Theme for User App' => 'Select Theme for User App',
  'PIN' => 'PIN',
  '$digit PIN' => '$digit PIN',
  '4digit PIN' => '4digit PIN',
  'No image available' => 'No image available',
  'Time' => 'Time',
  'E-Money' => 'E-Money',
  'ReCaptcha Google Credentials Setup' => 'ReCaptcha Google Credentials Setup',
  'Enter captcha' => 'Enter captcha',
  'Enter captcha.' => 'Enter captcha.',
  'This text will be replaced in the future. This text will be replaced in the future. This is dummy text.' => 'This text will be replaced in the future. This text will be replaced in the future. This is dummy text.',
  'Welcome to 6Cash' => 'Welcome to Arefan Wallet',
  '6 cash is a secured and user-friendly digital wallet' => 'Arefan Wallet is a secured and user-friendly digital wallet',
  'Update Notification' => 'Update Notification',
  'Resend' => 'Resend',
  'edit & resend' => 'Edit & resend',
  '6cash is a secured and user-friendly digital wallet' => 'Arefan Wallet is a secured and user-friendly digital wallet',
  'User will be logged out if no activity happened within this time' => 'User will be logged out if no activity happened within this time',
  'Inactive auth token expire time' => 'Inactive auth token expire time',
  'Customers can use these purposes when they will send money or request money' => 'Customers can use these purposes when they will send money or request money',
  'agent_commission' => 'Agent commission',
  'Successfully deleted' => 'Successfully deleted',
  'Not found' => 'Not found',
  'Total Transaction' => 'Total Transaction',
  'Redirecting_to_the_payment_page' => 'Redirecting to the payment page',
  'SenderQQQQ' => 'SenderQQQQ',
  'Welcome to 6cash' => 'Welcome to 6cash',
  'Welcome to 6cash @' => 'Welcome to 6cash @',
  '6cash @' => '6cash @',
  '6cash @ is a secured and user-friendly digital wallet' => '6cash @ is a secured and user-friendly digital wallet',
  'User Not found' => 'User Not found',
  'amount must be greater than equal to ' => 'Amount must be greater than equal to ',
  'Amount is too low to transfer' => 'Amount is too low to transfer',
  'amount must be less than equal to ' => 'Amount must be less than equal to ',
  'nill' => 'Nill',
  'nil' => 'Nil',
  'The amount must be less than equal to ' => 'The amount must be less than equal to ',
  'The amount is too low to transfer' => 'The amount is too low to transfer',
  'Welcome to 6CashPay' => 'Welcome to 6CashPay',
  '6CashPay is a secured and user-friendly digital wallet' => '6CashPay is a secured and user-friendly digital wallet',
  'The requested amount is too big' => 'The requested amount is too big',
  'KYC Requests' => 'KYC Requests',
  'approve' => 'Approve',
  'deny' => 'Deny',
  'Identification Type' => 'Identification Type',
  'Identification Image' => 'Identification Image',
  'Identification Number' => 'Identification Number',
  'Type unavailable' => 'Type unavailable',
  'Number unavailable' => 'Number unavailable',
  'click for bigger view' => 'Click for bigger view',
  'Successfully updated.' => 'Successfully updated.',
  'KYC requests list' => 'KYC requests list',
  'Agent KYC requests' => 'Agent KYC requests',
  'The amount must be less than or equal to ' => 'The amount must be less than or equal to ',
  'identification_type' => 'Identification type',
  'identification_number' => 'Identification number',
  'Verification Requests' => 'Verification Requests',
  'Add_withdrawal_methods' => 'Add withdrawal methods',
  'sdasdas' => 'Sdasdas',
  'Select Attributes' => 'Select Attributes',
  'required' => 'Required',
  'min' => 'Min',
  'max' => 'Max',
  'minlength' => 'Minlength',
  'maxlength' => 'Maxlength',
  'placeholder' => 'Placeholder',
  'step' => 'Step',
  'Labels' => 'Labels',
  'Attributes_intro' => 'Attributes intro',
  'this is name' => 'This is name',
  'a default value will be given for the user' => 'A default value will be given for the user',
  'hint will be shown for the user' => 'Hint will be shown for the user',
  'user must have to give the info' => 'User must have to give the info',
  'minimum value for the given value' => 'Minimum value for the given value',
  'maximum value for the given value' => 'Maximum value for the given value',
  'minlength length for the given value' => 'Minlength length for the given value',
  'maximum length for the given value' => 'Maximum length for the given value',
  'Attribute for the field' => 'Attribute for the field',
  'Method Name' => 'Method Name',
  'string' => 'String',
  'Required' => 'Required',
  'Required/Optional' => 'Required/Optional',
  'Withdrawal Method add' => 'Withdrawal Method add',
  'Withdrawal Method Add' => 'Withdrawal Method Add',
  'Method Type' => 'Method Type',
  'Is Required' => 'Is Required',
  'Add Method' => 'Add Method',
  'Method Fields' => 'Method Fields',
  'Method Field Name' => 'Method Field Name',
  'Name' => 'Name',
  'Fields Name' => 'Fields Name',
  'reset' => 'Reset',
  'String' => 'String',
  'Number' => 'Number',
  'Input Name' => 'Input Name',
  'Input Type' => 'Input Type',
  'Field Name' => 'Field Name',
  'Field Type' => 'Field Type',
  'Add Fields' => 'Add Fields',
  'Reset All' => 'Reset All',
  'Fields' => 'Fields',
  'successfully removed' => 'Successfully removed',
  'Are you sure' => 'Are you sure',
  'Removed successfully' => 'Removed successfully',
  'Reached maximum' => 'Reached maximum',
  'ok' => 'Ok',
  'Filter by method' => 'Filter by method',
  'Kane Scott' => 'Kane Scott',
  'BKASH' => 'BKASH',
  'Paypal' => 'Paypal',
  'qawrewr' => 'Qawrewr',
  'Withdrawal Method' => 'Withdrawal Method',
  'Withdraw_Requests' => 'Withdraw Requests',
  'Sender_Note' => 'Sender Note',
  'Admin_Note' => 'Admin Note',
  'Payment_Status' => 'Payment Status',
  'User_not_available' => 'User not available',
  'Not_Paid' => 'Not Paid',
  'Is_Paid' => 'Is Paid',
  'Request_Status' => 'Request Status',
  'Actions' => 'Actions',
  'approved' => 'Approved',
  'Paid' => 'Paid',
  'denied' => 'Denied',
  'The request has been successfully updated' => 'The request has been successfully updated',
  'User Log' => 'User Log',
  'User Logs' => 'User Logs',
  'Users Log' => 'Users Log',
  'login_time' => 'Login time',
  'device_model' => 'Device model',
  'os' => 'Os',
  'browser' => 'Browser',
  'mac_address' => 'Mac address',
  'ip_address' => 'Ip address',
  'Logs' => 'Logs',
  'Log' => 'Log',
  'Agent Log' => 'Agent Log',
  'Invalid data' => 'Invalid data',
  'device_id' => 'Device id',
  'BCASH' => 'BCASH',
  'pin' => 'Pin',
  'Withdrawal Method Fields' => 'Withdrawal Method Fields',
  'Date' => 'Date',
  'Test' => 'Test',
  'Card' => 'Card',
  'withdraw' => 'Withdraw',
  'Export' => 'Export',
  'Input Field Placeholder/Hints' => 'Input Field Placeholder/Hints',
  'Input Field Name' => 'Input Field Name',
  'Input Field Type' => 'Input Field Type',
  0 => '0',
  'Placeholder' => 'Placeholder',
  'Tyrone Russell' => 'Tyrone Russell',
  'No_data_available' => 'No data available',
  'Not_available' => 'Not available',
  'The request sender is unavailable' => 'The request sender is unavailable',
  'Sender Type' => 'Sender Type',
  'Verification requests list' => 'Verification requests list',
  'Agent Verification requests' => 'Agent Verification requests',
  'Verification List' => 'Verification List',
  'Agent/Customer will use these methods to withdraw their money directly from admin' => 'Agent/Customer will use these methods to withdraw their money directly from admin',
  'Remove the input field' => 'Remove the input field',
  'verification_request_is_accepted' => 'Verification request is accepted',
  'verification_request_is_denied' => 'Verification request is denied',
  '6Cash is a secured and user-friendly digital wallet' => '6Cash is a secured and user-friendly digital wallet',
  'Add New Merchant' => 'Add New Merchant',
  'Merchant Image' => 'Merchant Image',
  'Ex : 434624829' => 'Ex : 434624829',
  'Store Name' => 'Store Name',
  'Store Domain' => 'Store Domain',
  'Address' => 'Address',
  'BIN' => 'BIN',
  'Online Payment' => 'Online Payment',
  'card_holder\'s_name' => 'Card holder s name',
  'expiry_date' => 'Expiry date',
  'cvv' => 'Cvv',
  'Ex : DH-23434-LS' => 'Ex : DH-23434-LS',
  'Deliveryman Image' => 'Deliveryman Image',
  'Identity Image' => 'Identity Image',
  'Merchant Added Successfully!' => 'Merchant Added Successfully!',
  'merchant Table' => 'Merchant Table',
  'Ex: 534354' => 'Ex: 534354',
  'Update Merchant' => 'Update Merchant',
  'erchant' => 'Erchant',
  'Merchant Added Failed!' => 'Merchant Added Failed!',
  'Merchant Updated Failed!' => 'Merchant Updated Failed!',
  'Merchant Updated Successfully!' => 'Merchant Updated Successfully!',
  'store_name' => 'Store name',
  'store_domain' => 'Store domain',
  'merchant_number' => 'Merchant number',
  'Merchant Verification requests' => 'Merchant Verification requests',
  'merchants' => 'Merchants',
  'Merchant' => 'Merchant',
  'welcome_to_6cash_merchant_panel' => 'Welcome to 6cash merchant panel',
  'shop' => 'Shop',
  'integration' => 'Integration',
  'Integration Settings' => 'Integration Settings',
  'Public Key' => 'Public Key',
  'generate_code' => 'Generate code',
  'coupon code' => 'Coupon code',
  'Generate Public Key' => 'Generate Public Key',
  'Generate Secret Key' => 'Generate Secret Key',
  'generate' => 'Generate',
  'developer' => 'Developer',
  'Public key is required' => 'Public key is required',
  'Secret key is required' => 'Secret key is required',
  'Merchant number is required' => 'Merchant number is required',
  'Amount is required' => 'Amount is required',
  'User phone is required' => 'User phone is required',
  'Callback url is required' => 'Callback url is required',
  'Payment successful' => 'Payment successful',
  'success' => 'Success',
  'Your 6Cash Account Number' => 'Your 6Cash Account Number',
  '+88 017XXXXXXXX' => '+88 017XXXXXXXX',
  'I agree to the' => 'I agree to the',
  'Terms & conditions' => 'Terms & conditions',
  'Proceed' => 'Proceed',
  'Hotline' => 'Hotline',
  847283 => '847283',
  'OTP' => 'OTP',
  'Cancel' => 'Cancel',
  'Enter Verification Code' => 'Enter Verification Code',
  'Resend Code' => 'Resend Code',
  'Enter PIN Number' => 'Enter PIN Number',
  'PIN Number' => 'PIN Number',
  'Confirm' => 'Confirm',
  'Payment Successfully Completed' => 'Payment Successfully Completed',
  'Phone is required' => 'Phone is required',
  'OTP send !' => 'OTP send !',
  'Check terms and condition' => 'Check terms and condition',
  'Pin must be 4 digit' => 'Pin must be 4 digit',
  'You do not have enough balance. Please generate eMoney first.' => 'You do not have enough balance. Please generate eMoney first.',
  'Payment failed !' => 'Payment failed !',
  'Payment successful !' => 'Payment successful !',
  'please enter a valid phone' => 'Please enter a valid phone',
  'OTP is required' => 'OTP is required',
  'OTP must be 4 digit' => 'OTP must be 4 digit',
  'success !' => 'Success !',
  'Pin is required' => 'Pin is required',
  'wrong password !' => 'Wrong password !',
  'wrong pin !' => 'Wrong pin !',
  'No' => 'No',
  'Yes' => 'Yes',
  'Change status to pending ?' => 'Change status to pending  ',
  'Merchant Setup' => 'Merchant Setup',
  'Merchant OTP ' => 'Merchant OTP ',
  'Merchant OTP Verification' => 'Merchant OTP Verification',
  'OTP verify failed !' => 'OTP verify failed !',
  'pin mismatched !' => 'Pin mismatched !',
  'Transaction id is required' => 'Transaction id is required',
  'Payment OTP Verification' => 'Payment OTP Verification',
  'Merchant OTP' => 'Merchant OTP',
  'user not found !' => 'User not found !',
  'Withdraw_request_accepted' => 'Withdraw request accepted',
  'Withdraw' => 'Withdraw',
  'add withdraw request' => 'Add withdraw request',
  'withdraw request list' => 'Withdraw request list',
  'request' => 'Request',
  'Withdraw Request' => 'Withdraw Request',
  'Add Request' => 'Add Request',
  'select' => 'Select',
  'withdraw methods' => 'Withdraw methods',
  'payment Method' => 'Payment Method',
  'Withdraw Request List' => 'Withdraw Request List',
  'sender note' => 'Sender note',
  'Sender Note' => 'Sender Note',
  'Withdraw request send !' => 'Withdraw request send !',
  'Withdraw_request_denied' => 'Withdraw request denied',
  'Available Balance' => 'Available Balance',
  'Current eMoney' => 'Current eMoney',
  'OTP verify success !' => 'OTP verify success !',
  '6 digit PIN' => '6 digit PIN',
  'Merchant List' => 'Merchant List',
  'regenerate' => 'Regenerate',
  'Merchant Number' => 'Merchant Number',
  'Current Ballance' => 'Current Ballance',
  'Total Withdraw' => 'Total Withdraw',
  'You want to regenerate public key and merchant key' => 'You want to regenerate public key and merchant key',
  'Ex : 17********' => 'Ex : 17********',
  'Ex : 171*******' => 'Ex : 171*******',
  'PIN must contain 6 characters' => 'PIN must contain 6 characters',
  'Country code select is required' => 'Country code select is required',
  'Verification Code' => 'Verification Code',
  'select country' => 'Select country',
  'Country code is required' => 'Country code is required',
  'Password must contain 4 characters' => 'Password must contain 4 characters',
  'country code' => 'Country code',
  'Withdraw Table' => 'Withdraw Table',
  '8 digit PIN' => '8 digit PIN',
  'PIN must contain 8 characters' => 'PIN must contain 8 characters',
  'Payment time expired' => 'Payment time expired',
  'Hotline Number' => 'Hotline Number',
  34532525 => '34532525',
  123456789 => '123456789',
  'Terms_and_conditions' => 'Terms and conditions',
  'About_us' => 'About us',
  'Privacy_policy' => 'Privacy policy',
  'Terms and conditions' => 'Terms and conditions',
  'aa' => 'Aa',
  'debit' => 'Debit',
  'credit' => 'Credit',
  'Domain url is required' => 'Domain url is required',
  'Domain is required' => 'Domain is required',
  'This phone number is already taken' => 'This phone number is already taken',
  'Merchant Config' => 'Merchant Config',
  'Merchant Settings' => 'Merchant Settings',
  'Transaction Commission' => 'Transaction Commission',
  '4 digit PIN' => '4 digit PIN',
  'Add Domain' => 'Add Domain',
  'Domain Callback' => 'Domain Callback',
  'Domain' => 'Domain',
  'Callback' => 'Callback',
  'Payment Received' => 'Payment Received',
  'callback' => 'Callback',
  'driving_license' => 'Driving license',
  'store_callback' => 'Store callback',
  'Add Payment Message' => 'Add Payment Message',
  '8 digit Password' => '8 digit Password',
  'This user do not have enough balance. Please generate eMoney first.' => 'This user do not have enough balance. Please generate eMoney first.',
  'Pending Ballance' => 'Pending Ballance',
  '8 digit password' => '8 digit password',
  'fail' => 'Fail',
  'Agent/Customer/Merchant will use these methods to withdraw their money directly from admin' => 'Agent/Customer/Merchant will use these methods to withdraw their money directly from admin',
  'Business Analytics' => 'Business Analytics',
  'E-Money Statistics' => 'E-Money Statistics',
  'Search by Name' => 'Search by Name',
  'Search by ID' => 'Search by ID',
  'Search by Agent' => 'Search by Agent',
  'Update_Agent' => 'Update Agent',
  'Agent Details' => 'Agent Details',
  'Agent Transactions' => 'Agent Transactions',
  'Agent Details Logs' => 'Agent Details Logs',
  'sdf' => 'Sdf',
  'g-recaptcha-response google reCaptcha failed' => 'G-recaptcha-response google reCaptcha failed',
  'Contact' => 'Contact',
  'Contacts' => 'Contacts',
  'banner_image' => 'Banner image',
  'banner Image' => 'Banner Image',
  'Search by Title' => 'Search by Title',
  'Add New Purpose' => 'Add New Purpose',
  'Update Purpose' => 'Update Purpose',
  'Update Website' => 'Update Website',
  'update Notification' => 'Update Notification',
  'Sms Module' => 'Sms Module',
  'Payment Methods' => 'Payment Methods',
  'Recaptcha' => 'Recaptcha',
  'Languages' => 'Languages',
  'Business Settings' => 'Business Settings',
  'Pages' => 'Pages',
  'pages' => 'Pages',
  'system' => 'System',
  'Business Information' => 'Business Information',
  'form' => 'Form',
  'Instructions' => 'Instructions',
  'Resource not found' => 'Resource not found',
  'Request not found' => 'Request not found',
  'expense' => 'Expense',
  'withdraw_charge' => 'Withdraw charge',
  'Charge Setup' => 'Charge Setup',
  'Charge Config' => 'Charge Config',
  'Bonus' => 'Bonus',
  'Add New bonus' => 'Add New bonus',
  'bonus Table' => 'Bonus Table',
  'Add bonus' => 'Add bonus',
  'User Type' => 'User Type',
  'maximum_add_money_amount' => 'Maximum add money amount',
  'maximum_bonus_amount' => 'Maximum bonus amount',
  'limit_per_user' => 'Limit per user',
  'start_date_time' => 'Start date time',
  'end_date_time' => 'End date time',
  'Flat' => 'Flat',
  'Percentage' => 'Percentage',
  'bonus' => 'Bonus',
  'Bonus Type' => 'Bonus Type',
  'maximum Add Money Amount' => 'Maximum Add Money Amount',
  'maximum Bonus Amount' => 'Maximum Bonus Amount',
  'limit Per User' => 'Limit Per User',
  'Each user will only receive the bonus up to the limit' => 'Each user will only receive the bonus up to the limit',
  'End Date' => 'End Date',
  'End Up' => 'End Up',
  'Start Date' => 'Start Date',
  'Bonus added successfully!' => 'Bonus added successfully!',
  'Limit Per User' => 'Limit Per User',
  'Maximum Add Money Amount' => 'Maximum Add Money Amount',
  'Bonus status updated!' => 'Bonus status updated!',
  'Bonus removed!' => 'Bonus removed!',
  'Update bonus' => 'Update bonus',
  'Bonus updated successfully!' => 'Bonus updated successfully!',
  'Maximum Bonus Amount' => 'Maximum Bonus Amount',
  'Minimum Add Money Amount' => 'Minimum Add Money Amount',
  'percentage' => 'Percentage',
  'Total Expense' => 'Total Expense',
  'Add Money Bonus' => 'Add Money Bonus',
  'Last Year' => 'Last Year',
  'This Year' => 'This Year',
  'Last Month' => 'Last Month',
  'This Month' => 'This Month',
  'This Week' => 'This Week',
  'Total Debit' => 'Total Debit',
  'Total Users' => 'Total Users',
  'Total Transactions' => 'Total Transactions',
  'business setup' => 'Business setup',
  'View All' => 'View All',
  'E1dit' => 'E1dit',
  'Master Admin' => 'Master Admin',
  'admin panel' => 'Admin panel',
  'Transaction Charge Configuration' => 'Transaction Charge Configuration',
  'Transaction Charges' => 'Transaction Charges',
  'Agent will get the percentage from cash out charge' => 'Agent will get the percentage from cash out charge',
  'The customer will be charged the percentage from cash out amount.' => 'The customer will be charged the percentage from cash out amount.',
  'The sender will be charged the percentage of the sending money amount' => 'The sender will be charged the percentage of the sending money amount',
  'The sender will be charged the amount while sending money to others' => 'The sender will be charged the amount while sending money to others',
  'Merchant Details' => 'Merchant Details',
  'No data available' => 'No data available',
  'Expense Transactions' => 'Expense Transactions',
  'Email Unavailable' => 'Email Unavailable',
  'driving_licence' => 'Driving licence',
  'My Method' => 'My Method',
  'Filter' => 'Filter',
  'terms and Condition' => 'Terms and Condition',
  'privacy Plicy' => 'Privacy Plicy',
  'terms & Condition' => 'Terms & Condition',
  'privacy Policy' => 'Privacy Policy',
  'about Us' => 'About Us',
  'Agents or customers will be charged the percentage of the withdrawal amount' => 'Agents or customers will be charged the percentage of the withdrawal amount',
  'The amount is too big. Please contact with admin' => 'The amount is too big. Please contact with admin',
  'Add Withdrawal Methods' => 'Add Withdrawal Methods',
  'No Data Available' => 'No Data Available',
  'The customer will be charged the percentage of cash out amount' => 'The customer will be charged the percentage of cash out amount',
  'The agent will get the percentage from cash out charge' => 'The agent will get the percentage from cash out charge',
  'choose File' => 'Choose File',
  'Favicon' => 'Favicon',
  'Mail Cash' => 'Mail Cash',
  'Search by user info' => 'Search by user info',
  'Search by ip_address, device_id, browser, os or device_model' => 'Search by ip address  device id  browser  os or device model',
  'Search by ip, device id, browser, os or device model' => 'Search by ip  device id  browser  os or device model',
  'Search by ip, deviceId, browser, os or device model' => 'Search by ip  deviceId  browser  os or device model',
  'The customer will be charged the amount while sending money to others' => 'The customer will be charged the amount while sending money to others',
  'Customers can use these purposes when they will send money' => 'Customers can use these purposes when they will send money',
  'Merchant Panel' => 'Merchant Panel',
  'Admin Charge' => 'Admin Charge',
  'Expense' => 'Expense',
  'The withdraw request sender will be charged the percentage of the withdrawal amount' => 'The withdraw request sender will be charged the percentage of the withdrawal amount',
  'Receiver is not verified' => 'Receiver is not verified',
  'Receiver must be a user' => 'Receiver must be a user',
  'Request Status' => 'Request Status',
  'Add Money Charge Message' => 'Add Money Charge Message',
  'Add Money Bonus Message' => 'Add Money Bonus Message',
  'Click to refresh' => 'Click to refresh',
  'add_money_bonus' => 'Add money bonus',
  'Bonus will be applied depending on the highest valid "Minimum Add Money amount"' => 'Bonus will be applied depending on the highest valid  Minimum Add Money amount ',
  'Bonus will be applied depending on the highest valid Minimum Add Money amount' => 'Bonus will be applied depending on the highest valid Minimum Add Money amount',
  'Welcome to ' => 'Welcome to ',
  '6cash' => '6cash',
  'Welcome to wer' => 'Welcome to wer',
  'wer is a secured and user-friendly digital wallet' => 'Wer is a secured and user-friendly digital wallet',
  'Welcome to test 6cash' => 'Welcome to test 6cash',
  'test 6cash is a secured and user-friendly digital wallet' => 'Test 6cash is a secured and user-friendly digital wallet',
  'Enter new password' => 'Enter new password',
  'Confirm your new password' => 'Confirm your new password',
  'Login and OTP Setup' => 'Login and OTP Setup',
  'OTP Setup' => 'OTP Setup',
  'maximum_OTP_submit_attempt' => 'Maximum OTP submit attempt',
  'The maximum OTP hit is a measure of how many times a specific one-time password has been generated and used within a time.' => 'The maximum OTP hit is a measure of how many times a specific one-time password has been generated and used within a time.',
  'in_second' => 'In second',
  'otp_resend_time' => 'Otp resend time',
  'If the user fails to get the OTP within a certain time, user can request a resend.' => 'If the user fails to get the OTP within a certain time  user can request a resend.',
  'temporary_block_time' => 'Temporary block time',
  'Temporary OTP block time refers to a security measure implemented by systems to restrict access to OTP service for a specified period of time for wrong OTP submission.' => 'Temporary OTP block time refers to a security measure implemented by systems to restrict access to OTP service for a specified period of time for wrong OTP submission.',
  'maximum_login_attempt' => 'Maximum login attempt',
  'The maximum login hit is a measure of how many times a user can submit password within a time.' => 'The maximum login hit is a measure of how many times a user can submit password within a time.',
  'temporary_login_block_time' => 'Temporary login block time',
  'Temporary login block time refers to a security measure implemented by systems to restrict access for a specified period of time for wrong Password submission.' => 'Temporary login block time refers to a security measure implemented by systems to restrict access for a specified period of time for wrong Password submission.',
  'OTP_resend_time' => 'OTP resend time',
  'Settings updated!' => 'Settings updated!',
  'System Feature' => 'System Feature',
  'Add Money' => 'Add Money',
  'Send Money' => 'Send Money',
  'Cash Out' => 'Cash Out',
  'Send Money Request' => 'Send Money Request',
  'Transaction Limits' => 'Transaction Limits',
  'please_try_again_after_' => 'Please try again after ',
  'seconds' => 'Seconds',
  'Customer Transaction Limits' => 'Customer Transaction Limits',
  'Daily Transaction' => 'Daily Transaction',
  'Monthly Transaction' => 'Monthly Transaction',
  'Add Money Limit' => 'Add Money Limit',
  'Cookie Text' => 'Cookie Text',
  'Ex: 5' => 'Ex: 5',
  'transaction_limit_per_day' => 'Transaction limit per day',
  'Transaction Limit Per Day' => 'Transaction Limit Per Day',
  'Ex: 100' => 'Ex: 100',
  'Ex: 500' => 'Ex: 500',
  'Max Amount per Transaction' => 'Max Amount per Transaction',
  'Total Transaction Amount Per Day' => 'Total Transaction Amount Per Day',
  'Transaction Limit Per Month' => 'Transaction Limit Per Month',
  'Total Transaction_amount_per_month' => 'Total Transaction amount per month',
  'Send Money Limit' => 'Send Money Limit',
  'Ex: 10' => 'Ex: 10',
  'Send Money Request Limit' => 'Send Money Request Limit',
  'Cash Out Limit' => 'Cash Out Limit',
  'Withdraw Request Limit' => 'Withdraw Request Limit',
  'Receiver not found' => 'Receiver not found',
  'maximum amount per transaction exceeded' => 'Maximum amount per transaction exceeded',
  'transaction limit per day exceeded' => 'Transaction limit per day exceeded',
  'PIN is incorrect' => 'PIN is incorrect',
  'total transaction amount per day exceeded' => 'Total transaction amount per day exceeded',
  'transaction limit per month exceeded' => 'Transaction limit per month exceeded',
  'Agent Self Registration' => 'Agent Self Registration',
  'Too_many_attempts. please_try_again_after_' => 'Too many attempts. please try again after ',
  'Transaction limit per day cannot be greater than the transaction limit per month.' => 'Transaction limit per day cannot be greater than the transaction limit per month.',
  'Your account is temporarily blocked. Please_try_again_after_' => 'Your account is temporarily blocked. Please try again after ',
  'Try_again_after' => 'Try again after',
  'Welcome to 9cash' => 'Welcome to 9cash',
  '9cash is a secured and user-friendly digital wallet' => '9cash is a secured and user-friendly digital wallet',
  'Maximum amount per transaction cannot be greater than the total transaction amount per day.' => 'Maximum amount per transaction cannot be greater than the total transaction amount per day.',
  'Too_many_attempts. Please_try_again_after_' => 'Too many attempts. Please try again after ',
  'When this feature is enabled, transaction limits will be applied on a daily and monthly basis.' => 'When this feature is enabled  transaction limits will be applied on a daily and monthly basis.',
  'The maximum number of transactions allowed in a day is set by this setting.' => 'The maximum number of transactions allowed in a day is set by this setting.',
  'The maximum amount of money that can be used in a single transaction is set by this setting.' => 'The maximum amount of money that can be used in a single transaction is set by this setting.',
  'This field refers to the maximum amount of money for transactions in a day.' => 'This field refers to the maximum amount of money for transactions in a day.',
  'The maximum number of transactions allowed in a month is set by this setting.' => 'The maximum number of transactions allowed in a month is set by this setting.',
  'This field refers to the maximum amount of money for transactions in a month.' => 'This field refers to the maximum amount of money for transactions in a month.',
  'When this field is active agent can register themself using the agent app' => 'When this field is active agent can register themself using the agent app',
  'Money Request Limit' => 'Money Request Limit',
  'SMS Module' => 'SMS Module',
  'resend' => 'Resend',
);