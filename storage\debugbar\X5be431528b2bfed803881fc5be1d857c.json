{"__meta": {"id": "X5be431528b2bfed803881fc5be1d857c", "datetime": "2025-07-07 14:27:37", "utime": 1751876857.302653, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876856.800609, "end": 1751876857.30268, "duration": 0.5020709037780762, "duration_str": "502ms", "measures": [{"label": "Booting", "start": 1751876856.800609, "relative_start": 0, "end": 1751876857.138078, "relative_end": 1751876857.138078, "duration": 0.33746886253356934, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876857.138185, "relative_start": 0.33757591247558594, "end": 1751876857.302683, "relative_end": 3.0994415283203125e-06, "duration": 0.16449809074401855, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002184, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876857.191466, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876857.286598, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-215676821 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-215676821\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-712569465 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-712569465\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1576538735 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1576538735\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1498832007 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik90cW9uNFJPRVN6cnJGeE5wd3RYUGc9PSIsInZhbHVlIjoienRuSmJFRDBFS1BVTDg0Z29EMXh6b3h0ZUZua3ZlaFN5VStreTg0NXltc2NyOE05SVNHaVBlQ3pCVTRLbjRrSGZXR0hmMlowbXB5V1Y3bEh3SW1PaHlvMjhFZW5NUGhWS0JNNXlWL2lDVW9GK3UxVE5kK1VrejJ4Z3UxcFFuM2QiLCJtYWMiOiIzODExYTYwM2E0ZDM1YWQ2MjRjZTA0ZWM2MzE0NWIxYzIzN2ZkZDk3MDdhOWEyN2Q2OTNkNDc2N2YyYjlmOTRlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IktqR2hDV2VWd09yRXU3WXJQU3owdnc9PSIsInZhbHVlIjoiNUtWNGZxeDNJZG42bDQyc3llcnJVaW5BVUllQ2t5eGZaRjg2ZGhFdjZxY2NPRHZsWlJxU2k3a1NGczhUSUljaWdUREhqSHRFWGRxazFsdW9HZW5KV2FDdGRvM0N3aVlGY1QrZmlhU1BaQ3JyUzdRRkxkOUlpYjc5WlEwTWU5cnQiLCJtYWMiOiJiNmJmZGEyMjU2MGY4ZDE4Nzk0OTIzZDQwNjIyYTllNTAwODQxMWMxMWRlYWU2ZGE1YTQzODdjMzI4NDE2NDlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498832007\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-137039286 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-137039286\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-886726218 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjJ2K2hmRzVSeHB5VWJIZmJxUXRJaFE9PSIsInZhbHVlIjoiVmRBY2w2ZDg0eldBb0hkS3FCR0xDTHc1Vld3dU5Id01xU3VMZGVhdElJajBaeXMzeFpiUnBUWm11a1E5R3RGT3NybTVKMkJ0WXpvaTZDcldyKzZWdzdXdHN1dFprRTJIRTIyNzRTbkp2b2NMcFc4djJHM2pFMVZZaXZkTWFkc3AiLCJtYWMiOiJjM2ZiN2YyOTQ3NTA2MDJjMDZlMDYyYzgyNzI2MmYyNGEyYmM2NTU5YmIyYzYzZWU4NjhiNzdjOGFmOTFhYzAwIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:37 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ikt5TUcxSlMzVGFtdnNCNm1CRlZKS3c9PSIsInZhbHVlIjoiK2tQbTRKckcyTXRRbXROQUxMZVBwVXZzQ29ZSHZBVDhMemZLeUJ6RHZjMUljVTNJWnd5R3BpSG5jVld5KzRGelRvU1hmVEZES3AvNWtPc0lnTi84MzQ1Q2dRcGJ1cExHbExWK1UraE94RWRLL2tIaEVaNUduY2krcHYzeG55Y0EiLCJtYWMiOiJiZTlmMDc2NmI0MWY2YjhmOGRkMDYyNDY1NzVhMTAyYzc5ZDRlMjdjM2JjZjMzYTcwODZiNjJiZGQ5OWFmMzMzIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:37 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJ2K2hmRzVSeHB5VWJIZmJxUXRJaFE9PSIsInZhbHVlIjoiVmRBY2w2ZDg0eldBb0hkS3FCR0xDTHc1Vld3dU5Id01xU3VMZGVhdElJajBaeXMzeFpiUnBUWm11a1E5R3RGT3NybTVKMkJ0WXpvaTZDcldyKzZWdzdXdHN1dFprRTJIRTIyNzRTbkp2b2NMcFc4djJHM2pFMVZZaXZkTWFkc3AiLCJtYWMiOiJjM2ZiN2YyOTQ3NTA2MDJjMDZlMDYyYzgyNzI2MmYyNGEyYmM2NTU5YmIyYzYzZWU4NjhiNzdjOGFmOTFhYzAwIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ikt5TUcxSlMzVGFtdnNCNm1CRlZKS3c9PSIsInZhbHVlIjoiK2tQbTRKckcyTXRRbXROQUxMZVBwVXZzQ29ZSHZBVDhMemZLeUJ6RHZjMUljVTNJWnd5R3BpSG5jVld5KzRGelRvU1hmVEZES3AvNWtPc0lnTi84MzQ1Q2dRcGJ1cExHbExWK1UraE94RWRLL2tIaEVaNUduY2krcHYzeG55Y0EiLCJtYWMiOiJiZTlmMDc2NmI0MWY2YjhmOGRkMDYyNDY1NzVhMTAyYzc5ZDRlMjdjM2JjZjMzYTcwODZiNjJiZGQ5OWFmMzMzIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-886726218\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1774078217 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774078217\", {\"maxDepth\":0})</script>\n"}}