{"__meta": {"id": "Xccd0d6f7e5e42241c8dcf9258f327b26", "datetime": "2025-07-07 14:44:25", "utime": **********.683951, "method": "POST", "uri": "/api/v1/customer/auth/check-phone", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.224209, "end": **********.683968, "duration": 0.459758996963501, "duration_str": "460ms", "measures": [{"label": "Booting", "start": **********.224209, "relative_start": 0, "end": **********.494924, "relative_end": **********.494924, "duration": 0.27071499824523926, "duration_str": "271ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.494936, "relative_start": 0.27072691917419434, "end": **********.68397, "relative_end": 1.9073486328125e-06, "duration": 0.18903398513793945, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25144232, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/customer/auth/check-phone", "middleware": "api, deviceVerify", "controller": "App\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController@checkPhone", "namespace": "App\\Http\\Controllers\\Api\\V1\\Auth\\Auth", "prefix": "api/v1/customer/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FCustomer%2FAuth%2FCustomerAuthController.php&line=43\" onclick=\"\">app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php:43-111</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.08143000000000002, "accumulated_duration_str": "81.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where (`phone` = '+*************') and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["+*************"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5421941, "duration": 0.07141, "duration_str": "71.41ms", "memory": 0, "memory_str": null, "filename": "CustomerAuthController.php:53", "source": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php:53", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FCustomer%2FAuth%2FCustomerAuthController.php&line=53", "ajax": false, "filename": "CustomerAuthController.php", "line": "53"}, "connection": "wallet_db", "start_percent": 0, "width_percent": 87.695}, {"sql": "select * from `business_settings` where (`key` = 'phone_verification') limit 1", "type": "query", "params": [], "bindings": ["phone_verification"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.62026, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "CustomerAuthController.php:69", "source": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php:69", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FCustomer%2FAuth%2FCustomerAuthController.php&line=69", "ajax": false, "filename": "CustomerAuthController.php", "line": "69"}, "connection": "wallet_db", "start_percent": 87.695, "width_percent": 0.774}, {"sql": "select * from `business_settings` where (`key` = 'otp_resend_time') limit 1", "type": "query", "params": [], "bindings": ["otp_resend_time"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController.php", "line": 71}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.625659, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 88.469, "width_percent": 0.774}, {"sql": "select * from `phone_verifications` where `phone` = '+*************' limit 1", "type": "query", "params": [], "bindings": ["+*************"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController.php", "line": 72}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.630706, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "CustomerAuthController.php:72", "source": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php:72", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FCustomer%2FAuth%2FCustomerAuthController.php&line=72", "ajax": false, "filename": "CustomerAuthController.php", "line": "72"}, "connection": "wallet_db", "start_percent": 89.242, "width_percent": 1.842}, {"sql": "select exists(select * from `phone_verifications` where (`phone` = '+*************')) as `exists`", "type": "query", "params": [], "bindings": ["+*************"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController.php", "line": 85}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6363602, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "CustomerAuthController.php:85", "source": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php:85", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FCustomer%2FAuth%2FCustomerAuthController.php&line=85", "ajax": false, "filename": "CustomerAuthController.php", "line": "85"}, "connection": "wallet_db", "start_percent": 91.084, "width_percent": 0.774}, {"sql": "insert into `phone_verifications` (`phone`, `otp`, `otp_hit_count`, `is_temp_blocked`, `temp_block_time`, `created_at`, `updated_at`) values ('+*************', 9226, 0, 0, '', '2025-07-07 14:44:25', '2025-07-07 14:44:25')", "type": "query", "params": [], "bindings": ["+*************", "9226", "0", "0", "", "2025-07-07 14:44:25", "2025-07-07 14:44:25"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController.php", "line": 85}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.642104, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "CustomerAuthController.php:85", "source": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php:85", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FCustomer%2FAuth%2FCustomerAuthController.php&line=85", "ajax": false, "filename": "CustomerAuthController.php", "line": "85"}, "connection": "wallet_db", "start_percent": 91.858, "width_percent": 4.36}, {"sql": "select * from `addon_settings` where `key_name` = 'twilio' and `settings_type` = 'sms_config' limit 1", "type": "query", "params": [], "bindings": ["twi<PERSON>", "sms_config"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Lib/Helper.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Lib\\Helper.php", "line": 198}, {"index": 15, "namespace": null, "name": "app/CentralLogics/sms_module.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\sms_module.php", "line": 16}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController.php", "line": 97}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6509, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Helper.php:198", "source": "app/Lib/Helper.php:198", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FLib%2FHelper.php&line=198", "ajax": false, "filename": "Helper.php", "line": "198"}, "connection": "wallet_db", "start_percent": 96.218, "width_percent": 1.289}, {"sql": "select * from `addon_settings` where `key_name` = 'nexmo' and `settings_type` = 'sms_config' limit 1", "type": "query", "params": [], "bindings": ["nexmo", "sms_config"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Lib/Helper.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Lib\\Helper.php", "line": 198}, {"index": 15, "namespace": null, "name": "app/CentralLogics/sms_module.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\sms_module.php", "line": 21}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController.php", "line": 97}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.657192, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Helper.php:198", "source": "app/Lib/Helper.php:198", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FLib%2FHelper.php&line=198", "ajax": false, "filename": "Helper.php", "line": "198"}, "connection": "wallet_db", "start_percent": 97.507, "width_percent": 0.958}, {"sql": "select * from `addon_settings` where `key_name` = '2factor' and `settings_type` = 'sms_config' limit 1", "type": "query", "params": [], "bindings": ["2factor", "sms_config"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Lib/Helper.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Lib\\Helper.php", "line": 198}, {"index": 15, "namespace": null, "name": "app/CentralLogics/sms_module.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\sms_module.php", "line": 26}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController.php", "line": 97}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.661743, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Helper.php:198", "source": "app/Lib/Helper.php:198", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FLib%2FHelper.php&line=198", "ajax": false, "filename": "Helper.php", "line": "198"}, "connection": "wallet_db", "start_percent": 98.465, "width_percent": 0.712}, {"sql": "select * from `addon_settings` where `key_name` = 'msg91' and `settings_type` = 'sms_config' limit 1", "type": "query", "params": [], "bindings": ["msg91", "sms_config"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Lib/Helper.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Lib\\Helper.php", "line": 198}, {"index": 15, "namespace": null, "name": "app/CentralLogics/sms_module.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\sms_module.php", "line": 31}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/Customer/Auth/CustomerAuthController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Api\\V1\\Customer\\Auth\\CustomerAuthController.php", "line": 97}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.666538, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Helper.php:198", "source": "app/Lib/Helper.php:198", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FLib%2FHelper.php&line=198", "ajax": false, "filename": "Helper.php", "line": "198"}, "connection": "wallet_db", "start_percent": 99.177, "width_percent": 0.823}]}, "models": {"data": {"App\\Models\\BusinessSetting": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/v1/customer/auth/check-phone", "status_code": "<pre class=sf-dump id=sf-dump-1967030613 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1967030613\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-598929863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-598929863\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1461601378 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">+*************</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1461601378\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1807896955 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Dart/2.17 (dart:io)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expect</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">100-continue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">Keep-Alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1807896955\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1760266594 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1760266594\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-387235798 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:44:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387235798\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2093872961 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2093872961\", {\"maxDepth\":0})</script>\n"}}