{"__meta": {"id": "X1474c7469a482e81b9ce90cb7eb42f1f", "datetime": "2025-07-07 14:27:36", "utime": 1751876856.784039, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876856.172845, "end": 1751876856.784068, "duration": 0.6112232208251953, "duration_str": "611ms", "measures": [{"label": "Booting", "start": 1751876856.172845, "relative_start": 0, "end": 1751876856.579019, "relative_end": 1751876856.579019, "duration": 0.4061741828918457, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876856.579033, "relative_start": 0.4061880111694336, "end": 1751876856.784071, "relative_end": 2.86102294921875e-06, "duration": 0.20503807067871094, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002232, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876856.642543, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876856.760587, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-999826573 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-999826573\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-368408578 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-368408578\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-239797789 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-239797789\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1034626574 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ims2MEJkWTgxcFF1djYrTlJXV0xnRUE9PSIsInZhbHVlIjoiWDVNSmhCNloxdWVDa1Q5K21iNDhaVUgyMTBUaEg5TXM5NVhpcW53Q0lnQjlnMGVGdGZ3aXJRSEQ0MDMxRko4MG00MUlFWmwxTVJITlVramxWdllEMFN2amRpNVdpajFUOTNFdzZpcjg1cWZlRUVPbDNnN0pNQm54VjlIam5NQ0kiLCJtYWMiOiIzZTRmN2IyNTkwZTlkYTY2ZTY1M2I2MGMxZWY0NDQ4MzBkZjJmYjZhMzhkNjdiNDZlZDc1MGZmOTM4YjQ5MDk3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InNkdTh2MHowbE5XampQbGNRcnZ0Qmc9PSIsInZhbHVlIjoidWxZL1pqZnJxZ04xLzkzbGpJNWpRd0lnOHZpOTk2ZTB4eC9NQkZwOVFnd0d6RFpzaXBVMXAzUi9MT0hNaXlBczI0K2YvR1l3MUNmc3JPZWo5cGE0UjhrdTR1T292V1Rwa09CbENqeDVWWVo2SHNBSUxhOFBlOUJTWDhkSEo4cU8iLCJtYWMiOiIyYjIxZmFiN2VkODFkODgwYTNhNTFiZmM3YWNmZGRhN2M1NzRhZDcwNmZhMWI0OWM5MTBkZWE5MTgzOTQ0Y2FkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034626574\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-253523480 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-253523480\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-638540419 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlVVT0pBYjROWWJGV1NsUStvcm9tM2c9PSIsInZhbHVlIjoiU0FHWC9QU0JNTjdHd1BWY21MbzQraS9oN21NNFo1TTBTSXBrZnpKd0I4MDYvY0VFSGhVK2lHNU5kQ0RQREkvYjZHV2UzN1YvdzdWZ00rdEcxU0p6Q01iTnVZd0RCWko1Q3dlckkyaUVySEU2KzFTV282dnNWVXc2dXFsREVHUDMiLCJtYWMiOiI3OTc0OTIwYmJkNTVkYWI2ZDRkNDczMTY2ZWYwZDEwMGM1ZTk0NmNiYWJkMGYwOWZjZWRiODc5ODI3Y2IxYzZlIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImtoeUw2dHVYSlppK2xOcEs1ZW0rWFE9PSIsInZhbHVlIjoiZXBkYjlTU0VoYjA4SDNpcWNEVGZmZzliRFcvSjZkUFpoSXB4UmltWjR5RjU5U1JkdzVtSzZzSkhMTEdyOUVtc21FUlpTL20yWEM5S1Q0ZWFkc2ppWmZES1FZNDlhTnVjOE9Rc0ZHNVJCcFVPa0N4N3FhM25DNTNoNzVEelR0UlkiLCJtYWMiOiIwYjQ3ODY3NjZlZTExNTdmNjg5MjRjZGQ5N2U5NTgwZmJhODg5OWIxYTY0NTA0OGUwMzg2MWQ2NTEwYTBjZmI2IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlVVT0pBYjROWWJGV1NsUStvcm9tM2c9PSIsInZhbHVlIjoiU0FHWC9QU0JNTjdHd1BWY21MbzQraS9oN21NNFo1TTBTSXBrZnpKd0I4MDYvY0VFSGhVK2lHNU5kQ0RQREkvYjZHV2UzN1YvdzdWZ00rdEcxU0p6Q01iTnVZd0RCWko1Q3dlckkyaUVySEU2KzFTV282dnNWVXc2dXFsREVHUDMiLCJtYWMiOiI3OTc0OTIwYmJkNTVkYWI2ZDRkNDczMTY2ZWYwZDEwMGM1ZTk0NmNiYWJkMGYwOWZjZWRiODc5ODI3Y2IxYzZlIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImtoeUw2dHVYSlppK2xOcEs1ZW0rWFE9PSIsInZhbHVlIjoiZXBkYjlTU0VoYjA4SDNpcWNEVGZmZzliRFcvSjZkUFpoSXB4UmltWjR5RjU5U1JkdzVtSzZzSkhMTEdyOUVtc21FUlpTL20yWEM5S1Q0ZWFkc2ppWmZES1FZNDlhTnVjOE9Rc0ZHNVJCcFVPa0N4N3FhM25DNTNoNzVEelR0UlkiLCJtYWMiOiIwYjQ3ODY3NjZlZTExNTdmNjg5MjRjZGQ5N2U5NTgwZmJhODg5OWIxYTY0NTA0OGUwMzg2MWQ2NTEwYTBjZmI2IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-638540419\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1623980504 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623980504\", {\"maxDepth\":0})</script>\n"}}