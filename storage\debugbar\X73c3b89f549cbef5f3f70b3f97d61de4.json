{"__meta": {"id": "X73c3b89f549cbef5f3f70b3f97d61de4", "datetime": "2025-08-12 16:42:02", "utime": **********.293123, "method": "POST", "uri": "/admin/auth/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754995321.866701, "end": **********.293141, "duration": 0.4264400005340576, "duration_str": "426ms", "measures": [{"label": "Booting", "start": 1754995321.866701, "relative_start": 0, "end": **********.158062, "relative_end": **********.158062, "duration": 0.29136109352111816, "duration_str": "291ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.158075, "relative_start": 0.29137420654296875, "end": **********.293143, "relative_end": 2.1457672119140625e-06, "duration": 0.13506793975830078, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24901472, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST admin/auth/login", "middleware": "web, guest:user", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@submit", "as": "admin.auth.", "namespace": "App\\Http\\Controllers\\Admin\\Auth", "prefix": "admin/auth", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=62\" onclick=\"\">app/Http/Controllers/Admin/Auth/LoginController.php:62-100</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00428, "accumulated_duration_str": "4.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where (`key` = 'recaptcha') limit 1", "type": "query", "params": [], "bindings": ["recaptcha"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\CentralLogics\\helpers.php", "line": 244}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/Auth/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Admin\\Auth\\LoginController.php", "line": 69}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2003179, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "helpers.php:244", "source": "app/CentralLogics/helpers.php:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FCentralLogics%2Fhelpers.php&line=244", "ajax": false, "filename": "helpers.php", "line": "244"}, "connection": "wallet_db", "start_percent": 0, "width_percent": 14.252}, {"sql": "select * from `users` where `phone` = '**********' and `type` = 0 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["**********", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/Auth/LoginController.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\app\\Http\\Controllers\\Admin\\Auth\\LoginController.php", "line": 93}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\arefan_wallet_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.208249, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:139", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "wallet_db", "start_percent": 14.252, "width_percent": 85.748}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/auth/code/captcha/1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "default_captcha_code": "vkPu", "login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/auth/login", "status_code": "<pre class=sf-dump id=sf-dump-565713125 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-565713125\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1491643452 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1491643452\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1744341756 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>default_captcha_value</span>\" => \"<span class=sf-dump-str title=\"4 characters\">vkPu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1744341756\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-195073910 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">109</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkYzd3ByT0d6SGIwa240eXhvaStNWWc9PSIsInZhbHVlIjoiYkhSTFJnczJNZnNPQWN5UGNRRHBWRnZoV21DZXVid0FHTkdXZm1sTHFlcENtQWdNUDJzZ3djM2ZQQmtRYUdPcVJDdlZ1NXlpMHpaYjVXaTYyNkJrVjVacUszUUs0WittWkMzME4yaTNQY0NBYlFCYTJVM0FmR1ZhTjliSFRqMzIiLCJtYWMiOiIwMTQ4NzUyOTY2OWQzNjEzYjllYzJhMGIyMmZhNGUxN2IwMzA5MGQ5ZDVmYzk0MjRmYmI4ZTE4ZDg0NWM0MDg5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkFYSEpUOUcvMnJmcXJINVQrbDN0M0E9PSIsInZhbHVlIjoiWFp1OVY4am1aSENLS051K29ldGE5THg2OXM0UkN6bVdIdzBHK1BHbGVrTmFZalZ5YnZSczFNdzgrTDdJbkN3bnl1TnFuMUxERXVXckttWERRbnFCSnRWMlMxVitLQmUrY0xEY09MSUJ1TVdCdElwVGdLZjNkZURMRDVhVXo2R3oiLCJtYWMiOiI2YTliNDJmNzkwMjhjMmFhZjUzYjU2ZmQ4MjU5YWIyNDI1Yjg3M2U3OTRlNzFkMDM5MGM1MzkyOWVkOGZlMzI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195073910\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1532147077 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xXBna3nsIuOcOZy2mqUA7gFRJDRGHJs4xurDvGRe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532147077\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1484949332 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 10:42:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkF1bnJWNVVzRVBOUnlGdUpPUlI4ZGc9PSIsInZhbHVlIjoiWmNDOU12YnVLYmhQUkQrNyszKzJpVEJPdGdkaVJaUWdwRHgvTHZGbHRselhUOVY4S1pEdmRETEg5WWdkeTl6UHYwYzlHMTBiK1V2aTBIaEJUbUVQMWFrNFRCV2I0emFvVnY0SzNnNnNqMnczSGtCd3dULzdlZEttelNMUG9hNjciLCJtYWMiOiJhNTJlNTcxZTRhMmI1Njc2MGFkNzU3MGMwYzQ0NzM5NWQ5ZGZlMTMwNTYwNWJhNWY3OTU3OTJlNGJiNzMxYzJmIiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:42:02 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ik1Yb0lYdWZCTStSeFBkRllXa052ZUE9PSIsInZhbHVlIjoiam9BYVgrSUpncUxudUZzV2gzSFlWQWFXcUxSOWU5L0tjUDljcmZMNG1DWVRUTjFQVVlyWmVGYzVzWStOUnpmS0xtdGplVE5OdG5BVGUrTnMrYXpSdmN4VHlsZ3dYYmNKdHl2eUpPdWJCNzNJeWNhL2NtUVRvN2VzalhMNnBTbG0iLCJtYWMiOiIzZjBiOTNiZjAwMzFmNjFiNTVjYWRhMjUzYzlkYTgyM2IwNTVkMzgwMTYxZjkyNDFmYzExZDEzYTQ5YTZkNGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 12 Aug 2025 12:42:02 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkF1bnJWNVVzRVBOUnlGdUpPUlI4ZGc9PSIsInZhbHVlIjoiWmNDOU12YnVLYmhQUkQrNyszKzJpVEJPdGdkaVJaUWdwRHgvTHZGbHRselhUOVY4S1pEdmRETEg5WWdkeTl6UHYwYzlHMTBiK1V2aTBIaEJUbUVQMWFrNFRCV2I0emFvVnY0SzNnNnNqMnczSGtCd3dULzdlZEttelNMUG9hNjciLCJtYWMiOiJhNTJlNTcxZTRhMmI1Njc2MGFkNzU3MGMwYzQ0NzM5NWQ5ZGZlMTMwNTYwNWJhNWY3OTU3OTJlNGJiNzMxYzJmIiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:42:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ik1Yb0lYdWZCTStSeFBkRllXa052ZUE9PSIsInZhbHVlIjoiam9BYVgrSUpncUxudUZzV2gzSFlWQWFXcUxSOWU5L0tjUDljcmZMNG1DWVRUTjFQVVlyWmVGYzVzWStOUnpmS0xtdGplVE5OdG5BVGUrTnMrYXpSdmN4VHlsZ3dYYmNKdHl2eUpPdWJCNzNJeWNhL2NtUVRvN2VzalhMNnBTbG0iLCJtYWMiOiIzZjBiOTNiZjAwMzFmNjFiNTVjYWRhMjUzYzlkYTgyM2IwNTVkMzgwMTYxZjkyNDFmYzExZDEzYTQ5YTZkNGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 12-Aug-2025 12:42:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1484949332\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-116236280 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpAIdrdlCdEMTizlh2UHXGgmpWJF8crVBZ1h8oXl</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/admin/auth/code/captcha/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>default_captcha_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">vkPu</span>\"\n  \"<span class=sf-dump-key>login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-116236280\", {\"maxDepth\":0})</script>\n"}}