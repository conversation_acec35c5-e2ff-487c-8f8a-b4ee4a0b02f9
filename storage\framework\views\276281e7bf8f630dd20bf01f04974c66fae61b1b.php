<div class="card-header">
    <h5 class="card-header-title">
        <?php echo e(translate('Top Customers')); ?>

    </h5>
    <a href="<?php echo e(route('admin.customer.list')); ?>" class="fs-12px"><?php echo e(translate('View All')); ?></a>
</div>

<div class="card-body">
    <div class="grid-container">
        <?php $__currentLoopData = $top_customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=>$top_customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if(isset($top_customer->user)): ?>
                <a class="cursor-pointer" href="<?php echo e(route('admin.customer.view', [$top_customer['user_id']])); ?>">
                    <div class="dashboard--card h-100 p-4">
                        <div class="bg-primary text-white rounded-10 d-flex flex-column align-items-center mb-2 fs-10 px-3 py-2">
                            <span><?php echo e(translate('Total Transaction')); ?> :</span>
                            <span><?php echo e(Helpers::set_symbol($top_customer['total_transaction'])); ?></span>
                        </div>

                        <div class="avatar avatar-lg border border-width-2 rounded-circle mx-auto">
                            <img class="rounded-circle img-fit"
                                 src="<?php echo e($top_customer->user['image_fullpath']); ?>"
                                 alt="<?php echo e(translate('customer')); ?>">
                        </div>
                        <div class="text-center font-weight-bolder mt-2"><?php echo e($top_customer->user['f_name']??'Not exist'); ?></div>
                    </div>
                </a>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>
<?php /**PATH C:\laragon\www\arefan_wallet_admin\resources\views/admin-views/partials/_top-customer.blade.php ENDPATH**/ ?>