{"__meta": {"id": "Xfa661a693e94b9876f8af68b5d9b33d3", "datetime": "2025-07-07 14:27:46", "utime": 1751876866.300043, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876865.903624, "end": 1751876866.300063, "duration": 0.3964388370513916, "duration_str": "396ms", "measures": [{"label": "Booting", "start": 1751876865.903624, "relative_start": 0, "end": 1751876866.141256, "relative_end": 1751876866.141256, "duration": 0.23763203620910645, "duration_str": "238ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876866.141268, "relative_start": 0.23764395713806152, "end": 1751876866.300065, "relative_end": 2.1457672119140625e-06, "duration": 0.158797025680542, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002280, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876866.181286, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876866.282418, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1241749183 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1241749183\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-745255117 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-745255117\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1310402704 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1310402704\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1345676296 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"106 characters\">http://127.0.0.1:8000/step1?token=%242y%2410%24IfmQQouRPyMJDbXprWeAa.c2n8TGp%2FMKj%2FuZLk3wXwil%2FzPHKHP6S</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjBIZi9mZThjSnBMR2ZrRUgwR0ZOaEE9PSIsInZhbHVlIjoiMXptZzRDNGFucmk1d1FxREo2aXh5QTBPZzFiMnVZY0wxVUl2Tm0xckVvL2pRSzdWQ2d3ZVVPU3U2YndkanpxaXAra21idTJEK2hVYTh2Ynl0U05ITVVCbnB0WHQ2NWN1QnJrZ3IwRHdHVTd0QUR5UTRuemtESWRweWtvRzhHU1ciLCJtYWMiOiI1MmY3YWNkZGQ2Y2Y4NTlhODk4ODg1ZjI0YzBjZjIwMTI3MTM4ZWE3OTkyYTZjM2I5MTZmYTBkZTRhMmU2NTZkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik52MmVMdVFNak1qZS9XcENTbEcyQUE9PSIsInZhbHVlIjoiaXJ6TGN1WHRxM1NGQ0d4RUlJZXkrZWJRS0xUWlZhR3JTUEhPbVdNaU5ldE8yZG9zN1BlQWZlTWQ4NFZHSzIydXljdlp6V2piZFBxRUwrN0lFeHZmQlZLVHA1T0hUdGlZc2c3NDdVR09TNFhaR0RTN1RCblFVb2ZReE9GOXpuSGsiLCJtYWMiOiI3M2I5MzQ4ZDhiMjgwZTZiYzJiMjE2OGIzMWUxOWZiYjJiNzY1NjU1ZTc2NzcxODEzYzEwYjYxNjc3YThhN2ViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345676296\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1261376479 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261376479\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-506555534 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IldCU0V5bGFMTHU4WDlYQjFiemJ5MUE9PSIsInZhbHVlIjoiSElHRzh5emZlRWFPTnl0WElOUnZiTlh5UjNKU1FXL2RhSUZJakVDOW00MXZXWWtmcDUrU3JLSjNVa1RITm1PdHE5ZXNQV3NxWFJLVjdvN0NRT3J5MmFma2kyNkFvWStIU2RyQ3ZzSGQyYk1lMXR4VklzZEpNYytnTXUvbGRzUWwiLCJtYWMiOiI1OTQzMDY1ZDUxZDhhYzYxM2FiOTE1Yzc3MTYwNTcyZjM5YTU3ODM1NjkxZTNhNWQyYjA2MGNjZDAwZmY3OTYzIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:46 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InFJdkMvdlFreE5qK1RQeWJmMWxEUUE9PSIsInZhbHVlIjoiMGxScDNiSnFaQlNOejA5akd3T3Y1NHdzMkdjZTJiMjh0cW5oZVdoc1JlL21HOCtZWm5WUVFOYzlkejZSM3lnVlc4L0taa2pReDd0TWFJVmdNRUtSOW9ybXRwd05mSVFFOE1oWk9rSUZCelRMVmxTcnFJRWdvNzRCa0xGa3d5WnYiLCJtYWMiOiI3MjVjMGY0YjA0ZWI1MTMzZGY1ZTFiNzY5N2Y0ZWNiZTQ1MDAxMDMyNDk0ODhjYjg4NGE2ZmI1NzIyZGMyMDE3IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:46 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IldCU0V5bGFMTHU4WDlYQjFiemJ5MUE9PSIsInZhbHVlIjoiSElHRzh5emZlRWFPTnl0WElOUnZiTlh5UjNKU1FXL2RhSUZJakVDOW00MXZXWWtmcDUrU3JLSjNVa1RITm1PdHE5ZXNQV3NxWFJLVjdvN0NRT3J5MmFma2kyNkFvWStIU2RyQ3ZzSGQyYk1lMXR4VklzZEpNYytnTXUvbGRzUWwiLCJtYWMiOiI1OTQzMDY1ZDUxZDhhYzYxM2FiOTE1Yzc3MTYwNTcyZjM5YTU3ODM1NjkxZTNhNWQyYjA2MGNjZDAwZmY3OTYzIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InFJdkMvdlFreE5qK1RQeWJmMWxEUUE9PSIsInZhbHVlIjoiMGxScDNiSnFaQlNOejA5akd3T3Y1NHdzMkdjZTJiMjh0cW5oZVdoc1JlL21HOCtZWm5WUVFOYzlkejZSM3lnVlc4L0taa2pReDd0TWFJVmdNRUtSOW9ybXRwd05mSVFFOE1oWk9rSUZCelRMVmxTcnFJRWdvNzRCa0xGa3d5WnYiLCJtYWMiOiI3MjVjMGY0YjA0ZWI1MTMzZGY1ZTFiNzY5N2Y0ZWNiZTQ1MDAxMDMyNDk0ODhjYjg4NGE2ZmI1NzIyZGMyMDE3IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-506555534\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1038031964 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038031964\", {\"maxDepth\":0})</script>\n"}}