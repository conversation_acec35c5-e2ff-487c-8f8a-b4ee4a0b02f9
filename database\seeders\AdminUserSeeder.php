<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        User::create([
            'f_name' => 'Admin',
            'l_name' => 'User',
            'phone' => '1234567890',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'type' => 0, // Assuming 0 is for admin user type
        ]);
    }
}
