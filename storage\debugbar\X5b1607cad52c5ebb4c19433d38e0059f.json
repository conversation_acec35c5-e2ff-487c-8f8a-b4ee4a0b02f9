{"__meta": {"id": "X5b1607cad52c5ebb4c19433d38e0059f", "datetime": "2025-07-07 14:27:29", "utime": 1751876849.481561, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876849.102471, "end": 1751876849.48158, "duration": 0.37910890579223633, "duration_str": "379ms", "measures": [{"label": "Booting", "start": 1751876849.102471, "relative_start": 0, "end": 1751876849.34023, "relative_end": 1751876849.34023, "duration": 0.23775887489318848, "duration_str": "238ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876849.340242, "relative_start": 0.23777079582214355, "end": 1751876849.481582, "relative_end": 1.9073486328125e-06, "duration": 0.14134001731872559, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002056, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876849.37746, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876849.466535, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-161887370 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-161887370\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1558340802 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1558340802\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1060604977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1060604977\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1607694688 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlJYNDgrcG5vNDhXbHpKN1BNYjluOEE9PSIsInZhbHVlIjoiaXI4cWxmUUx5NkpjUys0RHd4VlpDUmV4d0g2V0crSFJNeksxbHlxQytpUWFzaXBHS3NlL1lveUlVSU9LS0RqS21ZSkRwQ2IvMkFJbXZwdlJJNmFlR3duRzY3S2JSZDd0c3pGbVMyMmxxcjJmWERSSUxmb1dDV3ZzQWJXRlQwSDYiLCJtYWMiOiI5ZDU0ZGE1ODUwMGM4ZWY4OTU2MDljOTI4NTE1OGI1ZTUwOGYyOTA5MTRmOWY5ZjhjNjM1N2ViNGJjZTlmZDdhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InQ5a0xXY0djMGJqU3Q2ZVhma2dGeGc9PSIsInZhbHVlIjoiWnRMS0FlWEFiVTJaMGpHOW5zeDEvWHM0dlJJTXZkVllrQ1N1ZlNrTjJsZWtteitOWWxhNTZJRkhDaTQ4SERTUlhvTjJGN0lrRmJ3Z3AxZC80L1BUam9aMWNtSzFIbENoK21hV1N6K1VITkwzVXFUV2UrMGlYTE8rNzlpS2g5S0YiLCJtYWMiOiJiYTE4ZGIyMTY2NWQyNzc0MTA1NzhmOWViNzhjNzI2NGUwNDY4NTU3OTU4Y2ZiYTBiMmM2YzhiMjJmOTY3NDE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607694688\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-451625977 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-451625977\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-551675180 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImxEQUl2OEsvaERQQ1BCQm9PSjFvYmc9PSIsInZhbHVlIjoiYUhOWmxueWg5clpNNzZrbDk1clJWTm9sSVk2emxKbGdJWERaVUYvcDAxaFozZFFFRzZ6VGwxcU82OTdBUnlVYWx2NnNPNUo3ZzliMGVrTG9RUjA2WW02MUxkU2xVbkQwWjByTWxMUGZQSHV3UTY3NEk5emtPYVAyNHh4Z1J4bU8iLCJtYWMiOiJkNDVjYWQ3ZWViMTdkYzZmNWVhOTY2MDMwZjBjYmY3OThkZTY5NWQ0M2Y0ZTdmMTI4NGExNWIxODUzNjE1MTQ0IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjRFREFGL1hnczdvcXltTHJwM1JaZWc9PSIsInZhbHVlIjoiRk16ZWJ5SEIrN25VdVJRRVZ4Y3JMQXhxK0Q0UWJ2dGhhVnNsNVJSalpJL2oyOFRhR1Q5a1VCMmhNNmttOEhkQWtSTXQrQU1mRkRlaG9adDM0MXBRaHZKUTNNNXdCclNjNlVVOFIvTUJJSTNoUjJSM0xSOFJlNjgzWFN0ckxyMSsiLCJtYWMiOiJmYjcwZjkzMWY0ZWQ5YjE1ZWJkMDlmNjM5Y2ZkYzQ5NjZiMmIyMWNlNjRkNzM0YjI3OGMwNzc0M2U3ODEzNWVmIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImxEQUl2OEsvaERQQ1BCQm9PSjFvYmc9PSIsInZhbHVlIjoiYUhOWmxueWg5clpNNzZrbDk1clJWTm9sSVk2emxKbGdJWERaVUYvcDAxaFozZFFFRzZ6VGwxcU82OTdBUnlVYWx2NnNPNUo3ZzliMGVrTG9RUjA2WW02MUxkU2xVbkQwWjByTWxMUGZQSHV3UTY3NEk5emtPYVAyNHh4Z1J4bU8iLCJtYWMiOiJkNDVjYWQ3ZWViMTdkYzZmNWVhOTY2MDMwZjBjYmY3OThkZTY5NWQ0M2Y0ZTdmMTI4NGExNWIxODUzNjE1MTQ0IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjRFREFGL1hnczdvcXltTHJwM1JaZWc9PSIsInZhbHVlIjoiRk16ZWJ5SEIrN25VdVJRRVZ4Y3JMQXhxK0Q0UWJ2dGhhVnNsNVJSalpJL2oyOFRhR1Q5a1VCMmhNNmttOEhkQWtSTXQrQU1mRkRlaG9adDM0MXBRaHZKUTNNNXdCclNjNlVVOFIvTUJJSTNoUjJSM0xSOFJlNjgzWFN0ckxyMSsiLCJtYWMiOiJmYjcwZjkzMWY0ZWQ5YjE1ZWJkMDlmNjM5Y2ZkYzQ5NjZiMmIyMWNlNjRkNzM0YjI3OGMwNzc0M2U3ODEzNWVmIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551675180\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2120201836 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120201836\", {\"maxDepth\":0})</script>\n"}}