{"__meta": {"id": "X8c3f205fa687bcdcafa0e08d43ad4faf", "datetime": "2025-07-07 14:27:26", "utime": 1751876846.89663, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876846.50885, "end": 1751876846.896666, "duration": 0.3878159523010254, "duration_str": "388ms", "measures": [{"label": "Booting", "start": 1751876846.50885, "relative_start": 0, "end": 1751876846.761704, "relative_end": 1751876846.761704, "duration": 0.2528538703918457, "duration_str": "253ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876846.761715, "relative_start": 0.2528648376464844, "end": 1751876846.89667, "relative_end": 4.0531158447265625e-06, "duration": 0.13495516777038574, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24001992, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876846.79325, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876846.884309, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-2046065660 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2046065660\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1874721620 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1874721620\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1298261484 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1298261484\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1945525662 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IndxMDNiUFQ3dWpwMXhIZjJxeUpoV0E9PSIsInZhbHVlIjoiRFF1MHBwa2RET0xnUyt5NW5RajFkRFhjTDhFUmxFNXpGVjlIeDhtTDVwQU9rWDlzdXc2L3lNdjFUK3luMnZHeTJtOGFCV25RYVVjRkFJMGc1RHFWTGVUM2h3Wnd1akdKS2l0cURsTmJmWEdCYnF3S2w3Nzh5NDRuWHlmeFI2bTEiLCJtYWMiOiIwY2I3ZTg4Y2U5MWNmZTgxMTM1NWM2MDY0NGUyZmM1NDM5OGI3ODMyN2UyYjAzOTM2ZjVlMTlhZjUwYTE0M2I0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IklmU1grNmRXODZTWVhaVU5ZT3dQVmc9PSIsInZhbHVlIjoiR1FSRXk5UW80aGZEcFY5aGh3QzhabElNQ1BJdUNIS1hUV2FNdUhFaFI3Q3hIbFVqOTBOS1RQZU5SSW9hbDQ5aXRCcXJiUk1WMWhGSU1mZ2VORXdTbGg5dUJ5RmRvZS9ZZlo1a1B4Y3BnTW9jN2gvK21kejBJVkxsaHVZNVk4VGQiLCJtYWMiOiJjZjM4ZWRhYjZkZWE4OTRhNzlmZGIzNDZmODFmNjc5MGU1NGM0ZTUzMGYxZDlmNjY1MWUyMTJkZWI3MTFmODZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1945525662\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1078375694 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078375694\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1685514529 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjBUU2FKcHVaaUFrSXVyYmpqdndtcnc9PSIsInZhbHVlIjoiRS9LWTFaUUl1ZWhoZHBwQ2tIbVczemE0VlVzd3Z1SkZyZlNFRXIwRXlQV0tLcmdFaWk5aDNEdHBSVktLOWYwK1ZmeVdmQmVEK0diWEtCbVF0QkJiNjdGY05oRmJuSnU2NDkrcks1ZDdRcitGNXpxYVdTWitQMzZ2U3p5MG5VSTEiLCJtYWMiOiIxMDU0MGNkMTIwNjJjZWRhYTRhZWYwY2Y2NWZmMGI2MGFhYmRkNDVlNGI0ZTQzY2M0MGVjMzQyNGE3NTEzYjU1IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjYxNjhqbm1jVUVQTW9abnhkQVhCN2c9PSIsInZhbHVlIjoid0VhSHZTVlltRWpHcnJiSUtSZTF1QnFsTnZTYmUzSWhod1o5OXFYRlQwK2l0dnN3VTBKMnpRSUU5Z0VJMXB0UDRDZGRrZm4va3JiZm9TRmJSR3JRRnovajFKR0Q4NWtJTWFjVGxkRFcyQ3ZVb2M3eFNncEVXSWxST25TeGd3YnQiLCJtYWMiOiJlMTU1YzVlOTBmMTA0MDZmYTdjMGYyZmU5MWI3N2M2NTFiZjU5OGRjZmExMGZkZjg4MzI1OGFjZTM2YWIyMDI1IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjBUU2FKcHVaaUFrSXVyYmpqdndtcnc9PSIsInZhbHVlIjoiRS9LWTFaUUl1ZWhoZHBwQ2tIbVczemE0VlVzd3Z1SkZyZlNFRXIwRXlQV0tLcmdFaWk5aDNEdHBSVktLOWYwK1ZmeVdmQmVEK0diWEtCbVF0QkJiNjdGY05oRmJuSnU2NDkrcks1ZDdRcitGNXpxYVdTWitQMzZ2U3p5MG5VSTEiLCJtYWMiOiIxMDU0MGNkMTIwNjJjZWRhYTRhZWYwY2Y2NWZmMGI2MGFhYmRkNDVlNGI0ZTQzY2M0MGVjMzQyNGE3NTEzYjU1IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjYxNjhqbm1jVUVQTW9abnhkQVhCN2c9PSIsInZhbHVlIjoid0VhSHZTVlltRWpHcnJiSUtSZTF1QnFsTnZTYmUzSWhod1o5OXFYRlQwK2l0dnN3VTBKMnpRSUU5Z0VJMXB0UDRDZGRrZm4va3JiZm9TRmJSR3JRRnovajFKR0Q4NWtJTWFjVGxkRFcyQ3ZVb2M3eFNncEVXSWxST25TeGd3YnQiLCJtYWMiOiJlMTU1YzVlOTBmMTA0MDZmYTdjMGYyZmU5MWI3N2M2NTFiZjU5OGRjZmExMGZkZjg4MzI1OGFjZTM2YWIyMDI1IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685514529\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-801737594 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-801737594\", {\"maxDepth\":0})</script>\n"}}