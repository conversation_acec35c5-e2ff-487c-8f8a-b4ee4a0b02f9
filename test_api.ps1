# Test Customer Registration API
$headers = @{
    'Content-Type' = 'application/json'
    'User-Agent' = 'Dart/2.17 (dart:io)'
}

# Step 1: Check Phone Availability and Send OTP
Write-Host "=== Step 1: Check Phone Availability ==="
$phoneCheckBody = @{
    'phone' = '+8801234567890'
} | ConvertTo-Json

try {
    $phoneCheckResponse = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/customer/auth/check-phone' -Method POST -Body $phoneCheckBody -Headers $headers
    Write-Host "Phone Check Success:"
    $phoneCheckResponse | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Phone Check Error:"
    Write-Host "Status Code:" $_.Exception.Response.StatusCode
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body:" $responseBody
    }
}

# Step 2: Get the actual OTP from database
Write-Host "`n=== Getting OTP from Database ==="
$otpQuery = "php artisan tinker --execute=`"echo DB::table('phone_verifications')->where('phone', '+8801234567890')->value('otp');`""
$actualOtp = Invoke-Expression $otpQuery
Write-Host "Actual OTP from database: $actualOtp"

# Step 3: Customer Registration with actual OTP
Write-Host "`n=== Step 3: Customer Registration ==="
$registrationBody = @{
    'f_name' = 'John'
    'l_name' = 'Doe'
    'phone' = '1234567890'
    'email' = '<EMAIL>'
    'password' = '1234'
    'gender' = 'male'
    'occupation' = 'Engineer'
    'dial_country_code' = '+880'
    'referral_code' = ''
    'otp' = $actualOtp.Trim()
} | ConvertTo-Json

Write-Host "Registration Request Body: $registrationBody"

try {
    $registrationResponse = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/customer/auth/register' -Method POST -Body $registrationBody -Headers $headers
    Write-Host "Registration Success:"
    $registrationResponse | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Registration Error:"
    Write-Host "Status Code:" $_.Exception.Response.StatusCode
    Write-Host "Status Description:" $_.Exception.Response.StatusDescription

    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body:" $responseBody
    }
}

# Step 4: Test Customer Login
Write-Host "`n=== Step 4: Customer Login ==="
$loginBody = @{
    'phone' = '1234567890'
    'dial_country_code' = '+880'
    'password' = '1234'
} | ConvertTo-Json

Write-Host "Login Request Body: $loginBody"

try {
    $loginResponse = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/customer/auth/login' -Method POST -Body $loginBody -Headers $headers
    Write-Host "Login Success:"
    $loginResponse | ConvertTo-Json -Depth 10

    # Store the token for further API calls
    $global:customerToken = $loginResponse.token
    Write-Host "Customer Token: $global:customerToken"

} catch {
    Write-Host "Login Error:"
    Write-Host "Status Code:" $_.Exception.Response.StatusCode
    Write-Host "Status Description:" $_.Exception.Response.StatusDescription

    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body:" $responseBody
    }
}

# Step 5: Test Authenticated Endpoints (if login was successful)
if ($global:customerToken) {
    Write-Host "`n=== Step 5: Testing Authenticated Endpoints ==="

    # Add Authorization header
    $authHeaders = @{
        'Content-Type' = 'application/json'
        'User-Agent' = 'Dart/2.17 (dart:io)'
        'Authorization' = "Bearer $global:customerToken"
    }

    # Test Get Customer Profile
    Write-Host "`n--- Get Customer Profile ---"
    try {
        $profileResponse = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/customer/get-customer' -Method GET -Headers $authHeaders
        Write-Host "Profile Success:"
        $profileResponse | ConvertTo-Json -Depth 10
    } catch {
        Write-Host "Profile Error:"
        Write-Host "Status Code:" $_.Exception.Response.StatusCode
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body:" $responseBody
        }
    }

    # Test Get Transaction History
    Write-Host "`n--- Get Transaction History ---"
    try {
        $transactionResponse = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/customer/transaction-history' -Method GET -Headers $authHeaders
        Write-Host "Transaction History Success:"
        $transactionResponse | ConvertTo-Json -Depth 10
    } catch {
        Write-Host "Transaction History Error:"
        Write-Host "Status Code:" $_.Exception.Response.StatusCode
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body:" $responseBody
        }
    }

    # Test Get Banners
    Write-Host "`n--- Get Customer Banners ---"
    try {
        $bannerResponse = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/customer/get-banner' -Method GET -Headers $authHeaders
        Write-Host "Banner Success:"
        $bannerResponse | ConvertTo-Json -Depth 10
    } catch {
        Write-Host "Banner Error:"
        Write-Host "Status Code:" $_.Exception.Response.StatusCode
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body:" $responseBody
        }
    }
}
