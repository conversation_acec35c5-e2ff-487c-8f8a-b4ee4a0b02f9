{"__meta": {"id": "X0094ee6986cb903592a781ad360702de", "datetime": "2025-07-07 14:27:28", "utime": 1751876848.44197, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876848.09735, "end": 1751876848.441988, "duration": 0.3446381092071533, "duration_str": "345ms", "measures": [{"label": "Booting", "start": 1751876848.09735, "relative_start": 0, "end": 1751876848.308591, "relative_end": 1751876848.308591, "duration": 0.2112410068511963, "duration_str": "211ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876848.308601, "relative_start": 0.21125102043151855, "end": 1751876848.441989, "relative_end": 9.5367431640625e-07, "duration": 0.13338804244995117, "duration_str": "133ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002184, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876848.340456, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876848.429553, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1255007648 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1255007648\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1873177251 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1873177251\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1653555393 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1653555393\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1419778988 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkFwdHV4OWtlaWV4M0hRd0JEMzhTOFE9PSIsInZhbHVlIjoiNXNsTnFzNytuMm5GVWtNWFZEZ29LUEZPdEIrdTNjRUNqOEwvU0NoUGZYc25uWW5qMC8vTS94K3Ird0sxZXorRjB1em1DTzJpN0NScEo3dzE1NlN5T0dZVzkrTXR4bXZ0akFxUFR1eGtqb0hlbFBWQnkrQXNvR1c1RE03YnFJN3UiLCJtYWMiOiJhZWJkNDNhOTkzOGI1YTdhNzAxZjVkNjJlMjFkYzE5ZGYwOWE0MWU1ZTEzY2UzMmZiYWI0NWRjMWQyMTIwZWJjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IklONWJwQWtPby9STmQ2aEZBUlF1L3c9PSIsInZhbHVlIjoiSEVGR2s3b3lpZ0JSUWVzQmxOeFJqYUVPbTlDQWtUSjh4cG1hUzFpOUFvUUZiTUxwNlBLQXd4THE2YjdqQjNlVGF2MHU3YkhVc3dzVW1Nd3hseWlsU2FKVmNrQUtHOEVxT1lMU2llOWI4azhxZmxTR1ZNalR4SFdLUDA0QXFCQ3QiLCJtYWMiOiI4Yjk1ODg2MjZkM2E3MWJlZjE4Mzc4ZTNhNWVlNjllMTFjYmEzY2NmMjllZTdjNTlmMTI0ZmY3ZDY1MjQyOGUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419778988\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1431694771 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431694771\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1806279057 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkhySElBem5RWWgrRFU1d0JsU2tFbkE9PSIsInZhbHVlIjoiUkNZVHN6eGlMVFhzS1dRZTVuTEYxK3lVZGhOT3dQNDFNYVU5NnhtN1lsSTRRelk5bkE3Q2pOS3FQMDQ3OWRxeERTZG0rN0NGU1RGVFFUdC95ZXU0YlNPT3V4UnFibTdxaU1MaENyUzlOS0diVjJ6YXJFUitrcTVhUDRyOER6dGIiLCJtYWMiOiJiZDI0NzY4N2NlNTViODdkZTE3MjE2NWI3NWJmZDFiMDc1YjA3OTZjZGY3MmYxYWZkZWZkZDFmNTQwNWVmYTk1IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im9VSkNXVlBTM1hrcFc4Q2Ixb3d0VFE9PSIsInZhbHVlIjoiV3Rsc2c2VTQ1YWNNK3RSOTZzdVMxbVBzb2lUNFB0THBjbnQwaGF6cjFVYVJxR1M5blRyTHBtUEZ4NERxSXhvSFFUR1FhdkVvOEdMTHQ3eHRtVm0zVWdHbWJ5L3lEdk52SFNKK1FPcHdnNGZkMTd6am05RzZYdDBBbk9XNW5hY1UiLCJtYWMiOiJmY2Q4NTc3YzU3YTFiZDRjMjVlNWVmZjZlYTEwN2IzMmU0MTY2YTdjMDIwNDZmZjBhMjMxZDRiODA5MDg0MzE0IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkhySElBem5RWWgrRFU1d0JsU2tFbkE9PSIsInZhbHVlIjoiUkNZVHN6eGlMVFhzS1dRZTVuTEYxK3lVZGhOT3dQNDFNYVU5NnhtN1lsSTRRelk5bkE3Q2pOS3FQMDQ3OWRxeERTZG0rN0NGU1RGVFFUdC95ZXU0YlNPT3V4UnFibTdxaU1MaENyUzlOS0diVjJ6YXJFUitrcTVhUDRyOER6dGIiLCJtYWMiOiJiZDI0NzY4N2NlNTViODdkZTE3MjE2NWI3NWJmZDFiMDc1YjA3OTZjZGY3MmYxYWZkZWZkZDFmNTQwNWVmYTk1IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im9VSkNXVlBTM1hrcFc4Q2Ixb3d0VFE9PSIsInZhbHVlIjoiV3Rsc2c2VTQ1YWNNK3RSOTZzdVMxbVBzb2lUNFB0THBjbnQwaGF6cjFVYVJxR1M5blRyTHBtUEZ4NERxSXhvSFFUR1FhdkVvOEdMTHQ3eHRtVm0zVWdHbWJ5L3lEdk52SFNKK1FPcHdnNGZkMTd6am05RzZYdDBBbk9XNW5hY1UiLCJtYWMiOiJmY2Q4NTc3YzU3YTFiZDRjMjVlNWVmZjZlYTEwN2IzMmU0MTY2YTdjMDIwNDZmZjBhMjMxZDRiODA5MDg0MzE0IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806279057\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-240104289 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-240104289\", {\"maxDepth\":0})</script>\n"}}