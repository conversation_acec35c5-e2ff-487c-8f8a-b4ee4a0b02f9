{"__meta": {"id": "Xe6bc0bd275a108f8bcd2cc1f7fd5490c", "datetime": "2025-07-07 14:27:33", "utime": 1751876853.502946, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751876853.139817, "end": 1751876853.502965, "duration": 0.3631479740142822, "duration_str": "363ms", "measures": [{"label": "Booting", "start": 1751876853.139817, "relative_start": 0, "end": 1751876853.366443, "relative_end": 1751876853.366443, "duration": 0.22662591934204102, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751876853.366453, "relative_start": 0.22663593292236328, "end": 1751876853.502967, "relative_end": 1.9073486328125e-06, "duration": 0.13651394844055176, "duration_str": "137ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24002088, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "installation.step0", "param_count": null, "params": [], "start": 1751876853.39928, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/installation/step0.blade.phpinstallation.step0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Finstallation%2Fstep0.blade.php&line=1", "ajax": false, "filename": "step0.blade.php", "line": "?"}}, {"name": "layouts.blank", "param_count": null, "params": [], "start": 1751876853.489681, "type": "blade", "hash": "bladeC:\\laragon\\www\\arefan_wallet_admin\\resources\\views/layouts/blank.blade.phplayouts.blank", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fresources%2Fviews%2Flayouts%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\InstallController@step0", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "step0", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Farefan_wallet_admin%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=23\" onclick=\"\">app/Http/Controllers/InstallController.php:23-26</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-597321692 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-597321692\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1221157470 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1221157470\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-404479327 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-404479327\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1137895467 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InMvdEZJbktoTFlubUJOd2xjbS9SeWc9PSIsInZhbHVlIjoieUVMeXFrajhDMDhDbWpWOW5MMGdNVTlqeklkNlY4NElOWWUxRTNzeTJuWHhJdk0xZFFvU05HNHgyeXJHZ1RvdUhaY3YyT0xWVXlua1RocjlISnR1U0JHc2V0SitPam0yOXpGQlkvbnF0UU9jYVZTRFp1UklhNzhjeVljdFlnSFQiLCJtYWMiOiJmOTg4Mjk2Njc1NTU0NDY3NGQ5MjQxYThhOGFmN2ZkOGJmZDNhNDdkMjdiOGZlZWQ4NmYxY2I1MmM1MTE1OTI5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5tYkJQN0ZlLzJiTk8xODNaY1Y1WXc9PSIsInZhbHVlIjoieFdhdmFCbUF6ZnRUclRtcDVNcTVBeVN1blZFZmZMNFVNYURMNlBvd2JRWVV4RUYxV0JkVko0OFJuZThEaFh4Qzg3bzJPNjJseklmamJWNklxclNqRS82R1drOStLOU9WalVmK2IwTlBXdW8zODR4M1R1THhvQ3lVWVZMTG5yU1IiLCJtYWMiOiI2MzU1OGYzZjJjYmIzMzRkNjBmZjIzOGI4MWYwOGIwN2MwYTRmOTUxZmQ3ZDY4MjdhNWU3OGVkNzk2ZmQ0YWU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137895467\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1956278374 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FNlRWoDHnhKWeetOPe7Y5sfwySC0iGGDXOL2G6TI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1956278374\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-207508716 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 08:27:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjNmdUI2bFFhR2tVQnhRaXZLOVJIVnc9PSIsInZhbHVlIjoiNDBjRXJTeUtKZ3hzUFg4aWN5em0xTnNBQmgyNmxQemEzbStjbXZxdHhGY3hXeHdHSitHdThBLytQSTh3RCs4Tmk1NENxcUZ3ZTZ3MzJ5djNIdDI3dDlRUWJZQjlVTVo2bW9xUWZuMWxBakJDY2VZNjVuNjNONW9OeWtLY2pqckciLCJtYWMiOiIzNDJiMTk5MDQ4MDgzYjVkY2VlOTE0ZmVmZTVjODBhYTNhYTg0MzgyMGM1YmYzODA4ZjZhOGIwNTY5MDNkMWY1IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:33 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im5NVHVONUQyWDQ1OHk0bkRRc0RCZ2c9PSIsInZhbHVlIjoibjk3T0ExeTVaWml6RVNZSEpqZjFQenRHdFo1eEg4VlA1VDlNZGxJcnlwK2d0TkZiQy95djdLbTk0Ym1TNEE5eXhVUHErWkY0YVlONmVoeiszVTYzenlwdUxGNjNIcDFqTTdhYUVza1Z4WXBuSFMyeTk0OUlwQkFsTzQ2WEVzOFAiLCJtYWMiOiI0NzNiYzRhZWYxNzk1NDFiNDkyYjYzOTk2Y2MwMmIyMmMxN2IyYmM4NzQ4ZmIyNjFiOGExZGRjNzFmZGI0MTVjIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 10:27:33 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjNmdUI2bFFhR2tVQnhRaXZLOVJIVnc9PSIsInZhbHVlIjoiNDBjRXJTeUtKZ3hzUFg4aWN5em0xTnNBQmgyNmxQemEzbStjbXZxdHhGY3hXeHdHSitHdThBLytQSTh3RCs4Tmk1NENxcUZ3ZTZ3MzJ5djNIdDI3dDlRUWJZQjlVTVo2bW9xUWZuMWxBakJDY2VZNjVuNjNONW9OeWtLY2pqckciLCJtYWMiOiIzNDJiMTk5MDQ4MDgzYjVkY2VlOTE0ZmVmZTVjODBhYTNhYTg0MzgyMGM1YmYzODA4ZjZhOGIwNTY5MDNkMWY1IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im5NVHVONUQyWDQ1OHk0bkRRc0RCZ2c9PSIsInZhbHVlIjoibjk3T0ExeTVaWml6RVNZSEpqZjFQenRHdFo1eEg4VlA1VDlNZGxJcnlwK2d0TkZiQy95djdLbTk0Ym1TNEE5eXhVUHErWkY0YVlONmVoeiszVTYzenlwdUxGNjNIcDFqTTdhYUVza1Z4WXBuSFMyeTk0OUlwQkFsTzQ2WEVzOFAiLCJtYWMiOiI0NzNiYzRhZWYxNzk1NDFiNDkyYjYzOTk2Y2MwMmIyMmMxN2IyYmM4NzQ4ZmIyNjFiOGExZGRjNzFmZGI0MTVjIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 10:27:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-207508716\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lRG5bZNp5mW4m94PFTihkWSz18LoLonhx5Izfha</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}