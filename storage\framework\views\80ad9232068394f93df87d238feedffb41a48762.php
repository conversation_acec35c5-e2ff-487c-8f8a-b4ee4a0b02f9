<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <title><?php echo $__env->yieldContent('title'); ?></title>
    <meta name="_token" content="<?php echo e(csrf_token()); ?>">

    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="keywords" content="">

    <link rel="shortcut icon"
          href="<?php echo e(asset('storage/app/public/favicon')); ?>/<?php echo e(Helpers::get_business_settings('favicon') ?? null); ?>"/>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600;700;800;900&display=swap"
          rel="stylesheet">

    <link rel="stylesheet" href="<?php echo e(asset('public/assets/landing/css/bootstrap.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('public/assets/landing/css/bootstrap-icons.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('public/assets/admin/vendor/icon-set/style.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('public/assets/landing/plugins/swiper/swiper-bundle.min.css')); ?>">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <link rel="stylesheet" href="<?php echo e(asset('public/assets/landing/css/style.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('public/assets/landing/css/custom.css')); ?>">

    <link rel="stylesheet" href="<?php echo e(asset('public/assets/admin')); ?>/css/toastr.css">

    <?php echo $__env->yieldPushContent('css_or_js'); ?>
</head>

<body>
<div class="preloader">
    <div class="spinner-grow" role="status">
        <span class="visually-hidden"><?php echo e(translate('Loading')); ?>...</span>
    </div>
</div>

<?php echo $__env->make('layouts.landing.partials._header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<main class="main-content d-flex flex-column">
    <?php echo $__env->yieldContent('content'); ?>
</main>

<section class="bg-white pt-5">
    <div class="newsletter-area" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="300">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="p-4 p-sm-5 rounded-10"
                         data-bg-img="<?php echo e(asset('public/assets/landing/img/media/newsletter-bg.png')); ?>">
                        <div
                            class="d-flex flex-column flex-md-row align-items-md-center justify-content-between gap-3 gap-md-5">
                            <div class="d-flex flex-column gap-3">
                                <h3 class="text-white"><?php echo e(translate('Subscribe Newsletter')); ?></h3>
                                <p class="text-white"><?php echo e(translate('get the latest')); ?> <?php echo e(App\Models\BusinessSetting::where('key', 'business_name')->value('value') ?? '6cash'); ?> <?php echo e(translate('offers delivered to your inbox')); ?></p>
                            </div>

                            <form action="<?php echo e(route('newsletter.subscribe')); ?>" method="POST"
                                  class="newsletter-form flex-grow-1 mx-w w-22-rem">
                                <?php echo csrf_field(); ?>
                                <div class="d-flex form-control px-1">
                                    <input type="email" name="email"
                                           class="border-0 px-2 text-dark bg-transparent w-100"
                                           placeholder="Enter your email">
                                    <button type="submit"
                                            class="btn btn-secondary rounded px-3"><?php echo e(translate('Submit')); ?></button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('layouts.landing.partials._footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</section>

<div class="progress-wrap">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
        <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
    </svg>
</div>

<aside class="aside d-flex flex-column d-xl-none">
    <div class="aside-overlay"></div>
    <div class="aside-header">
        <div class="d-flex pb-3 justify-content-between">
            <a href="index.html">
                <img width="100"
                     src="<?php echo e(asset('storage/app/public/business') . '/' . \App\Models\BusinessSetting::where(['key' => 'landing_page_logo'])->first()->value); ?>"
                     alt="">
            </a>
            <button class="aside-close-btn border-0 bg-transparent">
                <i class="bi bi-x-lg"></i>
            </button>
        </div>
    </div>
    <div class="aside-body custom-scrollbar">
        <ul class="main-nav nav">
            <li><a class="<?php echo e(Request::is('/') ? 'active' : ''); ?>"
                   href="<?php echo e(route('landing-page-home')); ?>"><?php echo e(translate('Home')); ?></a></li>
            <li><a class="<?php echo e(Request::is('pages/privacy-policy') ? 'active' : ''); ?>"
                   href="<?php echo e(route('pages.privacy-policy')); ?>"><?php echo e(translate('Privacy Policy')); ?></a></li>
            <li><a class="<?php echo e(Request::is('pages/terms-conditions') ? 'active' : ''); ?>"
                   href="<?php echo e(route('pages.terms-conditions')); ?>"><?php echo e(translate('Terms & Condition')); ?></a></li>
            <li><a class="<?php echo e(Request::is('pages/about-us') ? 'active' : ''); ?>"
                   href="<?php echo e(route('pages.about-us')); ?>"><?php echo e(translate('About Us')); ?></a></li>
            <li><a class="<?php echo e(Request::is('contact-us') ? 'active' : ''); ?>"
                   href="<?php echo e(route('contact-us')); ?>"><?php echo e(translate('Contact Us')); ?></a></li>
        </ul>
    </div>
</aside>

<script src="<?php echo e(asset('public/assets/landing/js/jquery-3.6.0.min.js')); ?>"></script>
<script src="<?php echo e(asset('public/assets/landing/js/bootstrap.bundle.min.js')); ?>"></script>
<script src="<?php echo e(asset('public/assets/landing/plugins/swiper/swiper-bundle.min.js')); ?>"></script>
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="<?php echo e(asset('public/assets/landing/js/main.js')); ?>"></script>

<script src="<?php echo e(asset('public/assets/admin')); ?>/js/vendor.min.js"></script>

<?php echo $__env->yieldPushContent('script_2'); ?>

<script>
    $(document).on('ready', function () {
        $('.js-toggle-password').each(function () {
            new HSTogglePassword(this).init()
        });

        $('.js-validate').each(function () {
            $.HSCore.components.HSValidation.init($(this));
        });
    });
</script>


<script src="<?php echo e(asset('public/assets/admin')); ?>/js/toastr.js"></script>
<?php echo Toastr::message(); ?>


<?php if($errors->any()): ?>
    <script>
        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        toastr.error('<?php echo e($error); ?>', Error, {
            CloseButton: true,
            ProgressBar: true
        });
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </script>
<?php endif; ?>
</body>
</html>
<?php /**PATH C:\laragon\www\arefan_wallet_admin\resources\views/layouts/landing/app.blade.php ENDPATH**/ ?>