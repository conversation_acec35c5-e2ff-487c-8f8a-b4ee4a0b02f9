<div id="headerMain" class="d-none">
    <header id="header"
            class="navbar navbar-expand-lg navbar-fixed navbar-height navbar-flush navbar-container navbar-bordered">
        <div class="navbar-nav-wrap">
            <div class="navbar-brand-wrapper">
            </div>

            <div class="navbar-nav-wrap-content-left d-xl-none">
                <button type="button" class="js-navbar-vertical-aside-toggle-invoker close mr-3">
                    <i class="tio-first-page navbar-vertical-aside-toggle-short-align" data-toggle="tooltip"
                       data-placement="right" title="Collapse"></i>
                    <i class="tio-last-page navbar-vertical-aside-toggle-full-align"
                       data-template='<div class="tooltip d-none d-sm-block" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
                       data-toggle="tooltip" data-placement="right" title="Expand"></i>
                </button>
            </div>

            <div class="navbar-nav-wrap-content-right">
                <ul class="navbar-nav align-items-center flex-row">

                    <li class="nav-item d-none d-sm-inline-flex align-items-center mr-5">
                        <div class="hs-unfold">
                            <?php ( $local = session()->has('local')?session('local'):'en'); ?>
                            <?php ($lang = \App\CentralLogics\Helpers::get_business_settings('language')??null); ?>
                            <div class="topbar-text dropdown disable-autohide text-capitalize">
                                <?php if(isset($lang)): ?>
                                    <a class="topbar-link dropdown-toggle d-flex align-items-center lang-country-flag text-dark" href="#" data-toggle="dropdown">
                                        <?php $__currentLoopData = $lang; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($data['code']==$local): ?>
                                                <img src="<?php echo e(asset('public/assets/admin/img/media/google_translate_logo1.png')); ?>" alt="<?php echo e(translate('image')); ?>">
                                                <span><?php echo e($data['name']); ?></span>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </a>
                                    <ul class="dropdown-menu">
                                        <?php $__currentLoopData = $lang; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key =>$data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($data['status']==1): ?>
                                                <li>
                                                    <a class="dropdown-item d-flex align-items-center gap-2" href="<?php echo e(route('admin.lang',[$data['code']])); ?>">
                                                        <span class="text-capitalize"><?php echo e(\App\CentralLogics\Helpers::get_language_name($data['code'])); ?></span>
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                <?php endif; ?>
                            </div>
                        </div>
                    </li>
                    <li class="nav-item">
                        <div class="hs-unfold">
                            <a class="js-hs-unfold-invoker navbar-dropdown-account-wrapper media align-items-center right-dropdown-icon" href="javascript:;"
                            data-hs-unfold-options='{
                                    "target": "#accountNavbarDropdown",
                                    "type": "css-animation"
                                }'>
                                <div class="media-body pl-0 pr-2">
                                    <span class="card-title h5 text-right"> <?php echo e(translate('admin panel')); ?> </span>
                                    <span class="card-text"><?php echo e(auth('user')->user()->f_name??''); ?> <?php echo e(auth('user')->user()->l_name??''); ?></span>
                                </div>
                                <div class="avatar avatar-sm avatar-circle">
                                    <img class="avatar-img"
                                        src="<?php echo e(auth('user')->user()->image_fullpath); ?>"
                                        alt="Image Description">
                                    <span class="avatar-status avatar-sm-status avatar-status-success"></span>
                                </div>
                            </a>

                            <div id="accountNavbarDropdown"
                                 class="hs-unfold-content dropdown-unfold dropdown-menu dropdown-menu-right navbar-dropdown-menu navbar-dropdown-account width-16rem">
                                <div class="dropdown-item-text">
                                    <div class="media align-items-center">
                                        <div class="avatar avatar-sm avatar-circle mr-2">
                                            <img class="avatar-img"
                                            src="<?php echo e(auth('user')->user()->image_fullpath); ?>"
                                                 alt="<?php echo e(translate('admin')); ?>">
                                        </div>
                                        <div class="media-body">
                                            <span class="card-title h5"><?php echo e(auth('user')->user()->f_name??''); ?></span>
                                            <span class="card-text"><?php echo e(auth('user')->user()->email??''); ?></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="dropdown-divider"></div>

                                <a class="dropdown-item" href="<?php echo e(route('admin.settings')); ?>">
                                    <span class="text-truncate pr-2" title="Settings"><?php echo e(translate('settings')); ?></span>
                                </a>

                                <div class="dropdown-divider"></div>

                                <a class="dropdown-item admin-logout-btn" href="javascript:">
                                    <span class="text-truncate pr-2" title="Sign out"><?php echo e(translate('sign_out')); ?></span>
                                </a>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </header>
</div>
<div id="headerFluid" class="d-none"></div>
<div id="headerDouble" class="d-none"></div>
<?php /**PATH C:\laragon\www\arefan_wallet_admin\resources\views/layouts/admin/partials/_header.blade.php ENDPATH**/ ?>