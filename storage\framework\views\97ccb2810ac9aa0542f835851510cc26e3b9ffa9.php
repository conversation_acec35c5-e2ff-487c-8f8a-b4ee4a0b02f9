<div class="footer">
    <div class="footer-main overflow-hidden">
        <img src="<?php echo e(asset('public/assets/landing/img/media/footer-bg.svg')); ?>" alt="" class="svg footer-bg">
        <div class="container">
            <div class="row gy-5">
                <div class="col-lg-4">
                    <div class="widget widget__about mb-4 text-center text-sm-start mx-auto mx-sm-0"
                         data-aos="fade-left" data-aos-duration="1000" data-aos-delay="300">
                        <img width="120" class="mb-3"
                             src="<?php echo e(asset('storage/app/public/business') . '/' . \App\Models\BusinessSetting::where(['key' => 'landing_page_logo'])->first()->value); ?>"
                             alt="">
                        <p><?php echo e(\App\CentralLogics\Helpers::get_business_settings('business_short_description')); ?></p>
                    </div>
                    <div class="widget widget__socials justify-content-center justify-content-sm-start"
                         data-aos="fade-left" data-aos-duration="1000" data-aos-delay="300">
                        <?php ($socialMedia = \App\Models\SocialMedia::where('status', 1)->get()); ?>
                        <?php if(isset($socialMedia)): ?>
                            <?php $__currentLoopData = $socialMedia; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $social): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <a href="<?php echo e($social->link); ?>" target="_blank"><i class="bi bi-<?php echo e($social->name); ?>"></i></a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-lg-8">
                    <div class="row gy-5">
                        <div class="col-sm-4">
                            <div class="widget widget__links text-center text-sm-start" data-aos="fade-left"
                                 data-aos-duration="1000" data-aos-delay="400">
                                <h4 class="widget__title"><?php echo e(translate('Quick Links')); ?></h4>

                                <ul class="d-flex flex-column gap-2">
                                    <li><a class="<?php echo e(Request::is('/') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('landing-page-home')); ?>"><?php echo e(translate('Home Page')); ?></a></li>
                                    <li><a class="<?php echo e(Request::is('pages/about-us') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('pages.about-us')); ?>"><?php echo e(translate('About Us')); ?></a></li>
                                    <li>
                                        <a href="<?php echo e(Request::is('/') ? '#how-it-works-section' : route('landing-page-home') . '#how-it-works-section'); ?>"><?php echo e(translate('How It Works')); ?></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="widget widget__links text-center text-sm-start" data-aos="fade-left"
                                 data-aos-duration="1000" data-aos-delay="500">
                                <h4 class="widget__title"><?php echo e(translate('Help & Support')); ?></h4>

                                <ul class="d-flex flex-column gap-2">
                                    <li><a class="<?php echo e(Request::is('contact-us') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('contact-us')); ?>"><?php echo e(translate('Contact Us')); ?></a></li>
                                    <li><a class="<?php echo e(Request::is('pages/privacy-policy') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('pages.privacy-policy')); ?>"><?php echo e(translate('Privacy Policy')); ?></a>
                                    </li>
                                    <li><a class="<?php echo e(Request::is('pages/terms-conditions') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('pages.terms-conditions')); ?>"><?php echo e(translate('Terms & Condition')); ?></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="widget widget__links text-center text-sm-start" data-aos="fade-left"
                                 data-aos-duration="1000" data-aos-delay="600">
                                <h4 class="widget__title"><?php echo e(translate('Get It Now')); ?></h4>

                                <div class="d-flex flex-column gap-3">
                                    <?php if($data['download_section']['data']['play_store_link'] != ""): ?>
                                        <a href="<?php echo e($data['download_section']['data']['play_store_link']); ?>"><img
                                                width="130"
                                                src="<?php echo e(asset('public/assets/landing/img/media/google_play_btn.png')); ?>"
                                                alt=""></a>
                                    <?php endif; ?>
                                    <?php if($data['download_section']['data']['app_store_link'] != ""): ?>
                                        <a href="<?php echo e($data['download_section']['data']['app_store_link']); ?>"><img
                                                width="130"
                                                src="<?php echo e(asset('public/assets/landing/img/media/app_store_btn.png')); ?>"
                                                alt=""></a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="footer-bottom text-center">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <p class="text-center text-md-left mb-2 mb-md-0">
                        &copy; <?php echo e(\App\CentralLogics\Helpers::get_business_settings('business_name')); ?>.
                        <span><?php echo e(\App\CentralLogics\Helpers::get_business_settings('footer_text')); ?></span>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\arefan_wallet_admin\resources\views/layouts/landing/partials/_footer.blade.php ENDPATH**/ ?>